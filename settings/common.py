from typing import Any, Dict, Optional, Tuple, Type

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
    TomlConfigSettingsSource,
)

from src.infra.utils import get_toml_dir


class LLMModel(BaseModel):
    provider: str = Field(description="The type of LLM")
    model: str = Field(description="The name of the LLM")
    temperature: float = Field(description="The temperature of the LLM", default=0.0)
    max_tokens: int = Field(description="The max tokens of the LLM", default=4096)
    external_args: Optional[Dict[str, Any]] = Field(
        default={}, description="The external arguments of the LLM"
    )
    extra_body: Optional[Dict[str, Any]] = Field(
        default={}, description="The extra body of the LLM"
    )


class CommonSettings(BaseSettings):
    llms: Dict[str, LLMModel] = Field(description="The LLM models used by the bot")

    model_config = SettingsConfigDict(
        extra="allow",
        env_file=".env",
        env_nested_delimiter="__",
        toml_file=get_toml_dir(),
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:

        return (
            init_settings,
            env_settings,
            dotenv_settings,
            file_secret_settings,
            TomlConfigSettingsSource(settings_cls),
        )
