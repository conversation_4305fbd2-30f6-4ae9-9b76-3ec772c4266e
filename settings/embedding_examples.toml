# Embedding 配置示例文件
# 支持多种 embedding 提供者的配置

# === Qwen3 Embedding 配置 ===
[embedding.qwen3]
provider = "qwen3"
model_name = "Qwen/Qwen3-Embedding-4B"
base_url = "http://ray.ttyuyin.com:10001/ray-qwen3-emb"
dimensions = 2560
timeout = 30
batch_size = 32

# === OpenAI Embedding 配置 ===
[embedding.openai]
provider = "openai"
model_name = "text-embedding-3-large"
api_key = "your-openai-api-key"
base_url = "https://api.openai.com/v1" # 可选，如果使用代理
dimensions = 3072                      # text-embedding-3-large 的维度
timeout = 30
batch_size = 32

# 额外参数示例
[embedding.openai.extra_params]
user = "your-user-id"

# === HuggingFace Embedding 配置 ===
[embedding.huggingface]
provider = "huggingface"
model_name = "sentence-transformers/all-MiniLM-L6-v2"
dimensions = 384
timeout = 30
batch_size = 32

# 额外参数示例
[embedding.huggingface.extra_params]
device = "cuda"
normalize_embeddings = true

# === 使用说明 ===
# 1. 在实际配置文件中选择一种配置方式，复制对应的配置段到主配置文件
# 2. 修改 provider 字段为对应的提供者名称
# 3. 根据实际情况修改其他参数
# 4. 对于需要 API Key 的提供者，请确保正确设置认证信息

# === 当前默认配置 (Qwen3) ===
[embedding]
provider = "qwen3"
model_name = "Qwen/Qwen3-Embedding-4B"
base_url = "http://ray.ttyuyin.com:10001/ray-qwen3-emb"
dimensions = 2560
timeout = 30
batch_size = 32
