from typing import Any, Dict, Literal, Optional

from pydantic import BaseModel, Field

from .common import CommonSettings


class APPSettings(BaseModel):
    host: str = Field(default="0.0.0.0", description="服务地址")
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field(
        default="DEBUG", description="日志级别"
    )
    thread_pool_size: int = Field(default=50, description="线程池大小")
    http_port: int = Field(default=8000, description="http port")
    http_openapi_url: str = Field(
        default="/openapi.json", description="http openapi url"
    )
    http_docs_url: str = Field(default="/docs", description="http docs url")
    terminal_type: Literal["console", "lark"] = Field(
        default="lark", description="终端类型"
    )

    class Config:
        env_prefix = "OPSBRAIN_"
        case_sensitive = True


class ChatBotSettings(BaseModel):
    shortterm_memory_expired: int = Field(
        default=600, description="短期记忆过期时间（秒）"
    )


class ConsoleSettings(BaseModel):
    chatbot_type: Literal["chat", "risk"] = Field(
        default="chat", description="只允许调试一种chatbot类型"
    )
    login_name: str = Field(default="none", description="控制台登录名")


class LarkClient(BaseModel):
    app_id: str = Field(..., description="应用ID")
    app_secret: str = Field(..., description="应用密钥")
    encrypt_key: str = Field(..., description="加密密钥")
    verification_token: str = Field(..., description="验证令牌")
    name: str = Field(..., description="应用名称")
    type: str = Field(..., description="应用类型")
    is_on: bool = Field(default=True, description="是否开启飞书机器人")


# 新增一个嵌套的配置模型
class LarkSettings(BaseModel):
    chat: LarkClient = Field(..., description="飞书聊天配置")
    risk: LarkClient = Field(..., description="飞书风险配置")


class RedisSettings(BaseModel):
    host: str = Field(..., description="redis host")
    port: int = Field(..., description="redis port")
    db: int = Field(..., description="redis db")
    password: Optional[str] = Field(default=None, description="redis password")
    max_conn: int = Field(default=50, description="redis max connections")
    conn_timeout: int = Field(default=15, description="redis connection timeout")


class QianxinSettings(BaseModel):
    base_url: str = Field(..., description="qianxin host")
    token: str = Field(..., description="qianxin token")


#  消息模板ID
class MessageTemplate(BaseModel):
    auth_tmp_id: str = Field(..., description="认证消息模板ID")
    interact_tmp_id: str = Field(..., description="响应消息模板ID")
    final_tmp_id: str = Field(..., description="最终消息模板ID")
    system_tmp_id: str = Field(..., description="系统消息模板ID")


class ModelSettings(BaseModel):
    default: str = Field(..., description="默认模型")
    talker: Optional[str] = Field(default=None, description="copilot模型")
    planner: Optional[str] = Field(default=None, description="规划模型")
    steps: Optional[str] = Field(default=None, description="执行模型")
    fix: Optional[str] = Field(default=None, description="修复模型")
    image: Optional[str] = Field(default=None, description="图片模型")
    summary: Optional[str] = Field(default=None, description="总结模型")
    task_memory: Optional[str] = Field(default=None, description="任务记忆模型")


class EmbeddingSettings(BaseModel):
    """Embedding 配置，支持多种embedding类型"""

    provider: str = Field(
        "qwen3", description="embedding提供者，支持: qwen3, openai, huggingface 等"
    )
    model_name: str = Field("Qwen/Qwen3-Embedding-4B", description="模型名称")
    base_url: str = Field(
        "http://ray.ttyuyin.com:10001/ray-qwen3-emb", description="服务地址"
    )
    api_key: Optional[str] = Field(None, description="API密钥（某些提供者需要）")
    dimensions: int = Field(2560, description="embedding维度")
    timeout: int = Field(30, description="请求超时时间")
    batch_size: int = Field(32, description="批处理大小")
    # 额外参数，不同provider可能需要不同的参数
    extra_params: Optional[Dict[str, Any]] = Field(None, description="额外参数")


class PostgresSettings(BaseModel):
    user: str = Field(..., description="数据库用户名")
    password: str = Field(..., description="数据库密码")
    host: str = Field(..., description="数据库主机")
    port: int = Field(5432, description="数据库端口")
    database: str = Field(..., description="数据库名")
    min_size: int = Field(1, description="连接池最小连接数")
    max_size: int = Field(10, description="连接池最大连接数")
    conn_timeout: int = Field(10, description="连接超时时间（秒）")
    pool_recycle: int = Field(3600, description="连接池回收时间（秒）")
    debug: bool = Field(False, description="是否开启调试模式")


class MilvusSettings(BaseModel):
    uri: str = Field(..., description="Milvus服务器主机地址")
    user: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    db_name: Optional[str] = Field(None, description="数据库名")
    timeout: int = Field(20, description="连接超时时间（秒）")
    token: Optional[str] = Field(None, description="token")


class ToolkitSettings(BaseModel):
    chat: dict = Field(..., description="toolkits settings")
    risk: dict = Field(..., description="toolkits settings")


class MCPDefaultSettings(BaseModel):
    sse_read_timeout: int = Field(120, description="SSE读取超时时间（秒）")
    connection_timeout: int = Field(30, description="连接超时时间（秒）")
    max_retries: int = Field(3, description="最大重试次数")
    retry_delay: float = Field(1.0, description="重试延迟时间（秒）")


class MCPSettings(BaseModel):
    # 保持原有的服务器配置结构，但添加默认设置
    servers: dict = Field(..., description="MCP服务器配置字典")
    defaults: MCPDefaultSettings = Field(
        default_factory=MCPDefaultSettings, description="默认配置"
    )


class Mem0Settings(BaseModel):
    enable: bool = Field(True, description="mem0 enable")
    collection_name: str = Field("jarvis_mem0", description="mem0 collection name")
    embedding_model_dims: int = Field(2560, description="mem0 embedding model dims")
    provider: str = Field("milvus", description="mem0 provider")
    metric_type: str = Field("COSINE", description="mem0 metric type")
    threshold: float = Field(0.1, description="mem0 threshold", ge=0.0, le=1.0)


class Settings(CommonSettings):
    app: APPSettings = Field(..., description="app settings")
    chatbot: ChatBotSettings = Field(..., description="chatbot settings")
    console: ConsoleSettings = Field(..., description="console settings")
    lark: LarkSettings = Field(..., description="飞书相关配置")
    redis: RedisSettings = Field(..., description="redis settings")
    qianxin: QianxinSettings = Field(..., description="qianxin settings")
    template: MessageTemplate = Field(..., description="message template")
    models: ModelSettings = Field(..., description="模型配置")
    postgres: PostgresSettings = Field(..., description="postgres settings")
    milvus: MilvusSettings = Field(..., description="milvus settings")
    mcp: MCPSettings = Field(..., description="MCP settings")
    toolkits: ToolkitSettings = Field(..., description="toolkits settings")
    embedding: EmbeddingSettings = Field(..., description="embedding settings")
    mem0: Mem0Settings = Field(..., description="mem0 settings")
