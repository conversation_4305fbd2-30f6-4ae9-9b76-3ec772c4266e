[app]
host = "0.0.0.0"
log_level = "DEBUG"
thread_pool_size = 50
http_port = 8888
http_openapi_url = "/openapi.json"
http_docs_url = "/docs"
terminal_type = "lark"

[chatbot]
shortterm_memory_expired = 900

[console]
chatbot_type = "risk"

[lark]
[lark.chat]
name = "牵星-容器云平台机器人"
type = "chat"
is_on = true

[lark.risk]
name = "Jarvis-风险治理"
type = "risk"
is_on = true


[template]
auth_tmp_id = "ctp_AAU5TN8OImLY"
interact_tmp_id = "AAqFrmOzww3Wf"
final_tmp_id = "AAqFr8mtXZSKw"
system_tmp_id = "AAqBfQxEgwHBM"

[redis]
host = "***********"
port = 6379
db = 0
max_conn = 50
conn_timeout = 15

[qianxin]
base_url = "https://cloud.ttyuyin.com"
token = "dHQtY2xvdWQtbGlzdGVuZXI=.cbab8901520a48b6b3df8980a28bc0196b7e6c2a0738ba060704e6a90fab33388580d35cd279a12e7c3512b2a8b8d798"

[postgres]
user = "checkpoint_rw"
host = "***********"
port = 5432
database = "langgraph_checkpoint_noprod"
min_size = 1
max_size = 10

[milvus]
uri = "https://in01-55c8dcbceba5eb8.tc-ap-beijing.vectordb.zilliz.com.cn:443"
user = "yw_ops"
db_name = "yw_ops_db"
timeout = 20

[models]
default = "qwen-max"
talker = "qwen-max"
fix = "gpt4o"
image = "gpt4o"
summary = "qwen-max"
planner = "local-qwen3-thinking"
steps = "local-qwen3-thinking"
task_memory = "local-qwen3-thinking"

[mcp.servers.server]
url = "http://************:8001/sse"
transport = "sse"

[mcp.defaults]
sse_read_timeout = 120  # 因为知识模块现在是一个比较综合的查询, 耗时比较久
connection_timeout = 30
max_retries = 3
retry_delay = 1.0

[toolkits]
[toolkits.chat]
kubernetes_toolkit = { name = "kubernetes", domain = "/kubernetes/" }
cicd_toolkit = { name = "cicd", domain = "/cicd/" }
resource_toolkit = { name = "resource", domain = "/resource/" }
knowledgeBase_toolkit = { name = "knowledgeBase", domain = "/knowledgeBase/" }
permissions_toolkit = { name = "permissions", domain = "/permissions/" }
faults_toolkit = { name = "faults", domain = "/faults/" }

[toolkits.risk]
risk_toolkit = { name = "risk", domain = "/risk/" }

[embedding]
provider = "qwen3"
model_name = "Qwen/Qwen3-Embedding-4B"
base_url = "http://ray.ttyuyin.com:10001/ray-qwen3-emb"
dimensions = 2560
timeout = 30
batch_size = 32


[mem0]
collection_name = "jarvis_mem0"
embedding_model_dims = 2560
provider = "milvus"
metric_type = "COSINE"
