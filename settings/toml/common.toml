[llms.gpt4]
provider = "azure_openai"
model = "gpt-4"
temperature = 0
external_args = { azure_endpoint = "https://east-us-quwan-yw-infra-01.openai.azure.com", azure_deployment = "gpt-4", api_version = "2024-02-15-preview" }

[llms.gpt4o]
provider = "azure_openai"
temperature = 0
model = "gpt-4o"
external_args = { azure_endpoint = "https://east-us-quwan-yw-infra-01.openai.azure.com", azure_deployment = "gpt-4o", api_version = "2024-02-15-preview" }

[llms.sonnet]
temperature = 0
model = "anthropic.claude-3-5-sonnet-20241022-v2:0"
provider = "bedrock"
external_args = { region = "us-west-2" }

[llms.sonnet3-7]
temperature = 0
model = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
provider = "bedrock"
external_args = { region = "us-west-2" }

[llms.qwen-max]
temperature = 0.01
model = "qwen-max-2025-01-25"
provider = "qwen"
external_args = { response_format = { type = "json_object" } }

[llms.qwen-deepseek-r1]
temperature = 0.01
model = "deepseek-r1"
provider = "qwen"

[llms.qwen-deepseek-v3]
temperature = 0.01
model = "deepseek-v3"
provider = "qwen"

[llms.deepseek]
temperature = 0
model = "deepseek-chat"
provider = "deepseek"

[llms.deepseek-reasoner]
temperature = 0
model = "deepseek-reasoner"
provider = "deepseek"

[llms.tc-deepseek-v3]
temperature = 0
model = "deepseek-v3-0324"
provider = "deepseek"
external_args = { api_base = "https://api.lkeap.cloud.tencent.com/v1" }

[llms.tc-deepseek-r1]
external_args = { api_base = "https://api.lkeap.cloud.tencent.com/v1" }
temperature = 0
model = "deepseek-r1"
provider = "deepseek"

[llms.local-r1]
external_args = { api_base = "https://scvv2h0g70epl9t0r6k5g.apigateway-cn-beijing.volceapi.com/mlp/s-20250415163308-qfkvd/v1" }
temperature = 0
model = "DeepSeek-R1"
provider = "deepseek"

[llms.local-qwen3-thinking]
temperature = 0.4
model = "Qwen/Qwen3-32B"
provider = "vllm_qwen3"
max_tokens = 10240
external_args = { base_url = "http://ray.ttyuyin.com:10001/vllm-qwen3-32b/v1", api_key = "sk-quwan-jarvis" }
extra_body = { "chat_template_kwargs" = { "enable_thinking" = true } }


[llms.local-qwen3-no-thinking]
temperature = 0.4
model = "Qwen/Qwen3-32B"
provider = "vllm_qwen3"
max_tokens = 5120
external_args = { base_url = "http://ray.ttyuyin.com:10001/vllm-qwen3-32b/v1", api_key = "sk-quwan-jarvis" }
extra_body = { "chat_template_kwargs" = { "enable_thinking" = false } }


[llms.local-qwenvl]
temperature = 0.4
model = "qwenvl"
provider = "openai"
external_args = { base_url = "http://ray.ttyuyin.com:10001/vllm-qwenvl-32b/v1", api_key = "sk-quwan-jarvis" }

# 原生qwen2.5lv32b+talker微调优化
[llms.local-qwenvl-talker]
temperature = 0.4
model = "qwenvl-talker"
provider = "openai"
external_args = { base_url = "http://ray.ttyuyin.com:10001/vllm-qwenvl-32b/v1", api_key = "sk-quwan-jarvis" }
