[project]
name = "ops-brain"
version = "2.0.0"
description = "智能运维机器人"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [
    "langgraph>=0.2.53",
    "langchain-ollama>=0.2.0",
    "pydantic-settings>=2.6.1",
    "fastapi>=0.115.5",
    "uvicorn>=0.32.1",
    "lark-oapi>=1.4.0",
    "lark>=1.1.5",
    "cel-python>=0.1.5",
    "legacy-cgi>=2.6.1",
    "qagent @ git+https://cicd_read:<EMAIL>/yunwei-infra/QAgent.git",
    "aiohttp>=3.11.9",
    "rocketry>=2.5.1",
    "langchain-openai>=0.3.6",
    "langchain-aws>=0.2.9",
    "tomli>=2.2.1",
    "langgraph-checkpoint-postgres>=2.0.13",
    "psycopg[binary,pool]>=3.2.4",
    "dashscope>=1.22.1",
    "langchain-community>=0.3.17",
    "langfuse>=2.60.8,<3.0.0",
    "loguru>=0.7.3",
    "langchain-deepseek>=0.1.2",
    "langchain-milvus>=0.1.8",
    "python-socks>=2.7.1",
    "mcp>=1.3.0",
    "langchain-mcp-adapters>=0.0.3",
    "redis>=5.2.1",
    "aioredis>=2.0.1",
    "sqlalchemy[asyncio]>=2.0.38",
    "psycopg-binary>=3.2.5",
    "alembic==1.13.1",
    "redbird>=0.7.1",
    "asyncpg>=0.30.0",
    "apscheduler>=3.11.0",
    "pillow>=11.1.0",
    "langchain-redis>=0.2.0",
    "prompt-toolkit>=3.0.50",
    "rich>=14.0.0",
    "jsonschema>=4.23.0",
    "openpyxl>=3.1.5",
    "mem0ai>=0.1.108",
    "border-secure-core>=0.2.3",
    "pyjwt>=2.10.1",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "ipykernel>=6.29.5",
    "pyright",
    "ruff",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[[tool.uv.index]]
name = "tencent"
url = "https://mirrors.aliyun.com/pypi/simple"
default = true

[tool.hatch.build.targets.wheel]
packages = ["src"]

# Pyright 配置
[tool.pyright]
pythonVersion = "3.11"
pythonPlatform = "Linux"
include = ["src"]
exclude = ["**/__pycache__", "build", "dist"]
venvPath = "."
venv = ".venv"
reportMissingImports = true
reportMissingTypeStubs = false
reportUnusedImport = true
reportUnusedVariable = true
reportOptionalMemberAccess = false
reportGeneralTypeIssues = true
reportUnnecessaryTypeIgnoreComment = true
typeCheckingMode = "basic"

# Ruff 配置
[tool.ruff]
line-length = 88
indent-width = 4
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

[tool.ruff.lint.isort]
known-first-party = ["src"]
