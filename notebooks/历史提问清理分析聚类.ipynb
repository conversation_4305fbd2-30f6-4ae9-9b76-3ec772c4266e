{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 数据清洗与标准化方案：为AI Agent评估构建高质量“黄金测试集”\n", "\n", "1. 核心目标与原则\n", "目标： 将原始、杂乱的历史用户提问，转化为一个干净、结构化、多样化且无隐私风险的评估数据集，为后续的Agent性能评估、回归测试和持续改进提供坚实的基础。\n", "\n", "原则：\n", "\n", "目的导向： 所有的清洗和标注工作都应服务于最终的评估目的——即有效衡量Agent在特定能力维度的表现。\n", "\n", "保留真实性： 在修正错误的同时，应尽力保留用户提问的真实意图和语言风格，避免过度“净化”而导致测试集失真。\n", "\n", "可追溯性： 对数据的每一个修改步骤都应有记录，方便回溯、审计和理解数据处理过程。\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /data/moyueheng_data/Projects/ops-brain\n"]}], "source": ["import os\n", "\n", "# 设置工作目录\n", "os.chdir('/data/moyueheng_data/Projects/ops-brain')\n", "print(f\"当前工作目录: {os.getcwd()}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2863, 6)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "v1_history_questions_path = 'notebooks/question_eval_result_embeddings_cluster.xlsx'\n", "\n", "v1_history_questions = pd.read_excel(v1_history_questions_path)\n", "v1_history_questions.shape\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二次聚类打分\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score_prompt = \"\"\"\n", "LLM Prompt: 查询质量评估与打分\n", "角色\n", "你是一位顶尖的AI Agent测试集架构师。你的任务是评估用户查询的质量，判断其是否适合作为一个有效的测试用例来评估一个AI Agent（一个需要推理、规划和使用工具的AI）。\n", "\n", "你的评估核心是**“评估价值”**，即一个查询在多大程度上能够揭示Agent的能力上限或缺陷。\n", "\n", "评估标准与打分体系（1-5分）\n", "请根据以下标准，给用户查询打出1到5分的分数。\n", "\n", "1分：完全无用 / 噪音\n", "\n", "定义： 查询是乱码、单个词、无意义的字符、纯粹的测试输入（如\"asdfg\"），或极其简单的问候（如“你好”）。\n", "\n", "处理建议： 直接丢弃。\n", "\n", "2分：低评估价值\n", "\n", "定义： 查询过于简单，通常是简单的、事实性的问题，不需要Agent进行任何复杂的推理或工具调用即可回答（例如，一个简单的搜索引擎就能解决）。或者意图极其模糊，几乎无法修复。\n", "\n", "示例： “法国的首都是哪里？”，“1+1等于几？”，“那个东西”。\n", "\n", "处理建议： 通常建议丢弃。\n", "\n", "3分：中等评估价值\n", "\n", "定义： 查询意图基本清晰，但可能缺少一些细节，或过于口语化。它可能需要Agent进行简单的单步工具调用或基础推理。这是一个合格的测试用例，但不够有挑战性。\n", "\n", "示例： “帮我查下天气”， “推荐个附近的餐厅”， “总结下这篇文章”。\n", "\n", "处理建议： 保留，可用于测试Agent的基础能力。\n", "\n", "4. 分：高评估价值\n", "\n", "定义： 查询清晰、具体，并且需要Agent执行多步操作、调用单个复杂工具或进行一定程度的推理才能完成。这类问题能有效测试Agent的核心能力。\n", "\n", "示例： “帮我预订明天下午从上海虹桥到北京南站的高铁二等座，要求下午3点后出发。”，“对比一下苹果和微软最近一个季度的财报，重点关注收入和利润增长率。”\n", "\n", "处理建议： 优质测试用例，必须保留。\n", "\n", "5分：极高评估价值 / 压力测试\n", "\n", "定义： 查询非常复杂，需要Agent进行复杂的规划、多个工具的协同调用、处理不确定性，或者包含潜在的“陷阱”（边缘案例）。这类问题是测试Agent鲁棒性和智能上限的绝佳材料。\n", "\n", "示例： “帮我规划一个为期五天的东京自由行，要求包含至少三个主要景点，覆盖文化和购物，预算控制在人均8000元以内，并生成一个每日行程表。”，“如果我的航班因为天气取消了，请帮我自动改签到下一班可用的航班，并同时通知我的接机司机新的到达时间。”\n", "\n", "处理建议： 黄金级测试用例，优先保留和分析。\n", "\n", "输出格式要求\n", "请严格按照以下JSON格式输出你的判断结果，不要添加任何额外的解释或对话。\n", "\n", "{\n", "  \"score\": <分数，一个1到5之间的整数>,\n", "  \"reasoning\": \"在此处简要说明你给出该分数的理由，请结合'清晰度'、'复杂度'和'评估价值'进行说明。\"\n", "}\n", "\n", "[请在此处输入你需要打分的用户查询]:\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.helper.utils import get_embedder_model\n", "\n", "embedder = get_embedder_model()\n", "\n", "\n", "from \n", "\n", "embedder.embed_documents(\"Hello, world!\")\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# 构建分类体系\n", "\n", "信息查询 (Information Query): 用户寻求特定事实或信息。\n", "\n", "示例： “苹果公司的最新股价是多少？”\n", "\n", "任务执行 (Task Execution): 用户希望Agent执行某个操作。\n", "\n", "示例： “帮我预订明天下午两点到虹桥机场的专车。”\n", "\n", "数据分析 (Data Analysis): 用户要求对数据进行处理、汇总或洞察。\n", "\n", "示例： “对比一下A产品和B产品上个季度的销售额，并生成图表。”\n", "\n", "闲聊/对话 (Chit-chat): 开放式的、非任务导向的对话。\n", "\n", "示例： “你觉得AI未来会怎么样？”\n", "\n", "模糊/不明确查询 (Ambiguous Query): 意图不清晰，需要Agent进一步澄清。\n", "\n", "示例： “我的订单有问题。”"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>question_length</th>\n", "      <th>score</th>\n", "      <th>category</th>\n", "      <th>reason</th>\n", "      <th>cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********* 这个是什么IP</td>\n", "      <td>18</td>\n", "      <td>5</td>\n", "      <td>基础运维问题</td>\n", "      <td>用户提出了一个关于IP地址的问题，但问题较为基础，没有涉及具体的运维场景或公司内部问题，因此...</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>集群名称: k8s-hw-bj-tt-web-activity\\n命名空间: common-...</td>\n", "      <td>80</td>\n", "      <td>8</td>\n", "      <td>运维/计算机相关</td>\n", "      <td>用户提问的问题是关于Kubernetes集群中特定命名空间下应用的副本数查询，属于典型的运维...</td>\n", "      <td>66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>************* 这个是什么IP</td>\n", "      <td>23</td>\n", "      <td>3</td>\n", "      <td>运维/计算机相关领域</td>\n", "      <td>用户的问题是一个关于IP地址的查询，属于运维或计算机相关领域。但问题较为简单，仅询问IP地址...</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1. 实例名称：`kafka-prod-hw-bj-yy-ad-01`\\n2. 消费组名称：...</td>\n", "      <td>111</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问属于运维相关领域，涉及Kafka实例、消费组和Topic的监控查询，与公司内部运维问...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>实例名称: kafka-prod-hw-bj-tt-auth-ev\\n消费组名称: anti...</td>\n", "      <td>105</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问属于运维相关领域，涉及Kafka消费者组的消费和堆积情况查询，问题清晰且与公司内部运...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2858</th>\n", "      <td>集群：k8s-tc-bj-1-yunwei  pod prometheus-istio-0 ...</td>\n", "      <td>51</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>问题涉及具体的运维场景，提到了Kubernetes集群（k8s-tc-bj-1-yunwei...</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2859</th>\n", "      <td>集群：k8s-tc-bj-1-yunwei  pod prometheus-istio-0 ...</td>\n", "      <td>56</td>\n", "      <td>9</td>\n", "      <td>运维相关</td>\n", "      <td>问题涉及具体的运维环境，包括 Kubernetes 集群名称、Pod 名称以及内存使用率查询...</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2860</th>\n", "      <td>骑士团相关的cmdb</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问中提到了与运维相关的CMDB（Configuration Management Da...</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2861</th>\n", "      <td>麻烦帮我看看 效能平台组有哪几个流水线模板</td>\n", "      <td>21</td>\n", "      <td>8</td>\n", "      <td>运维相关问题</td>\n", "      <td>问题明确涉及公司内部运维相关的具体信息查询，即关于'效能平台组'的流水线模板，符合与运维和计...</td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2862</th>\n", "      <td>黑神话悟空很火爆，公司内有没有人在关注</td>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td>非运维/计算机相关</td>\n", "      <td>该问题与运维、计算机相关领域无关，而是关于公司内部员工对黑神话悟空的关注情况，属于非技术性问...</td>\n", "      <td>59</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2863 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                               question  question_length  \\\n", "0                                     ********* 这个是什么IP               18   \n", "1     集群名称: k8s-hw-bj-tt-web-activity\\n命名空间: common-...               80   \n", "2                                 ************* 这个是什么IP               23   \n", "3     1. 实例名称：`kafka-prod-hw-bj-yy-ad-01`\\n2. 消费组名称：...              111   \n", "4     实例名称: kafka-prod-hw-bj-tt-auth-ev\\n消费组名称: anti...              105   \n", "...                                                 ...              ...   \n", "2858  集群：k8s-tc-bj-1-yun<PERSON>  pod prometheus-istio-0 ...               51   \n", "2859  集群：k8s-tc-bj-1-yun<PERSON>  pod prometheus-istio-0 ...               56   \n", "2860                                         骑士团相关的cmdb               10   \n", "2861                              麻烦帮我看看 效能平台组有哪几个流水线模板               21   \n", "2862                                黑神话悟空很火爆，公司内有没有人在关注               19   \n", "\n", "      score    category                                             reason  \\\n", "0         5      基础运维问题  用户提出了一个关于IP地址的问题，但问题较为基础，没有涉及具体的运维场景或公司内部问题，因此...   \n", "1         8    运维/计算机相关  用户提问的问题是关于Kubernetes集群中特定命名空间下应用的副本数查询，属于典型的运维...   \n", "2         3  运维/计算机相关领域  用户的问题是一个关于IP地址的查询，属于运维或计算机相关领域。但问题较为简单，仅询问IP地址...   \n", "3         8        运维相关  用户提问属于运维相关领域，涉及Kafka实例、消费组和Topic的监控查询，与公司内部运维问...   \n", "4         8        运维相关  用户提问属于运维相关领域，涉及Kafka消费者组的消费和堆积情况查询，问题清晰且与公司内部运...   \n", "...     ...         ...                                                ...   \n", "2858      8        运维相关  问题涉及具体的运维场景，提到了Kubernetes集群（k8s-tc-bj-1-yunwei...   \n", "2859      9        运维相关  问题涉及具体的运维环境，包括 Kubernetes 集群名称、Pod 名称以及内存使用率查询...   \n", "2860      8        运维相关  用户提问中提到了与运维相关的CMDB（Configuration Management Da...   \n", "2861      8      运维相关问题  问题明确涉及公司内部运维相关的具体信息查询，即关于'效能平台组'的流水线模板，符合与运维和计...   \n", "2862      3   非运维/计算机相关  该问题与运维、计算机相关领域无关，而是关于公司内部员工对黑神话悟空的关注情况，属于非技术性问...   \n", "\n", "      cluster  \n", "0          58  \n", "1          66  \n", "2          58  \n", "3          24  \n", "4          24  \n", "...       ...  \n", "2858       61  \n", "2859       61  \n", "2860       56  \n", "2861       94  \n", "2862       59  \n", "\n", "[2863 rows x 6 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["v1_history_questions"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 清理掉NaN的列\n", "\n", "v1_history_questions = v1_history_questions.dropna(axis=1)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>question_length</th>\n", "      <th>score</th>\n", "      <th>category</th>\n", "      <th>reason</th>\n", "      <th>cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1. 实例名称：`kafka-prod-hw-bj-yy-ad-01`\\n2. 消费组名称：...</td>\n", "      <td>111</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问属于运维相关领域，涉及Kafka实例、消费组和Topic的监控查询，与公司内部运维问...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>实例名称: kafka-prod-hw-bj-tt-auth-ev\\n消费组名称: anti...</td>\n", "      <td>105</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问属于运维相关领域，涉及Kafka消费者组的消费和堆积情况查询，问题清晰且与公司内部运...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>实例名称:kafka-prod-hw-bj-tt-gift-event-01\\n消费组名称:...</td>\n", "      <td>144</td>\n", "      <td>8</td>\n", "      <td>运维相关问题</td>\n", "      <td>用户提问的问题属于运维相关领域，涉及Kafka的消费者组监控和堆积数据查询，与公司内部运维问...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>实例名称:kafka-prod-hw-bj-tt-gift-event-01  \\n消费组名...</td>\n", "      <td>145</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问的问题与运维和计算机领域密切相关，涉及Kafka消费组的当前消费情况和堆积情况，属于...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>实例名称:kafka-prod-hw-bj-tt-ugc-following-update\\...</td>\n", "      <td>99</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提问与公司内部运维相关，具体涉及Kafka topic的监控图查询。问题描述清晰，包含实...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>查询监控：\\n实例名称:kafka-prod-hw-bj-tt-gift-event-01\\...</td>\n", "      <td>143</td>\n", "      <td>8</td>\n", "      <td>运维/计算机相关</td>\n", "      <td>用户的问题涉及具体的运维场景，包括Kafka的实例名称、消费组名称、Topic名称以及对消费...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>获取最近3小时消费堆积数量，消费速率\\n实例名称:kafka-prod-hw-bj-zt-m...</td>\n", "      <td>151</td>\n", "      <td>8</td>\n", "      <td>运维相关问题</td>\n", "      <td>用户的问题涉及运维领域，具体询问了Kafka的消费堆积数量和消费速率，包含实例名称、消费组名...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>获取最近3小时消费堆积数量，消费速率\\n实例名称:kafka-prod-hw-bj-zt-m...</td>\n", "      <td>139</td>\n", "      <td>8</td>\n", "      <td>运维相关问题</td>\n", "      <td>用户的问题与公司内部运维相关，具体涉及Kafka系统的消费堆积数量和消费速率查询，属于典型的...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>请你查询监控数据：\\n实例名称: kafka-prod-hw-bj-tt-auth-ev\\n...</td>\n", "      <td>108</td>\n", "      <td>9</td>\n", "      <td>运维相关</td>\n", "      <td>用户的问题明确涉及运维领域，具体询问了Kafka消费者组的消费情况和堆积情况，提供了实例名称...</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>请你查询监控数据：\\n实例名称: kafka-prod-hw-bj-tt-auth-ev\\n...</td>\n", "      <td>94</td>\n", "      <td>8</td>\n", "      <td>运维相关</td>\n", "      <td>用户提出了一个具体的运维相关问题，涉及监控数据查询，包括实例名称、消费组名称和 Topic ...</td>\n", "      <td>24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>75 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                             question  question_length  score  \\\n", "0   1. 实例名称：`kafka-prod-hw-bj-yy-ad-01`\\n2. 消费组名称：...              111      8   \n", "1   实例名称: kafka-prod-hw-bj-tt-auth-ev\\n消费组名称: anti...              105      8   \n", "2   实例名称:kafka-prod-hw-bj-tt-gift-event-01\\n消费组名称:...              144      8   \n", "3   实例名称:kafka-prod-hw-bj-tt-gift-event-01  \\n消费组名...              145      8   \n", "4   实例名称:kafka-prod-hw-bj-tt-ugc-following-update\\...               99      8   \n", "..                                                ...              ...    ...   \n", "70  查询监控：\\n实例名称:kafka-prod-hw-bj-tt-gift-event-01\\...              143      8   \n", "71  获取最近3小时消费堆积数量，消费速率\\n实例名称:kafka-prod-hw-bj-zt-m...              151      8   \n", "72  获取最近3小时消费堆积数量，消费速率\\n实例名称:kafka-prod-hw-bj-zt-m...              139      8   \n", "73  请你查询监控数据：\\n实例名称: kafka-prod-hw-bj-tt-auth-ev\\n...              108      9   \n", "74  请你查询监控数据：\\n实例名称: kafka-prod-hw-bj-tt-auth-ev\\n...               94      8   \n", "\n", "    category                                             reason  cluster  \n", "0       运维相关  用户提问属于运维相关领域，涉及Kafka实例、消费组和Topic的监控查询，与公司内部运维问...       24  \n", "1       运维相关  用户提问属于运维相关领域，涉及Kafka消费者组的消费和堆积情况查询，问题清晰且与公司内部运...       24  \n", "2     运维相关问题  用户提问的问题属于运维相关领域，涉及Kafka的消费者组监控和堆积数据查询，与公司内部运维问...       24  \n", "3       运维相关  用户提问的问题与运维和计算机领域密切相关，涉及Kafka消费组的当前消费情况和堆积情况，属于...       24  \n", "4       运维相关  用户提问与公司内部运维相关，具体涉及Kafka topic的监控图查询。问题描述清晰，包含实...       24  \n", "..       ...                                                ...      ...  \n", "70  运维/计算机相关  用户的问题涉及具体的运维场景，包括Kafka的实例名称、消费组名称、Topic名称以及对消费...       24  \n", "71    运维相关问题  用户的问题涉及运维领域，具体询问了Kafka的消费堆积数量和消费速率，包含实例名称、消费组名...       24  \n", "72    运维相关问题  用户的问题与公司内部运维相关，具体涉及Kafka系统的消费堆积数量和消费速率查询，属于典型的...       24  \n", "73      运维相关  用户的问题明确涉及运维领域，具体询问了Kafka消费者组的消费情况和堆积情况，提供了实例名称...       24  \n", "74      运维相关  用户提出了一个具体的运维相关问题，涉及监控数据查询，包括实例名称、消费组名称和 Topic ...       24  \n", "\n", "[75 rows x 6 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["v1_history_questions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}