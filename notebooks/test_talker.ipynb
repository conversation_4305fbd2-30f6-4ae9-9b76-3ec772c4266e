{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /data/moyueheng_data/Projects/ops-brain\n"]}], "source": ["import os\n", "\n", "# 设置工作目录\n", "os.chdir('/data/moyueheng_data/Projects/ops-brain')\n", "print(f\"当前工作目录: {os.getcwd()}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第0轮对话: human: 你好, ai: ```json\n", "{\n", "    \"message\": \"你好！有什么我可以帮你的吗？\"\n", "}\n", "```\n", "第1轮对话: human: 帮我写一个python代码, ai: ```json\n", "{\n", "    \"message\": \"当然可以！请告诉我你想要实现什么功能或需要解决的问题，我会为你编写相应的 Python 代码。\"\n", "}\n", "```\n", "第2轮对话: human: 写好了吗, ai: ```json\n", "{\n", "    \"message\": \"还在等待您的具体需求呢！请告诉我您需要实现什么功能或解决什么问题，我会根据您的要求为您编写 Python 代码。\"\n", "}\n", "```\n", "第3轮对话: human: 写好了, ai: ```json\n", "{\n", "    \"message\": \"看起来您可能已经完成了代码的编写！如果您仍有任何问题或需要帮助，请随时告诉我，我很乐意为您提供帮助。\"\n", "}\n", "```\n", "第4轮对话: human: 帮我部署到服务器上, ai: ```json\n", "{\n", "    \"message\": \"要将代码部署到服务器上，您需要提供一些具体信息，例如服务器的操作系统、是否需要安装依赖环境（如 Python 版本、库等），以及您希望通过哪种方式部署（如直接运行脚本、使用 Docker 等）。请提供更多细节，以便我为您提供准确的帮助。\"\n", "}\n", "```\n", "第5轮对话: human: 部署好了吗, ai: {\n", "    \"message\": \"我还在等待您提供关于部署的具体信息呢！请告诉我服务器的详情和部署方式，我会根据您的要求帮助您完成部署。\"\n", "}\n", "第6轮对话: human: 部署好了, ai: ```json\n", "{\n", "    \"message\": \"看来您已经成功完成了部署！如果还有其他问题或需要进一步的帮助，请随时告诉我，我很乐意为您提供支持。\"\n", "}\n", "```\n", "第7轮对话: human: 帮我测试一下, ai: ```json\n", "{\n", "    \"message\": \"当然可以！为了测试您的代码或服务，请告诉我需要测试的具体内容，例如功能点、接口请求或其他需求。我会根据您提供的信息协助完成测试。\"\n", "}\n", "```\n", "第8轮对话: human: 测试好了吗, ai: ```json\n", "{\n", "    \"message\": \"我还在等待您提供关于测试的具体信息呢！请告诉我需要测试的内容或功能，我会根据您的要求协助完成测试。\"\n", "}\n", "```\n", "第9轮对话: human: 测试好了, ai: ```json\n", "{\n", "    \"message\": \"看来您已经完成了测试！如果还有其他问题或需要进一步的帮助，请随时告诉我，我很乐意为您提供支持。\"\n", "}\n", "```\n", "第10轮对话: human: 帮我写一个文档, ai: ```json\n", "{\n", "    \"message\": \"好的！请告诉我您需要的文档类型和具体内容，例如功能说明、API 文档、使用指南等，我会根据您的要求为您编写文档。\"\n", "}\n", "```\n", "第11轮对话: human: 写好了吗, ai: 我还在等待您提供关于文档的具体信息呢！请告诉我需要编写文档的内容和类型，我会根据您的要求为您完成文档编写。\n"]}, {"ename": "OutputParserException", "evalue": "Invalid json output: 我还在等待您提供关于文档的具体信息呢！请告诉我需要编写文档的内容和类型，我会根据您的要求为您完成文档编写。\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/OUTPUT_PARSING_FAILURE ", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mJSONDecodeError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/output_parsers/json.py:86\u001b[39m, in \u001b[36mJsonOutputParser.parse_result\u001b[39m\u001b[34m(self, result, partial)\u001b[39m\n\u001b[32m     85\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m86\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mparse_json_markdown\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     87\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/utils/json.py:150\u001b[39m, in \u001b[36mparse_json_markdown\u001b[39m\u001b[34m(json_string, parser)\u001b[39m\n\u001b[32m    149\u001b[39m     json_str = json_string \u001b[38;5;28;01mif\u001b[39;00m match \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m match.group(\u001b[32m2\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_parse_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjson_str\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparser\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparser\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/utils/json.py:166\u001b[39m, in \u001b[36m_parse_json\u001b[39m\u001b[34m(json_str, parser)\u001b[39m\n\u001b[32m    165\u001b[39m \u001b[38;5;66;03m# Parse the JSON string into a Python dictionary\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m166\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mparser\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjson_str\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/utils/json.py:123\u001b[39m, in \u001b[36mparse_partial_json\u001b[39m\u001b[34m(s, strict)\u001b[39m\n\u001b[32m    120\u001b[39m \u001b[38;5;66;03m# If we got here, we ran out of characters to remove\u001b[39;00m\n\u001b[32m    121\u001b[39m \u001b[38;5;66;03m# and still couldn't parse the string as JSON, so return the parse error\u001b[39;00m\n\u001b[32m    122\u001b[39m \u001b[38;5;66;03m# for the original string.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m123\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mjson\u001b[49m\u001b[43m.\u001b[49m\u001b[43mloads\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstrict\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstrict\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/json/__init__.py:359\u001b[39m, in \u001b[36mloads\u001b[39m\u001b[34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[39m\n\u001b[32m    358\u001b[39m     kw[\u001b[33m'\u001b[39m\u001b[33mparse_constant\u001b[39m\u001b[33m'\u001b[39m] = parse_constant\n\u001b[32m--> \u001b[39m\u001b[32m359\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/json/decoder.py:337\u001b[39m, in \u001b[36mJSONDecoder.decode\u001b[39m\u001b[34m(self, s, _w)\u001b[39m\n\u001b[32m    333\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[32m    334\u001b[39m \u001b[33;03mcontaining a JSON document).\u001b[39;00m\n\u001b[32m    335\u001b[39m \n\u001b[32m    336\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m337\u001b[39m obj, end = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mraw_decode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43midx\u001b[49m\u001b[43m=\u001b[49m\u001b[43m_w\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    338\u001b[39m end = _w(s, end).end()\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/json/decoder.py:355\u001b[39m, in \u001b[36mJSONDecoder.raw_decode\u001b[39m\u001b[34m(self, s, idx)\u001b[39m\n\u001b[32m    354\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m--> \u001b[39m\u001b[32m355\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[33m\"\u001b[39m\u001b[33mExpecting value\u001b[39m\u001b[33m\"\u001b[39m, s, err.value) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    356\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m obj, end\n", "\u001b[31mJSONDecodeError\u001b[39m: Expecting value: line 1 column 1 (char 0)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mOutputParserException\u001b[39m                     Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 65\u001b[39m\n\u001b[32m     63\u001b[39m model = get_chat_model(\u001b[33m\"\u001b[39m\u001b[33mgpt4o\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     64\u001b[39m \u001b[38;5;66;03m# history = talker_agent(msgs)\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m65\u001b[39m history = \u001b[43mtalker_agent_new\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmsgs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     66\u001b[39m \u001b[38;5;28mprint\u001b[39m(history)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 57\u001b[39m, in \u001b[36mtalker_agent_new\u001b[39m\u001b[34m(msgs)\u001b[39m\n\u001b[32m     54\u001b[39m     response: BaseMessage = model.invoke([system_msg, human_msg])\n\u001b[32m     56\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m第\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m轮对话: human: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmsg\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, ai: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse.content\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m57\u001b[39m     msg = \u001b[43mparser\u001b[49m\u001b[43m.\u001b[49m\u001b[43mparse\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcontent\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     58\u001b[39m     history.append(AIMessage(content=msg[\u001b[33m\"\u001b[39m\u001b[33mmessage\u001b[39m\u001b[33m\"\u001b[39m]))\n\u001b[32m     59\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m history\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/output_parsers/json.py:100\u001b[39m, in \u001b[36mJsonOutputParser.parse\u001b[39m\u001b[34m(self, text)\u001b[39m\n\u001b[32m     91\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mparse\u001b[39m(\u001b[38;5;28mself\u001b[39m, text: \u001b[38;5;28mstr\u001b[39m) -> Any:\n\u001b[32m     92\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Parse the output of an LLM call to a JSON object.\u001b[39;00m\n\u001b[32m     93\u001b[39m \n\u001b[32m     94\u001b[39m \u001b[33;03m    Args:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     98\u001b[39m \u001b[33;03m        The parsed JSON object.\u001b[39;00m\n\u001b[32m     99\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m100\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mparse_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[43mGeneration\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/langchain_core/output_parsers/json.py:89\u001b[39m, in \u001b[36mJsonOutputParser.parse_result\u001b[39m\u001b[34m(self, result, partial)\u001b[39m\n\u001b[32m     87\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m     88\u001b[39m     msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mInvalid json output: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtext\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m89\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m OutputParserException(msg, llm_output=text) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n", "\u001b[31mOutputParserException\u001b[39m: Invalid json output: 我还在等待您提供关于文档的具体信息呢！请告诉我需要编写文档的内容和类型，我会根据您的要求为您完成文档编写。\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/OUTPUT_PARSING_FAILURE "]}], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.messages.base import BaseMessage\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from pydantic import BaseModel, Field\n", "\n", "from src.helper.utils import get_chat_model\n", "\n", "system_prompt = \"\"\"\n", "你是一个聊天机器人, 请你按照以下相应和我聊天\n", "```json\n", "{\n", "    \"message\": \"string, 你直接返回给用户的内容时或者当任务状态为完成时，请将任务结果返回给用户。\"\n", "}\n", "```\n", "\"\"\"\n", "\n", "system_msg = SystemMessage(content=system_prompt)\n", "\n", "msgs = [\"你好\", \"帮我写一个python代码\", \"写好了吗\", \"写好了\", \"帮我部署到服务器上\", \"部署好了吗\", \"部署好了\", \"帮我测试一下\", \"测试好了吗\", \"测试好了\", \"帮我写一个文档\", \"写好了吗\", \"写好了\", \"帮我部署到服务器上\", \"部署好了吗\", \"部署好了\", \"帮我测试一下\", \"测试好了吗\", \"测试好了\"]\n", "\n", "\n", "\n", "class Msg(BaseModel):\n", "    message: str = Field(description=\"你直接返回给用户的内容时或者当任务状态为完成时，请将任务结果返回给用户。\")\n", "\n", "\n", "def talker_agent(msgs):\n", "    history = [system_msg]\n", "    parser = JsonOutputParser(pydantic_object=Msg)\n", "    for i, msg in enumerate(msgs):\n", "        human_msg = HumanMessage(content=msg)\n", "        history.append(human_msg)\n", "        response: BaseMessage = model.invoke(history)\n", "        print(f\"第{i}轮对话: human: {msg}, ai: {response.content}\")\n", "        msg = parser.parse(response.content)\n", "        history.append(AIMessage(content=msg[\"message\"]))\n", "    return history\n", "\n", "\n", "def format_history(history: list[BaseMessage]) -> str:\n", "    history_str = \"\"\n", "    for msg in history[1:]:\n", "        history_str += f\"{msg.type}: {msg.content}\\n\"\n", "    return history_str\n", "\n", "def talker_agent_new(msgs):\n", "    history = [system_msg]\n", "    parser = JsonOutputParser(pydantic_object=Msg)\n", "    for i, msg in enumerate(msgs):\n", "        ori_humen_msg = HumanMessage(content=msg)\n", "        history.append(ori_humen_msg)\n", "        human_msg = HumanMessage(content=f\"历史消息: {format_history(history)}, 当前消息: {msg}\")\n", "        # 本质上只有单轮\n", "        response: BaseMessage = model.invoke([system_msg, human_msg])\n", "\n", "        print(f\"第{i}轮对话: human: {msg}, ai: {response.content}\")\n", "        msg = parser.parse(response.content)\n", "        history.append(AIMessage(content=msg[\"message\"]))\n", "    return history\n", "\n", "model = get_chat_model(\"gpt4o\")\n", "# history = talker_agent(msgs)\n", "history = talker_agent_new(msgs)\n", "print(history)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}