{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /data/moyueheng_data/Projects/ops-brain\n"]}], "source": ["import os\n", "\n", "# 设置工作目录\n", "os.chdir('/data/moyueheng_data/Projects/ops-brain')\n", "print(f\"当前工作目录: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# langchain tool call改造talker\n", "https://python.langchain.com/docs/how_to/function_calling/"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.tools import tool\n", "from pydantic import BaseModel, Field\n", "\n", "from src.core.schema.task import DeleteTask, TaskMeta\n", "from src.helper.utils import get_chat_model\n", "\n", "model = get_chat_model('planner')\n", "\n", "\n", "class TasksModel(BaseModel):\n", "    \"\"\"任务模型，用于验证和序列化任务列表\"\"\"\n", "\n", "    tasks: List[TaskMeta] = Field(description=\"任务列表\")\n", "\n", "\n", "@tool\n", "def update_task(tasks: list[TaskMeta]) -> list[TaskMeta]:\n", "    \"\"\"Update a task.\"\"\"\n", "    return \"任务更新成功\"\n", "\n", "\n", "@tool\n", "def delete_task(task: DeleteTask) -> list[TaskMeta]:\n", "    \"\"\"Delete a task.\"\"\"\n", "    return \"任务删除成功\"\n", "\n", "@tool\n", "def add_task(tasks: list[TaskMeta]) -> list[TaskMeta]:\n", "    \"\"\"添加多个任务.\"\"\"\n", "    return \"任务创建成功\"\n", "\n", "# 创建 加减乘除工具\n", "@tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Add a and b.\"\"\"\n", "    return a + b\n", "@tool\n", "def sub(a: int, b: int) -> int:\n", "    \"\"\"Sub a and b.\"\"\"\n", "    return a - b\n", "\n", "@tool\n", "def mul(a: int, b: int) -> int:\n", "    \"\"\"Mul a and b.\"\"\"\n", "    return a * b\n", "\n", "@tool\n", "def div(a: int, b: int) -> int:\n", "    \"\"\"Div a and b.\"\"\"\n", "    return a / b\n", "\n", "tools = [add_task, update_task, add, sub, mul, div, delete_task]\n", "\n", "tools_map = {\n", "    \"add_task\": add_task,\n", "    \"update_task\": update_task,\n", "    \"add\": add,\n", "    \"sub\": sub,\n", "    \"mul\": mul,\n", "    \"div\": div,\n", "    \"delete_task\": delete_task\n", "}\n", "\n", "llm_with_tools = model.bind_tools(tools)\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tools.invoke(\"随便删除多个任务\").tool_calls\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "inputs = [SystemMessage(content=\"你是一个运维助手，负责创建和更新任务\"), HumanMessage(content=\"随便创建多个任务\")]\n", "tool_calls = llm_with_tools.invoke(inputs).tool_calls\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"ename": "ValidationError", "evalue": "1 validation error for TasksModel\ntasks\n  Field required [type=missing, input_value={'task': [{'id': 1, 'name...系统安全更新'}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mValidationError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[39]\u001b[39m\u001b[32m, line 26\u001b[39m\n\u001b[32m     22\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"任务模型，用于验证和序列化任务列表\"\"\"\u001b[39;00m\n\u001b[32m     24\u001b[39m     tasks: List[TaskMeta] = Field(description=\u001b[33m\"\u001b[39m\u001b[33m任务列表\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m26\u001b[39m tasks = \u001b[43mTasksModel\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mtool_calls\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43margs\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     27\u001b[39m \u001b[38;5;28mprint\u001b[39m(tasks)\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/pydantic/main.py:253\u001b[39m, in \u001b[36mBaseModel.__init__\u001b[39m\u001b[34m(self, **data)\u001b[39m\n\u001b[32m    251\u001b[39m \u001b[38;5;66;03m# `__tracebackhide__` tells pytest and some other tools to omit this function from tracebacks\u001b[39;00m\n\u001b[32m    252\u001b[39m __tracebackhide__ = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m253\u001b[39m validated_self = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__pydantic_validator__\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mself_instance\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m validated_self:\n\u001b[32m    255\u001b[39m     warnings.warn(\n\u001b[32m    256\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mA custom validator is returning a value other than `self`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m'\u001b[39m\n\u001b[32m    257\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mReturning anything other than `self` from a top level model validator isn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt supported when validating via `__init__`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    258\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mSee the `model_validator` docs (https://docs.pydantic.dev/latest/concepts/validators/#model-validators) for more details.\u001b[39m\u001b[33m'\u001b[39m,\n\u001b[32m    259\u001b[39m         stacklevel=\u001b[32m2\u001b[39m,\n\u001b[32m    260\u001b[39m     )\n", "\u001b[31mValidationError\u001b[39m: 1 validation error for TasksModel\ntasks\n  Field required [type=missing, input_value={'task': [{'id': 1, 'name...系统安全更新'}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing"]}], "source": ["tool_calls[0]['args']\n", "\"\"\"\n", "{'task': [{'id': 1, 'name': '服务器监控', 'description': '监控服务器CPU和内存使用情况'},\n", "  {'id': 2, 'name': '日志备份', 'description': '每日凌晨备份系统日志文件'},\n", "  {'id': 3, 'name': '安全补丁更新', 'description': '安装最新的系统安全更新'}]}\n", "\"\"\"\n", "# tool_calls[0]['args'] 转成 TasksModel\n", "from typing import List\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "class TaskModel(BaseModel):\n", "    id: int\n", "    name: str \n", "    description: str\n", "\n", "# class TasksModel(BaseModel):\n", "#     task: List[TaskModel]\n", "\n", "class TasksModel(BaseModel):\n", "    \"\"\"任务模型，用于验证和序列化任务列表\"\"\"\n", "\n", "    tasks: List[TaskMeta] = Field(description=\"任务列表\")\n", "\n", "tasks = TasksModel(**tool_calls[0]['args'])\n", "print(tasks)\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "你是一个运维助手，负责创建和更新任务\n", "\n", "# Tools\n", "\n", "You may call one or more functions to assist with the user query.\n", "\n", "You are provided with function signatures within <tools></tools> XML tags:\n", "<tools>\n", "{\"type\": \"function\", \"function\": {\"name\": \"add_task\", \"description\": \"添加多个任务.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"update_task\", \"description\": \"Update a task.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"add\", \"description\": \"Add a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"sub\", \"description\": \"Sub a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"mul\", \"description\": \"Mul a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"div\", \"description\": \"Div a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"delete_task\", \"description\": \"Delete a task.\", \"parameters\": {\"properties\": {\"task\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"reason\": {\"description\": \"删除任务原因\", \"type\": \"string\"}}, \"required\": [\"id\", \"reason\"], \"type\": \"object\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "</tools>\n", "\n", "For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n", "<tool_call>\n", "{\"name\": <function-name>, \"arguments\": <args-json-object>}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "随便创建多个任务<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["llm_input = '<|im_start|>system\\n你是一个运维助手，负责创建和更新任务\\n\\n# Tools\\n\\nYou may call one or more functions to assist with the user query.\\n\\nYou are provided with function signatures within <tools></tools> XML tags:\\n<tools>\\n{\"type\": \"function\", \"function\": {\"name\": \"add_task\", \"description\": \"添加多个任务.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"update_task\", \"description\": \"Update a task.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"add\", \"description\": \"Add a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"sub\", \"description\": \"Sub a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"mul\", \"description\": \"Mul a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"div\", \"description\": \"Div a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"delete_task\", \"description\": \"Delete a task.\", \"parameters\": {\"properties\": {\"task\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"reason\": {\"description\": \"删除任务原因\", \"type\": \"string\"}}, \"required\": [\"id\", \"reason\"], \"type\": \"object\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n</tools>\\n\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\n<tool_call>\\n{\"name\": <function-name>, \"arguments\": <args-json-object>}\\n</tool_call><|im_end|>\\n<|im_start|>user\\n随便创建多个任务<|im_end|>\\n<|im_start|>assistant\\n'\n", "print(llm_input)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'delete_task',\n", "  'args': {'task': {'id': 1, 'reason': '用户要求删除'}},\n", "  'id': 'chatcmpl-tool-3ebbf92f9a3640e9aa5cfe42a26ef6a7',\n", "  'type': 'tool_call'}]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tools.invoke(\"删除多个任务\").tool_calls\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'add_task',\n", "  'args': {'task': [{'id': 1, 'name': '任务1', 'description': '描述1'},\n", "    {'id': 2, 'name': '任务2', 'description': '描述2'}]},\n", "  'id': 'chatcmpl-tool-aa16ca3bb5124553b69fd0280d8a2307',\n", "  'type': 'tool_call'}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tools.invoke(\"创建多个任务\").tool_calls\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["任务创建成功\n"]}], "source": ["tool_calls = llm_with_tools.invoke(\"更新创建多个任务\").tool_calls\n", "for tool_call in tool_calls:\n", "    result = tools_map[tool_call['name']].invoke(tool_call['args'])\n", "    print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 直接用langgraph的react agent"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["a = '<|im_start|>system\\n# Tools\\n\\nYou may call one or more functions to assist with the user query.\\n\\nYou are provided with function signatures within <tools></tools> XML tags:\\n<tools>\\n{\"type\": \"function\", \"function\": {\"name\": \"add_task\", \"description\": \"添加多个任务.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"update_task\", \"description\": \"Update a task.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"add\", \"description\": \"Add a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"sub\", \"description\": \"Sub a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"mul\", \"description\": \"Mul a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"div\", \"description\": \"Div a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\\n{\"type\": \"function\", \"function\": {\"name\": \"delete_task\", \"description\": \"Delete a task.\", \"parameters\": {\"properties\": {\"task\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"reason\": {\"description\": \"删除任务原因\", \"type\": \"string\"}}, \"required\": [\"id\", \"reason\"], \"type\": \"object\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\\n</tools>\\n\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\n<tool_call>\\n{\"name\": <function-name>, \"arguments\": <args-json-object>}\\n</tool_call><|im_end|>\\n<|im_start|>user\\n更新创建多个任务<|im_end|>\\n<|im_start|>assistant\\n'"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "# Tools\n", "\n", "You may call one or more functions to assist with the user query.\n", "\n", "You are provided with function signatures within <tools></tools> XML tags:\n", "<tools>\n", "{\"type\": \"function\", \"function\": {\"name\": \"add_task\", \"description\": \"添加多个任务.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"update_task\", \"description\": \"Update a task.\", \"parameters\": {\"properties\": {\"task\": {\"items\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"name\": {\"description\": \"任务名称\", \"type\": \"string\"}, \"description\": {\"description\": \"任务描述\", \"type\": \"string\"}}, \"required\": [\"id\", \"name\", \"description\"], \"type\": \"object\"}, \"type\": \"array\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"add\", \"description\": \"Add a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"sub\", \"description\": \"Sub a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"mul\", \"description\": \"Mul a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"div\", \"description\": \"Div a and b.\", \"parameters\": {\"properties\": {\"a\": {\"type\": \"integer\"}, \"b\": {\"type\": \"integer\"}}, \"required\": [\"a\", \"b\"], \"type\": \"object\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"delete_task\", \"description\": \"Delete a task.\", \"parameters\": {\"properties\": {\"task\": {\"properties\": {\"id\": {\"description\": \"任务id\", \"type\": \"integer\"}, \"reason\": {\"description\": \"删除任务原因\", \"type\": \"string\"}}, \"required\": [\"id\", \"reason\"], \"type\": \"object\"}}, \"required\": [\"task\"], \"type\": \"object\"}}}\n", "</tools>\n", "\n", "For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n", "<tool_call>\n", "{\"name\": <function-name>, \"arguments\": <args-json-object>}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "更新创建多个任务<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["print(a)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}