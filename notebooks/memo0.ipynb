{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.3 environment at: /data/moyueheng_data/Projects/ops-brain/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 5ms\u001b[0m\u001b[0m\n"]}], "source": ["! uv pip install mem0ai"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /data/moyueheng_data/Projects/ops-brain\n"]}], "source": ["import os\n", "\n", "# 设置工作目录\n", "os.chdir('/data/moyueheng_data/Projects/ops-brain')\n", "print(f\"当前工作目录: {os.getcwd()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n"]}, {"data": {"text/plain": ["{'results': [{'id': '113387ac-9638-4eb4-ae3a-def0f4c70bb4',\n", "   'memory': '不太喜欢惊悚片',\n", "   'event': 'ADD'},\n", "  {'id': '61bb67e0-76e3-4d9d-b6e0-8b29e7412306',\n", "   'memory': '很喜欢科幻电影',\n", "   'event': 'ADD'}]}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Optional\n", "\n", "from mem0 import Memory\n", "from mem0.configs.base import (\n", "    EmbedderConfig,\n", "    LlmConfig,\n", "    MemoryConfig,\n", "    MemoryItem,\n", "    VectorStoreConfig,\n", ")\n", "from mem0.utils.factory import EmbedderFactory\n", "\n", "# Set necessary environment variables for your chosen LangChain provider\n", "from src.helper.utils import get_chat_model, get_embedder_model\n", "\n", "# Initialize a Lang<PERSON>hain model directly\n", "llm = get_chat_model()\n", "embedder = get_embedder_model()\n", "# Pass the initialized model to the config\n", "\n", "\n", "llm_config = LlmConfig(provider=\"langchain\", config={\"model\": llm})\n", "\n", "embedder_config = EmbedderConfig(\n", "    provider=\"langchain\", config={\"model\": embedder, \"embedding_dims\": 2560}\n", ")\n", "\n", "# # 默认 qdrant\n", "# vector_store_config = VectorStoreConfig(\n", "#     config={\n", "#         \"embedding_model_dims\": 2560,\n", "#     }\n", "# )\n", "\n", "\n", "vector_store_config = VectorStoreConfig(\n", "    provider=\"milvus\",\n", "    config={\n", "        \"embedding_model_dims\": 2560,\n", "        \"collection_name\": \"jarvis_mem0_test_2\",\n", "        \"url\": \"https://in01-55c8dcbceba5eb8.tc-ap-beijing.vectordb.zilliz.com.cn:443\",\n", "        \"token\": \"65d282d733a1df8184c44ecd498670f0b1b9506badb2b35a78cddf27f373d0611cee3c9bb45cb11c19485262fc3fd705127056ce\",\n", "        \"metric_type\": \"COSINE\", # 默认走L2相似度, 也就是把距离作为score, 就会出现分数越小, 越相似\n", "    }\n", ")\n", "\n", "\n", "from src.core.cognition.memory.prompts import (\n", "    DEFAULT_UPDATE_MEMORY_PROMPT,\n", "    FACT_RETRIEVAL_PROMPT,\n", ")\n", "\n", "custom_fact_extraction_prompt = FACT_RETRIEVAL_PROMPT\n", "custom_update_memory_prompt = DEFAULT_UPDATE_MEMORY_PROMPT\n", "\n", "\n", "\n", "config = MemoryConfig(\n", "    llm=llm_config,\n", "    embedder=embedder_config,\n", "    vector_store=vector_store_config,\n", "    custom_fact_extraction_prompt=custom_fact_extraction_prompt,\n", "    custom_update_memory_prompt=custom_update_memory_prompt,\n", ")\n", "\n", "\n", "class MyMemory(Memory):\n", "    def __init__(self, config: MemoryConfig):\n", "        super().__init__(config)\n", "        # qwem3的query和document是分开的\n", "        self.query_embedding_model = EmbedderFactory.create(\n", "            \"langchain\",\n", "            {\"model\": get_embedder_model(is_query=True)},\n", "            self.config.vector_store.config,\n", "        )\n", "\n", "    def _search_vector_store(\n", "        self, query, filters, limit, threshold: Optional[float] = None\n", "    ):\n", "        embeddings = self.query_embedding_model.embed(query, \"search\")\n", "        memories = self.vector_store.search(\n", "            query=query, vectors=embeddings, limit=limit, filters=filters\n", "        )\n", "\n", "        promoted_payload_keys = [\n", "            \"user_id\",\n", "            \"agent_id\",\n", "            \"run_id\",\n", "            \"actor_id\",\n", "            \"role\",\n", "        ]\n", "\n", "        core_and_promoted_keys = {\n", "            \"data\",\n", "            \"hash\",\n", "            \"created_at\",\n", "            \"updated_at\",\n", "            \"id\",\n", "            *promoted_payload_keys,\n", "        }\n", "\n", "        original_memories = []\n", "        for mem in memories:\n", "            memory_item_dict = MemoryItem(\n", "                id=mem.id,\n", "                memory=mem.payload[\"data\"],\n", "                hash=mem.payload.get(\"hash\"),\n", "                created_at=mem.payload.get(\"created_at\"),\n", "                updated_at=mem.payload.get(\"updated_at\"),\n", "                score=mem.score,\n", "            ).model_dump()\n", "\n", "            for key in promoted_payload_keys:\n", "                if key in mem.payload:\n", "                    memory_item_dict[key] = mem.payload[key]\n", "\n", "            additional_metadata = {\n", "                k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys\n", "            }\n", "            if additional_metadata:\n", "                memory_item_dict[\"metadata\"] = additional_metadata\n", "\n", "            if threshold is None or mem.score >= threshold:\n", "                original_memories.append(memory_item_dict)\n", "\n", "        return original_memories\n", "\n", "\n", "m = MyMemory(config)\n", "\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"今晚我打算看电影。有什么推荐吗？\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"要不要看惊悚片？这类电影很吸引人。\",\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"我不太喜欢惊悚片，但我很喜欢科幻电影。\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"明白了！以后我会避免推荐惊悚片，而是为您推荐科幻电影。\",\n", "    },\n", "]\n", "m.add(messages, user_id=\"alice\",)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'results': [{'id': 'db11e8c4-35b9-4baf-a273-f61f434e630d',\n", "   'memory': '我叫莫岳恒',\n", "   'event': 'ADD'},\n", "  {'id': '08354990-e274-49f1-96a7-68a158500f00',\n", "   'memory': '是趣丸网络科技有限公司的员工',\n", "   'event': 'ADD'},\n", "  {'id': '55607c62-d624-410c-bd40-8c8fc7232864',\n", "   'memory': '是一个高级Python开发工程师',\n", "   'event': 'ADD'}]}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"我叫莫岳恒, 是趣丸网络科技有限公司的员工, 我是一个高级Python开发工程师\",\n", "    }\n", "]\n", "m.add(messages, user_id=\"alice\",)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'results': [{'id': '08354990-e274-49f1-96a7-68a158500f00',\n", "   'memory': '是趣丸网络科技有限公司的员工',\n", "   'hash': '4ba8be5145ecca3fb05388bb2791690b',\n", "   'metadata': None,\n", "   'created_at': '2025-06-16T01:06:28.479040-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '113387ac-9638-4eb4-ae3a-def0f4c70bb4',\n", "   'memory': '不太喜欢惊悚片',\n", "   'hash': '24b7e3eecd94df3586a924be17019c0c',\n", "   'metadata': None,\n", "   'created_at': '2025-06-16T01:06:18.654856-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '55607c62-d624-410c-bd40-8c8fc7232864',\n", "   'memory': '是一个高级Python开发工程师',\n", "   'hash': '6a5de0f5a8d976975aabcdbfc543f819',\n", "   'metadata': None,\n", "   'created_at': '2025-06-16T01:06:28.573166-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '61bb67e0-76e3-4d9d-b6e0-8b29e7412306',\n", "   'memory': '很喜欢科幻电影',\n", "   'hash': 'cfdf9859cc139a76ac458a1ac9564055',\n", "   'metadata': None,\n", "   'created_at': '2025-06-16T01:06:18.805800-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': 'db11e8c4-35b9-4baf-a273-f61f434e630d',\n", "   'memory': '我叫莫岳恒',\n", "   'hash': '649dc61e2d2881238aed9435a9e4e7c0',\n", "   'metadata': None,\n", "   'created_at': '2025-06-16T01:06:28.377316-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'}]}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# m.delete_all(user_id=\"alice\")\n", "\n", "m.get_all(user_id=\"alice\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'results': [{'id': '0bd74d45-b5b4-441b-99e0-2a0fc6d1ce83',\n", "   'memory': '不太喜欢惊悚片',\n", "   'hash': '24b7e3eecd94df3586a924be17019c0c',\n", "   'metadata': None,\n", "   'score': 0.38261179903612486,\n", "   'created_at': '2025-06-16T00:50:19.923565-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '2eab91d0-1ec8-43ef-8899-583e59d43d30',\n", "   'memory': '很喜欢科幻电影',\n", "   'hash': 'cfdf9859cc139a76ac458a1ac9564055',\n", "   'metadata': None,\n", "   'score': 0.3767644640420071,\n", "   'created_at': '2025-06-16T00:50:19.931577-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': 'f8e6b4bf-1fb7-4734-942a-02a5a748a138',\n", "   'memory': '我叫莫岳恒',\n", "   'hash': '649dc61e2d2881238aed9435a9e4e7c0',\n", "   'metadata': None,\n", "   'score': 0.27332554094670364,\n", "   'created_at': '2025-06-16T00:50:32.024347-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': 'cfc75755-ee0c-48f4-bd3b-cb74ae807c58',\n", "   'memory': '是趣丸网络科技有限公司的员工',\n", "   'hash': '4ba8be5145ecca3fb05388bb2791690b',\n", "   'metadata': None,\n", "   'score': 0.16584851032976528,\n", "   'created_at': '2025-06-16T00:50:32.037658-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '0c093340-9a88-4fb4-8b67-7af1d3d1e382',\n", "   'memory': '是一个高级Python开发工程师',\n", "   'hash': '6a5de0f5a8d976975aabcdbfc543f819',\n", "   'metadata': None,\n", "   'score': 0.13492280449788951,\n", "   'created_at': '2025-06-16T00:50:32.050402-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'}]}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["a = m.search(\"今晚我打算看电影。有什么推荐吗？\", user_id='alice')\n", "a"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'results': [{'id': 'db11e8c4-35b9-4baf-a273-f61f434e630d',\n", "   'memory': '我叫莫岳恒',\n", "   'hash': '649dc61e2d2881238aed9435a9e4e7c0',\n", "   'metadata': None,\n", "   'score': 0.38924360275268555,\n", "   'created_at': '2025-06-16T01:06:28.377316-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '08354990-e274-49f1-96a7-68a158500f00',\n", "   'memory': '是趣丸网络科技有限公司的员工',\n", "   'hash': '4ba8be5145ecca3fb05388bb2791690b',\n", "   'metadata': None,\n", "   'score': 0.36210042238235474,\n", "   'created_at': '2025-06-16T01:06:28.479040-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '55607c62-d624-410c-bd40-8c8fc7232864',\n", "   'memory': '是一个高级Python开发工程师',\n", "   'hash': '6a5de0f5a8d976975aabcdbfc543f819',\n", "   'metadata': None,\n", "   'score': 0.2758839428424835,\n", "   'created_at': '2025-06-16T01:06:28.573166-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '113387ac-9638-4eb4-ae3a-def0f4c70bb4',\n", "   'memory': '不太喜欢惊悚片',\n", "   'hash': '24b7e3eecd94df3586a924be17019c0c',\n", "   'metadata': None,\n", "   'score': 0.20491139590740204,\n", "   'created_at': '2025-06-16T01:06:18.654856-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'}]}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["a = m.search(\"我是谁\", user_id='alice', threshold=0.2)\n", "a\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'results': [{'id': '31d040f6-566a-47eb-b9b0-60f0a0e799d4',\n", "   'memory': '不太喜欢惊悚片',\n", "   'hash': '24b7e3eecd94df3586a924be17019c0c',\n", "   'metadata': None,\n", "   'score': 1.6433079242706299,\n", "   'created_at': '2025-06-16T00:35:39.071358-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'},\n", "  {'id': '9bfc8c3a-fd8a-4451-889a-4b1eeb74ab46',\n", "   'memory': '很喜欢科幻电影',\n", "   'hash': 'cfdf9859cc139a76ac458a1ac9564055',\n", "   'metadata': None,\n", "   'score': 1.6746537685394287,\n", "   'created_at': '2025-06-16T00:35:39.212335-07:00',\n", "   'updated_at': None,\n", "   'user_id': 'alice'}]}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["a = m.search(\"你记住了什么\", user_id='alice')\n", "a\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/data/moyueheng_data/Projects/ops-brain/.venv/lib/python3.12/site-packages/mem0/client/main.py:34: DeprecationWarning: output_format='v1.0' is deprecated therefore setting it to 'v1.1' by default.Check out the docs for more information: https://docs.mem0.ai/platform/quickstart#4-1-create-memories\n", "  return func(*args, **kwargs)\n"]}, {"data": {"text/plain": ["{'results': [{'id': 'ca14beb6-ede6-4cdb-bbf7-d7018cd63fdf',\n", "   'event': 'ADD',\n", "   'memory': 'User name is <PERSON>'},\n", "  {'id': '246aa23d-a9f5-4504-9798-a82714b2232d',\n", "   'event': 'ADD',\n", "   'memory': 'Is a vegetarian'},\n", "  {'id': '1364e3ea-a232-49ff-ac41-35c8d6489ead',\n", "   'event': 'ADD',\n", "   'memory': 'Is allergic to nuts'}]}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from mem0 import MemoryClient\n", "\n", "client = MemoryClient(api_key=\"m0-69uYX8e8QOsLuErtMFXmcVHXQVnTDwzX2ft4fSZA\")\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"<PERSON>, I'm <PERSON>. I'm a vegetarian and I'm allergic to nuts.\"},\n", "    {\"role\": \"assistant\", \"content\": \"Hello <PERSON>! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions.\"}\n", "]\n", "client.add(messages, user_id=\"alex\")\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': '246aa23d-a9f5-4504-9798-a82714b2232d',\n", "  'memory': 'Is a vegetarian',\n", "  'user_id': 'alex',\n", "  'actor_id': None,\n", "  'metadata': None,\n", "  'categories': ['user_preferences', 'food'],\n", "  'created_at': '2025-06-15T22:55:43.330111-07:00',\n", "  'updated_at': '2025-06-15T22:55:43.349922-07:00',\n", "  'expiration_date': None,\n", "  'structured_attributes': None,\n", "  'internal_metadata': None,\n", "  'deleted_at': None,\n", "  'score': 0.30164014110804493}]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"What can I cook for dinner tonight?\"\n", "client.search(query, user_id=\"alex\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}