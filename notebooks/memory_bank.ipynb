{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.3 environment at: /data/moyueheng_data/Projects/ops-brain/.venv\u001b[0m\n", "\u001b[2K\u001b[2mResolved \u001b[1m81 packages\u001b[0m \u001b[2min 5.34s\u001b[0m\u001b[0m                                        \u001b[0m\n", "\u001b[2K\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)                                                  \n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m     0 B/12.97 MiB       \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 8.00 KiB/12.97 MiB      \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 16.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 24.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 32.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 40.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 48.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 56.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 64.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 72.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 80.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 88.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 96.00 KiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 104.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 112.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 120.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 128.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 136.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 616.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 704.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 704.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 712.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 720.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 728.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 736.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 776.00 KiB/12.97 MiB    \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1000.00 KiB/12.97 MiB   \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1.11 MiB/12.97 MiB      \u001b[1A\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 640.00 KiB/4.26 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1.98 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 688.00 KiB/4.26 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1.98 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mcryptography\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 1.07 MiB/4.26 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1.98 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 1.45 MiB/4.26 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------------\u001b[0m\u001b[0m 1.98 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-auth-httplib2\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/9.04 KiB\n", "\u001b[2mzipp      \u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/11.22 KiB\n", "\u001b[2mopentelemetry-exporter-gcp-trace\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/13.65 KiB\n", "\u001b[2mgrpcio-status\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/14.09 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/28.66 KiB\n", "\u001b[2mgoogle-cloud-audit-log\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/31.71 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/46.19 KiB\n", "\u001b[2mu<PERSON>orn   \u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m\u001b[2m------------------------------\u001b[0m\u001b[0m     0 B/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 8.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 16.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 8.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 8.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 8.00 KiB/115.70 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)------------\u001b[0m\u001b[0m 8.00 KiB/128.70 KiB\u001b[23A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 8.00 KiB/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 11.22 KiB/11.22 KiB\n", "\u001b[2mopentelemetry-exporter-gcp-trace\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 13.65 KiB/13.65 KiB\n", "\u001b[2mgrpcio-status\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 14.09 KiB/14.09 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 8.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 16.00 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 16.00 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 16.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-cloud-audit-log\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 31.71 KiB/31.71 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 40.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 8.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 24.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-------\u001b[0m\u001b[0m 48.00 KiB/157.04 KiB\u001b[23A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 8.00 KiB/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 11.22 KiB/11.22 KiB\n", "\u001b[2mopentelemetry-exporter-gcp-trace\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 13.65 KiB/13.65 KiB\n", "\u001b[2mgrpcio-status\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 14.09 KiB/14.09 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 8.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 16.00 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 16.00 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 16.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-cloud-audit-log\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 31.71 KiB/31.71 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 40.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 16.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 24.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-------\u001b[0m\u001b[0m 48.00 KiB/157.04 KiB\u001b[23A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 10.04 KiB/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 11.22 KiB/11.22 KiB\n", "\u001b[2mopentelemetry-exporter-gcp-trace\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 13.65 KiB/13.65 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 8.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 16.00 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 16.00 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 16.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-cloud-audit-log\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 31.71 KiB/31.71 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 56.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 24.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 24.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2K\u001b[22A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\u001b[22A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 10.04 KiB/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 11.22 KiB/11.22 KiB\n", "\u001b[2mopentelemetry-exporter-gcp-trace\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 13.65 KiB/13.65 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 8.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 16.00 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 16.00 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 16.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 56.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 24.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 24.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)--------------------------\u001b[0m\u001b[0m 24.00 KiB/191.62 KiB\u001b[23A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 10.04 KiB/10.04 KiB\n", "\u001b[2muritemplate\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 11.22 KiB/11.22 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 8.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 18.79 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 19.90 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 56.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 24.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 24.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)---\u001b[0m\u001b[0m 56.00 KiB/211.07 KiB\u001b[23A\n", "\u001b[2mzipp      \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 10.04 KiB/10.04 KiB\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 16.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 18.79 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 19.90 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 16.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 56.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 24.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 32.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)---\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\u001b[23A\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 16.00 KiB/16.49 KiB\n", "\u001b[2mgrpc-google-iam-v1\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 18.79 KiB/18.79 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 19.90 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 24.00 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 16.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 32.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 32.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-------------------\u001b[0m\u001b[0m 64.00 KiB/212.94 KiB\u001b[23A\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 16.00 KiB/16.49 KiB\n", "\u001b[2mopentelemetry-resourcedetector-gcp\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 19.90 KiB/19.90 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 27.01 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 40.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 32.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/212.94 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)------------\u001b[0m\u001b[0m 40.00 KiB/224.09 KiB\u001b[23A\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 16.00 KiB/16.49 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 27.01 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgoogle-crc32c\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 32.00 KiB/32.03 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 48.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 32.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 32.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 40.00 KiB/224.09 KiB\n", "\u001b[2mauth<PERSON>b   \u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/234.36 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-------------\u001b[0m\u001b[0m 120.00 KiB/247.61 KiB\u001b[23A\n", "\u001b[2mgoogle-cloud-appengine-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 16.49 KiB/16.49 KiB\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 27.01 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 48.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 40.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 32.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 40.00 KiB/224.09 KiB\n", "\u001b[2mauth<PERSON>b   \u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 120.00 KiB/247.61 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)--\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\u001b[23A\n", "\u001b[2mimportlib-metadata\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 27.01 KiB/27.01 KiB\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 48.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 40.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 32.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 40.00 KiB/224.09 KiB\n", "\u001b[2mauth<PERSON>b   \u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2K\u001b[22A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)-----------\u001b[0m\u001b[0m 96.00 KiB/327.81 KiB\u001b[22A\n", "\u001b[2mgoogle-cloud-core\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 24.00 KiB/28.66 KiB\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 24.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 24.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 48.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 48.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 32.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 48.00 KiB/224.09 KiB\n", "\u001b[2mauth<PERSON>b   \u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 24.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 112.00 KiB/327.81 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\u001b[23A\n", "\u001b[2mgraphviz  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 32.00 KiB/46.19 KiB\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 32.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 32.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 48.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 56.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 40.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 88.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 56.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 32.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 120.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)--\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\u001b[23A\n", "\u001b[2muvicorn   \u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 56.00 KiB/60.97 KiB\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 56.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 64.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 64.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 64.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 88.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 64.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 40.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)--\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\u001b[23A\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 56.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 64.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 64.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 64.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 88.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 64.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 48.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\u001b[23A\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 64.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 64.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 64.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 64.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 64.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 88.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 64.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 112.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\u001b[23A\n", "\u001b[2mopentelemetry-api\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 64.00 KiB/64.23 KiB\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 64.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 64.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 64.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 64.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 64.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 64.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 64.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 64.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 72.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 88.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 112.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\u001b[23A\n", "\u001b[2mgoogle-resumable-media\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 72.00 KiB/79.35 KiB\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 88.00 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 72.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 72.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 80.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 104.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 72.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 72.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 112.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 96.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)---------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\u001b[23A\n", "\u001b[2mhttplib2  \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 94.58 KiB/94.58 KiB\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 96.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 104.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 96.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 128.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 80.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 80.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 112.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[23A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.15 MiB/12.97 MiB      \u001b[23A\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 96.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 104.00 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 104.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-cloud-storage\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 128.00 KiB/128.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 88.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 88.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 112.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[22A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.21 MiB/12.97 MiB      \u001b[22A\n", "\u001b[2mgoogle-cloud-trace\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 96.00 KiB/101.32 KiB\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 108.52 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 104.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 88.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 88.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 120.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[21A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.25 MiB/12.97 MiB      \u001b[21A\n", "\u001b[2mpyparsing \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 108.52 KiB/108.52 KiB\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 104.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 96.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 88.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 120.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[20A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.26 MiB/12.97 MiB      \u001b[20A\n", "\u001b[2mopentelemetry-sdk\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 104.00 KiB/115.70 KiB\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 96.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 88.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 120.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.00 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[19A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.26 MiB/12.97 MiB      \u001b[19A\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 128.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 128.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 128.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m----------\u001b[2m--------------------\u001b[0m\u001b[0m 128.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 128.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 128.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.06 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 832.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[18A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (0/37)----------------\u001b[0m\u001b[0m 2.52 MiB/12.97 MiB      \u001b[18A\n", "\u001b[2mgoogle-api-core\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 128.00 KiB/157.04 KiB\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 128.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 128.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 128.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 128.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 128.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 128.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 144.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 128.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 136.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.06 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 848.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[18A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (21/37)---------------\u001b[0m\u001b[0m 2.58 MiB/12.97 MiB      \u001b[18A\n", "\u001b[2mopentelemetry-semantic-conventions\u001b[0m \u001b[32m-----------------------\u001b[2m-------\u001b[0m\u001b[0m 144.00 KiB/191.62 KiB\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-----------------------\u001b[2m-------\u001b[0m\u001b[0m 160.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m-----------------------\u001b[2m-------\u001b[0m\u001b[0m 160.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 176.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 144.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 176.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----\u001b[2m--------------------------\u001b[0m\u001b[0m 928.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[17A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (21/37)---------------\u001b[0m\u001b[0m 2.77 MiB/12.97 MiB      \u001b[17A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 160.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 1008.00 KiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (21/37)---------------\u001b[0m\u001b[0m 2.98 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-----------------------\u001b[2m-------\u001b[0m\u001b[0m 176.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 1.07 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 1.22 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 1.22 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.23 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.24 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.25 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.25 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.26 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.28 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.29 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.30 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.32 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 1.39 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 192.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 192.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----\u001b[2m-------------------------\u001b[0m\u001b[0m 192.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--\u001b[2m----------------------------\u001b[0m\u001b[0m 192.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.57 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mgoogle-cloud-big<PERSON>y\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 192.00 KiB/247.61 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 200.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 192.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 208.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.60 MiB/7.33 MiB\n", "\u001b[2K\u001b[15A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[15A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 192.00 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-secret-manager\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 192.00 KiB/212.94 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 192.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 192.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.09 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.66 MiB/7.33 MiB\n", "\u001b[2K\u001b[14A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.68 MiB/12.97 MiB      \u001b[14A\n", "\u001b[2mgoogle-genai\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 198.28 KiB/198.28 KiB\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 200.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 216.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 200.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.67 MiB/7.33 MiB\n", "\u001b[2K\u001b[13A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[13A\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 200.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 216.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 200.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.67 MiB/7.33 MiB\n", "\u001b[2K\u001b[12A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[12A\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 200.00 KiB/211.07 KiB\n", "\u001b[2mgoogle-cloud-logging\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 224.00 KiB/224.09 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 200.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.69 MiB/7.33 MiB\n", "\u001b[2K\u001b[12A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[12A\n", "\u001b[2mgoogle-auth\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 208.00 KiB/211.07 KiB\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 208.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 1.69 MiB/7.33 MiB\n", "\u001b[2K\u001b[11A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[11A\n", "\u001b[2mauthlib   \u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 208.00 KiB/234.36 KiB\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 1.71 MiB/7.33 MiB\n", "\u001b[2K\u001b[10A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)---------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[10A\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---\u001b[2m---------------------------\u001b[0m\u001b[0m 256.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 1.74 MiB/7.33 MiB\n", "\u001b[2K\u001b[9A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)----------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[9A\n", "\u001b[2mprotobuf  \u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 256.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 256.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 256.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------\u001b[2m-----------------------\u001b[0m\u001b[0m 256.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m------\u001b[2m------------------------\u001b[0m\u001b[0m 544.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m---------------\u001b[2m---------------\u001b[0m\u001b[0m 2.12 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 2.12 MiB/7.33 MiB\n", "\u001b[2K\u001b[9A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (23/37)----------------\u001b[0m\u001b[0m 3.75 MiB/12.97 MiB      \u001b[9A\n", "\u001b[2mprot<PERSON>uf  \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 312.00 KiB/312.44 KiB\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 320.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 320.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 320.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 832.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 2.19 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 2.53 MiB/7.33 MiB\n", "\u001b[2K\u001b[9A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (29/37)----------------\u001b[0m\u001b[0m 3.85 MiB/12.97 MiB      \u001b[9A\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 320.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 320.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 320.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 832.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 2.19 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 2.59 MiB/7.33 MiB\n", "\u001b[2K\u001b[8A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (29/37)----------------\u001b[0m\u001b[0m 3.85 MiB/12.97 MiB      \u001b[8A\n", "\u001b[2mgoogle-cloud-speech\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 320.00 KiB/327.81 KiB\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 320.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------\u001b[2m----------------------\u001b[0m\u001b[0m 320.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------\u001b[2m---------------------\u001b[0m\u001b[0m 832.00 KiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 2.19 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 2.59 MiB/7.33 MiB\n", "\u001b[2K\u001b[8A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (29/37)----------------\u001b[0m\u001b[0m 4.16 MiB/12.97 MiB      \u001b[8A\n", "\u001b[2mgoogle-cloud-resource-manager\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 328.00 KiB/385.10 KiB\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 1.52 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------\u001b[2m-------------------\u001b[0m\u001b[0m 2.60 MiB/7.33 MiB\n", "\u001b[2K\u001b[7A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (29/37)----------------\u001b[0m\u001b[0m 4.23 MiB/12.97 MiB      \u001b[7A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 1.70 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.70 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (29/37)----------------\u001b[0m\u001b[0m 4.73 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 1.95 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 768.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 784.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 792.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 800.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 808.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 816.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 824.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 832.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 840.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 848.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 864.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m-----------------------\u001b[2m-------\u001b[0m\u001b[0m 936.00 KiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.28 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2mgoogle-adk\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 1.11 MiB/1.25 MiB\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 2.33 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[5A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[5A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 2.50 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 2.06 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m--------------------\u001b[2m----------\u001b[0m\u001b[0m 2.81 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.72 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.81 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 2.12 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-------------------------\u001b[2m-----\u001b[0m\u001b[0m 3.45 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.73 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (32/37)----------------\u001b[0m\u001b[0m 4.88 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 2.12 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 3.95 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------\u001b[2m------------------\u001b[0m\u001b[0m 2.90 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)----------------\u001b[0m\u001b[0m 5.45 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 2.12 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 3.95 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------\u001b[2m-----------------\u001b[0m\u001b[0m 3.17 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)----------------\u001b[0m\u001b[0m 5.55 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 2.66 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 3.95 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------\u001b[2m----------------\u001b[0m\u001b[0m 3.39 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)----------------\u001b[0m\u001b[0m 5.55 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2msha<PERSON>y   \u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 2.91 MiB/2.95 MiB\n", "\u001b[2mcryptography\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 4.03 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----------------\u001b[2m--------------\u001b[0m\u001b[0m 3.79 MiB/7.33 MiB\n", "\u001b[2K\u001b[4A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)----------------\u001b[0m\u001b[0m 5.71 MiB/12.97 MiB      \u001b[4A\n", "\u001b[2mcryptography\u001b[0m \u001b[32m------------------------------\u001b[2m\u001b[0m\u001b[0m 4.16 MiB/4.26 MiB\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------------\u001b[2m-------------\u001b[0m\u001b[0m 3.98 MiB/7.33 MiB\n", "\u001b[2K\u001b[3A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[3A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 4.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (33/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------------\u001b[2m------------\u001b[0m\u001b[0m 4.32 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.53 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.54 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.55 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.55 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.56 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.57 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.58 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.59 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.59 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.60 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.61 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.62 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.62 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.63 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.63 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.64 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.67 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)2m--------------\u001b[0m\u001b[0m 6.77 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)[2m-------------\u001b[0m\u001b[0m 6.92 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)[2m-------------\u001b[0m\u001b[0m 6.95 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-------------------\u001b[2m-----------\u001b[0m\u001b[0m 4.64 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)[2m-------------\u001b[0m\u001b[0m 7.26 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m---------------------\u001b[2m---------\u001b[0m\u001b[0m 5.12 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)\u001b[2m------------\u001b[0m\u001b[0m 7.55 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----------------------\u001b[2m--------\u001b[0m\u001b[0m 5.36 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)\u001b[2m------------\u001b[0m\u001b[0m 7.55 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m------------------------\u001b[2m------\u001b[0m\u001b[0m 5.73 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)-\u001b[2m-----------\u001b[0m\u001b[0m 8.13 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.26 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.37 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.37 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.39 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.40 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.41 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.41 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.42 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.43 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.52 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠇\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)--\u001b[2m----------\u001b[0m\u001b[0m 8.63 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)---\u001b[2m---------\u001b[0m\u001b[0m 8.73 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)---\u001b[2m---------\u001b[0m\u001b[0m 8.90 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠋\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)---\u001b[2m---------\u001b[0m\u001b[0m 8.95 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)---\u001b[2m---------\u001b[0m\u001b[0m 9.07 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)---\u001b[2m---------\u001b[0m\u001b[0m 9.07 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.11 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.13 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠙\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.14 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m--------------------------\u001b[2m----\u001b[0m\u001b[0m 6.27 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.15 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m---------------------------\u001b[2m---\u001b[0m\u001b[0m 6.38 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.44 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m----------------------------\u001b[2m--\u001b[0m\u001b[0m 6.77 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)----\u001b[2m--------\u001b[0m\u001b[0m 9.44 MiB/12.97 MiB      \u001b[2A\n", "\u001b[2mgoogle-cloud-aiplatform\u001b[0m \u001b[32m-----------------------------\u001b[2m-\u001b[0m\u001b[0m 6.98 MiB/7.33 MiB\n", "\u001b[2K\u001b[2A\u001b[37m⠹\u001b[0m \u001b[2mPreparing packages...\u001b[0m (35/37)------\u001b[2m------\u001b[0m\u001b[0m 10.27 MiB/12.97 MiB     \u001b[2A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)-------\u001b[2m-----\u001b[0m\u001b[0m 10.58 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠸\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)-------\u001b[2m-----\u001b[0m\u001b[0m 10.58 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠼\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)--------\u001b[2m----\u001b[0m\u001b[0m 11.20 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.79 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.79 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠴\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.80 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.80 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.80 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.81 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.82 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.83 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.84 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.84 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.85 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.86 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠦\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.88 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 11.89 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)----------\u001b[2m--\u001b[0m\u001b[0m 12.01 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)-----------\u001b[2m-\u001b[0m\u001b[0m 12.16 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[1A\u001b[37m⠧\u001b[0m \u001b[2mPreparing packages...\u001b[0m (36/37)-----------\u001b[2m-\u001b[0m\u001b[0m 12.48 MiB/12.97 MiB     \u001b[1A\n", "\u001b[2K\u001b[2mPrepared \u001b[1m37 packages\u001b[0m \u001b[2min 6.77s\u001b[0m\u001b[0m                                                \u001b[1A\n", "\u001b[2mUninstalled \u001b[1m2 packages\u001b[0m \u001b[2min 11ms\u001b[0m\u001b[0m\n", "\u001b[2K░░░░░░░░░░░░░░░░░░░░ [0/47] \u001b[2mInstalling wheels...                                \u001b[0m\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1mFailed to hardlink files; falling back to full copy. This may lead to degraded performance.\n", "         If the cache and target directories are on different filesystems, hardlinking may not be supported.\n", "         If this is intentional, set `export UV_LINK_MODE=copy` or use `--link-mode=copy` to suppress this warning.\u001b[0m\n", "\u001b[2K\u001b[2mInstalled \u001b[1m47 packages\u001b[0m \u001b[2min 156ms\u001b[0m\u001b[0mrm==1.97.0                    \u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mauth<PERSON>b\u001b[0m\u001b[2m==1.6.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcachetools\u001b[0m\u001b[2m==5.5.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcffi\u001b[0m\u001b[2m==1.17.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcloud<PERSON>kle\u001b[0m\u001b[2m==3.1.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcryptography\u001b[0m\u001b[2m==45.0.4\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mdocstring-parser\u001b[0m\u001b[2m==0.16\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-adk\u001b[0m\u001b[2m==1.3.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-api-core\u001b[0m\u001b[2m==2.25.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-api-python-client\u001b[0m\u001b[2m==2.172.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-auth\u001b[0m\u001b[2m==2.40.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-auth-httplib2\u001b[0m\u001b[2m==0.2.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-aiplatform\u001b[0m\u001b[2m==1.97.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-appengine-logging\u001b[0m\u001b[2m==1.6.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-audit-log\u001b[0m\u001b[2m==0.3.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-bigquery\u001b[0m\u001b[2m==3.34.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-core\u001b[0m\u001b[2m==2.4.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-logging\u001b[0m\u001b[2m==3.12.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-resource-manager\u001b[0m\u001b[2m==1.14.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-secret-manager\u001b[0m\u001b[2m==2.24.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-speech\u001b[0m\u001b[2m==2.33.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-storage\u001b[0m\u001b[2m==2.19.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-cloud-trace\u001b[0m\u001b[2m==1.16.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-crc32c\u001b[0m\u001b[2m==1.7.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-genai\u001b[0m\u001b[2m==1.20.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogle-resumable-media\u001b[0m\u001b[2m==2.7.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgoogleapis-common-protos\u001b[0m\u001b[2m==1.70.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgraphviz\u001b[0m\u001b[2m==0.21\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgrpc-google-iam-v1\u001b[0m\u001b[2m==0.14.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgrpcio-status\u001b[0m\u001b[2m==1.67.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mhttplib2\u001b[0m\u001b[2m==0.22.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mimportlib-metadata\u001b[0m\u001b[2m==8.7.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-api\u001b[0m\u001b[2m==1.34.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-exporter-gcp-trace\u001b[0m\u001b[2m==1.9.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-resourcedetector-gcp\u001b[0m\u001b[2m==1.9.0a0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-sdk\u001b[0m\u001b[2m==1.34.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-semantic-conventions\u001b[0m\u001b[2m==0.55b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mproto-plus\u001b[0m\u001b[2m==1.26.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mprotob<PERSON>\u001b[0m\u001b[2m==6.30.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mprotob<PERSON>\u001b[0m\u001b[2m==5.29.5\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyasn1\u001b[0m\u001b[2m==0.6.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyasn1-modules\u001b[0m\u001b[2m==0.4.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyc<PERSON>ser\u001b[0m\u001b[2m==2.22\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyparsing\u001b[0m\u001b[2m==3.2.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mrsa\u001b[0m\u001b[2m==4.9.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mshapely\u001b[0m\u001b[2m==2.1.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1muritemplate\u001b[0m\u001b[2m==4.2.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mu<PERSON>orn\u001b[0m\u001b[2m==0.32.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mu<PERSON>orn\u001b[0m\u001b[2m==0.34.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mzipp\u001b[0m\u001b[2m==3.23.0\u001b[0m\n"]}], "source": ["! uv pip install google-adk"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"GOOGLE_GENAI_USE_VERTEXAI\"] = \"TRUE\"\n", "os.environ[\"GOOGLE_CLOUD_PROJECT\"] = \"gcp-quwan-gemini\"\n", "os.environ[\"GOOGLE_CLOUD_LOCATION\"] = \"us-central1\"\n", "\n", "\n", "LOCATION = \"us-central1\"\n", "PROJECT = \"gcp-quwan-gemini\"\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import google.auth\n", "import google.auth.transport.requests\n", "credentials, _ = google.auth.default()\n", "auth_req = google.auth.transport.requests.Request()\n", "credentials.refresh(auth_req)\n", "\n", "\n", "url = f\"https://{LOCATION}-aiplatform.googleapis.com/v1beta1/projects/{PROJECT}/l\n", "response = requests.post(\n", "url=url,\n", "json={\n", "\"contextSpec\": {\n", "\"memoryBankConfig\": {\n", "\"generationConfig\": {\n", "\"model\": f\"projects/ /locations/ /publishe\n", "},\n", "\"similaritySearchConfig\": {\n", "\"embeddingModel\": f\"projects/ /locations/ /\n", "}\n", "}\n", "}\n", "},\n", "headers={\n", "\"Content-Type\": \"application/json; charset=utf-8\",\n", "\"Authorization\": f\"Bearer {credentials.token}\"\n", "}\n", ")\n", "split_name = response.json()[\"name\"].split(\"/\")\n", "agent_engine_name = \"/\".join(split_name[:-2])"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}