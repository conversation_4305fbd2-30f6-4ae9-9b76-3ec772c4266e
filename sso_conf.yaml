# SSO配置文件
# 根据运维平台SSO接入文档配置

data:
  # 应用基本信息
  sc: "ops-brain"  # 应用代指code，需要向运维平台申请
  name: "ops-brain智能运维机器人"  # 应用中文名称
  
  # 环境配置 - 测试环境 (内网不可达，使用测试环境)
  environment: "testing"

  # SSO服务端点配置
  # 使用测试环境地址
  sso_domain: "http://testing-yw.ttyuyin.com"

  # 票据验证API (测试环境地址)
  validate_api: "http://testing-yw.ttyuyin.com/api/sso/validate/"

  # 单点登出API (测试环境地址，用于重定向)
  logout_api: "http://testing-yw.ttyuyin.com/accounts/logout/"

  # 公共域名 (测试环境地址)
  public_domain: "http://testing-yw.ttyuyin.com"

  # JSON-RPC接口 (测试环境地址)
  jsonrpc: "http://testing-yw.ttyuyin.com/api/jsonrpc/"
  
  # 应用接入路径配置
  auth_ticket_path: "/auth/sso-ticket-auth/"  # SSO票据验证接口路径
  public_action_path: "/public_action/"       # 公共操作接口路径
  
  # RSA私钥 (用于解密用户信息)
  # 注意：这是示例私钥，实际使用时需要从运维平台获取真实私钥
  private_key: |
    -----BEGIN RSA PRIVATE KEY-----
    MIIEpAIBAAKCAQEA0vx8...这里需要替换为真实的私钥...
    -----END RSA PRIVATE KEY-----
  
  # 签名盐值 (用于签名验证)
  salt: "your_salt_value_here"  # 需要从运维平台获取真实盐值
  
  # 错误页面重定向地址
  error_page_url: "/error"
  
  # 应用配置信息
  app_config:
    # 应用地址 (前端地址)
    app_url: "http://localhost:8000"
    
    # 权限管理人员
    admin_contact: "运维团队"
    
    # 系统简介
    description: "基于LangGraph的智能运维机器人系统，提供自动化运维和智能问答服务"
    
    # Cookie配置
    cookie:
      name: "Authorization"
      domain: "localhost"
      path: "/"
      secure: false  # 开发环境设为false，生产环境应设为true
      httponly: true
      samesite: "lax"
      max_age: 86400  # 24小时

# 环境特定配置
environments:
  development:
    sso_domain: "http://**************"
    validate_api: "http://**************/api/sso/validate/"
    logout_api: "http://**************/accounts/logout/"
    public_domain: "http://**************"
    app_url: "http://localhost:8000"
    
  testing:
    sso_domain: "http://testing-yw.ttyuyin.com"
    validate_api: "http://testing-yw.ttyuyin.com/api/sso/validate/"
    logout_api: "http://testing-yw.ttyuyin.com/accounts/logout/"
    public_domain: "http://testing-yw.ttyuyin.com"
    app_url: "http://testing-ops-brain.ttyuyin.com"
    
  production:
    sso_domain: "https://yw-sso.ttyuyin.com"
    validate_api: "https://yw-sso.ttyuyin.com/api/sso/validate/"
    logout_api: "https://yw-sso.ttyuyin.com/accounts/logout/"
    public_domain: "https://yw-sso.ttyuyin.com"
    app_url: "https://ops-brain.ttyuyin.com"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  
# 安全配置
security:
  # 票据有效期检查 (秒)
  ticket_timeout: 300  # 5分钟
  
  # 防重放攻击
  replay_protection: true
  
  # 用户信息缓存时间 (秒)
  user_cache_timeout: 3600  # 1小时
