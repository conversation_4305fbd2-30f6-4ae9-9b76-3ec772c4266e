北斗-运维平台SSO接入文档
技术支持： 北斗-运维平台技术人员列表 
运维平台各环境地址
环境	入口	说明
生产环境	https://yw-sso.ttyuyin.com/	
云测试环境	http://testing-yw.ttyuyin.com/	


接口说明
validate_api
Method: POST
ticket校验接口
第三方应用在收到运维平台携带ticket的验证请求后，请求该接接口校验ticket的有效性
请求参数

JSON
{
    "ticket": "ticket"
}

响应-成功

JSON
{
    "data":
        {
            "ciphertext": "加密字符串"
        }, 
    "signature": "`签名`"
}

响应-异常

JSON
{
    "errors": "ticket 过期",
    "code": -1001
}
1.-1001 ticket 过期
2.-1002 ticket 重复
3.-1003 参数错误


logout_api
Method: GET
单点登出
第三方应用在登出自身系统后，可以调用该接口同时登出运维平台

注：直接跳转该接口


第三方应用接入流程【第三方应用对接说明】
注册应用信息
接入的应用需要提供以下信息，用于运维平台生成相应的配置文件。
更多配置文件信息说明可点击配置文件说明查看。
 
名称	样例	说明
环境	开发|测试|正式	
应用名	CMDB	
应用地址	http://dev-yw-cmdb.ttyuyin.com/	一般为前端地址
sc	cmdb	应用的代指code
auth_ticket_path	默认/auth/sso-ticket-auth/	接入的应用认证运维SSO的API
public_action_path	默认/public_action/	通常用于应用创建用户、权限分配等动作
权限管理人员	小王	用户接入应用的信息展示
系统简介	abc	20-30字简介
系统图片	/	建议使用png
单点登陆
有两个途径对目标系统进行单点登录

1.从运维平台页面入口登录目标系统
在运维平台（SSO）登录成功，对目标系统进行登录时，运维平台会携带ticekt请求目标系统的ticket验证后端（通常为目标系统后端的 /auth/sso-ticket-auth/，通过目标系统前端反向代理访问）进行验证。
目标系统解密sso加密信息，如解密成功则表示认证有效。

2.目标系统主动请求运维平台进行单点登录
目标系统如果需要进行单点登录验证，可以通过跳转运维平台的API进行主动登录验证
Bash
sso_api?sc=xx&target=local_url
# sso_api; sc 都可以在 运维平台生成的 yaml 配置文件中获取
# target 会在生成 ticket 验证时返回，
# 目标系统可以在验证 ticket 成功后跳转至 target 对应的本地系统的 url


目标系统的ticket验证后端/auth/sso-ticket-auth/例子：
PY代码可使用border_secure_core库

Python
from border_secure_core.utils import YamlParse

# API => /auth/sso-ticket-auth/
class SSOTicketAuthView(MethodView):
    def get(self):
        """ticket验证后端
        """
        # 1. 从url参数获取ticket
        ticket = request.args.get('ticket', '')
        
        # 2. 读取sso_conf.yaml配置文件
        y = YamlParser('./sso_conf.yaml')
        
        # 3. 请求运维平台校验ticket有效性
        # 该函数动作为请求sso_conf.yaml文件中的validate_api
        # post validate_api data: {"ticket": ticket}
        # 响应数据结构请阅读下文
        d = y.request_validate_api(ticket=ticket)
     
        
        if d.get('errors'):
            # 存在错误信息，异常处理
            return d
            
        # 4. 解码加密字符串， “加密字符串解密”逻辑请阅读下文
        ts, feature, uid, username, sid, = y.decrypt(d['data']['ciphertext']).split(':')

        # 5. 解码成功，获取验证成功的用户名
        # 进行系统本地登录流程，如生成对应用户的Token等
        try:
            user = User.objects.get(name=username)
        except:
            # 当用户不存在时，可以选择创建用户或拒绝登录等
            return redirect(y.error_page_redict('用户未授权'), code=302)

        # 6. 从url参数中获取要跳转的本地系统前端地址，默认为本地系统前端根路径，可直接选择/
        target = request.args.get('target', '/')
        
        # 7. 设置重定向信息，在跳转前端之前在后端设置用户的登录信息（cookie写入token等）
        response = redirect(target, code=302)
        response.set_cookie('Authorization', f'{create_token_by_user(str(user.pk))}')
        
        # 8. 跳转
        return respons

解密加密字符串流程（python）
private_key从sso_conf.yaml中获取
数据使用RSA算法进行加解密，解密时需要对原加密字符串 进行base64解码，得到二进制数据。 用 PKCS1_OAEP填充方案进行解密，得到用户信息字符串。
用户信息字符串说明:
{时间戳}:{2位随机数}:{1～2位数字id}:{username}:{1～2位数字id}
例如 **********:Sw:1:xiaoming:3
其中"xiaoming"username
注: 时间戳为ticket生成的时间戳，可作为过期的依据,理论上，返回加密字符串不会重复，可以此作为重放攻击的判断



单点登出
目标系统开发 /auth/sso-logout/ (GET 方法)接口，该接口需要实现两个行为
3.清除属于该系统的cookie信息
4.完成1后，重定向到 http://{sso_url}/accounts/logout/ 这个url，清除sso的session
 该url即是yaml配置文件中的 "data.logout_api"，最后退出运维平台（及TT登录中心）

PY代码可使用border_secure_core库
Python
ticket = request.query_params.get('ticket', '')
y = YamlParser('./sso_conf.yaml')
logout_api = y.loaded['data']['logout_api']
response = HttpResponseRedirect(logout_api)
response.delete_cookie('Authorization')

public_action（非必要）
主要应用于运维平台发送创建用户信息给接入应用，
接入应用收到请求后，需要再次请求运维平台的该接口解码验证
Python样例
Python
# 接入应用的 public action API

action = request.args.get('action', '')
sc = request.args.get('sc')
uuid = request.args.get('uuid')
user_no = request.args.get('user_no') # 工号
y = YamlParser('./sso_conf.yaml')

try:
    resp = y.request_sso_jsonrpc(method=action, sc=sc, uuid=uuid)、
    '''
        jsonrpc req data
        {
            'method': action,
            'jsonrpc': '2.0',
            'id': 0,
            'params': {
                'sc': sc,
                'uuid': uuid
                }
        }
    '''
    username = y.decrypt(resp['ciphertext'])
    # 解码成功则有效
except Exception as e:
    return dict({'error': '创建用户出错 {}'.format(e), 'code': -1})

# 创建用户等动作

return dict({'code': 0})
运维平台接口
该接口已不再分配新系统，人员信息建议使用【用户中心】接口
可参阅 【灵犀平台-用户中心】开发者文档 
获取系统账号信息
/accounts/admin/get
GET

page	int
limit	int
EG
Bash
curl --request GET \
  --url 'http://**************/accounts/admin/get/?page=1&limit=10' \
  --header 'SSO-AUTHORIZATION: xxx'

其他
运维平台配置文件说明
配置文件说明:
1.private_key: 私钥用于解密
2.salt 盐值，用于签名加盐
3.sc 系统代号 name 中文名称
4.sso_api: **************:8000/sso/(貌似没用)
5.validate_api: 用ticket交换加密的用户信息(内网)
6.logout_api: 注销sso重定向地址(公网)，
应用在重定向时携带sc参数可实现运维平台验证后自动登录应用，同时支持target参数记录应用的访问信息，应用可以通过target获取用户在未登录前想要访问的地址
7.public_domain: sso域名(公网)
8.jsonrpc: jsonrpc地址(内网)
配置文件样例

样例
Go
感谢黄泽雄提供的Go样例

Go
// 单点登录


func ValidateTicket(validateApi, ticket string) (rs string, err error) {

   type postData struct {
      Ticket string `json:"ticket"`
   }

   var pd postData

   pd.Ticket = ticket
   postDataMarshal, err := json.Marshal(pd)
   if err != nil {
      return
   }
   rs, err = HttpHandle(validateApi, "POST", "", postDataMarshal)

   return
}

ticket := c.Ctx.Input.Query("ticket")
if len(ticket) == 0 {
   ticket = ""
}

privateKey, _, _, _, _, validateApi, _, _, _ := utils.GetSsoConfig(ssoDir,
   ssoFileNameOnly, "yaml")
vt, err := utils.ValidateTicket(validateApi, ticket)
if err != nil {
   logs.Error("utils.ValidateTicket() err: ", err.Error())
   c.ApiJson(403, err.Error(), "error")
   return
}

type SsoData struct {
   Data struct {
      Ciphertext string `json:"ciphertext"`
   } `json:"data"`
   Signature string `json:"signature"`
}
var sd SsoData
if err := json.Unmarshal([]byte(vt), &sd); err != nil {
   logs.Error("json.Unmarshal SsoData err: ", err.Error())
   c.ApiJson(403, err.Error(), "error")
   return
}

ciphertext := sd.Data.Ciphertext

decrypt, err := utils.RsaDecryptOAEP(ciphertext, privateKey)
if err != nil {
   logs.Error("utils.RsaDecryptOAEP err: ", err.Error())
   c.ApiJson(403, err.Error(), "error")
   return
}

decryptList := strings.Split(string(decrypt), ":")
//ts, feature, uid, username, sid,
username := decryptList[3]  // 获取用户后设置用户token

// set cache 参考
tiJson, _ := json.Marshal(utils.Struct2Map(tokenInfo))
c.Token = token
GetCache.Put(token, string(tiJson), time.Duration(redisExpire)*time.Second)
c.Ctx.SetCookie("Authorization", token, redisExpire)
c.Ctx.Redirect(302, target)

Go
// 单点登出
_, _, _, _, _, _, logoutApi, _, _ := utils.GetSsoConfig(ssoDir,
   ssoFileNameOnly, "yaml") // 获取文件内容

c.Ctx.Redirect(302, logoutApi)
GetCache.Delete(c.Token)
c.Ctx.SetCookie("Authorization", "")
