from typing import List

import requests
from langchain.embeddings.base import Embeddings
from pydantic import BaseModel, Field


class Qwen3EmbeddingConfig(BaseModel):
    base_url: str = Field(default="http://ray.ttyuyin.com:10001/ray-qwen3-emb")
    model_name: str = Field(default="Qwen/Qwen3-Embedding-4B", description="模型名称")
    timeout: int = Field(default=30, description="请求超时时间")


class Qwen3Embedding(Embeddings):
    def __init__(
        self,
        base_url: str = None,
        model_name: str = None,
        timeout: int = None,
        is_query: bool = False,
    ):
        """
        初始化Qwen3Embedding

        Args:
            base_url: 服务地址，如果为None则使用默认值
            model_name: 模型名称，如果为None则使用默认值
            timeout: 超时时间，如果为None则使用默认值
            is_query: 是否用于查询
        """
        # 创建默认配置
        default_config = Qwen3EmbeddingConfig()

        # 使用传入的参数覆盖默认配置
        self.config = Qwen3EmbeddingConfig(
            base_url=base_url or default_config.base_url,
            model_name=model_name or default_config.model_name,
            timeout=timeout or default_config.timeout,
        )
        # TODO rerank
        self.is_query = is_query

    def _get_embedding(self, texts: List[str]) -> List[float]:
        url = f"{self.config.base_url}/v1/embeddings"

        payload = {
            "model": self.config.model_name,
            "input": texts,
            "encoding_format": "float",
            "is_query": self.is_query,
        }

        response = requests.post(url, json=payload, timeout=self.config.timeout)
        response.raise_for_status()

        return response.json()["data"]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents using Qwen3."""
        data = self._get_embedding(texts)
        return [d["embedding"] for d in data]

    def embed_query(self, text: str) -> List[float]:
        """Embed a query using Qwen3."""
        data = self._get_embedding([text])
        return data[0]["embedding"]


if __name__ == "__main__":
    embedding = Qwen3Embedding(is_query=True)
    print(embedding.embed_query("Hello, world!"))
    print(embedding.embed_documents(["Hello, world!", "Hello, world!"]))
