from typing import Dict, Optional, Type, Union

import openai
from langchain_core.messages import AIMessageChunk
from langchain_core.outputs import ChatGenerationChunk, ChatResult
from langchain_openai import ChatOpenAI


class VllmQwen3ChatModel(ChatOpenAI):
    """基于 vLLM 8.5 的 Qwen3 聊天模型实现。 NOTE: 后面8.6可能会修复, 就不需要这个类了

    这个类继承自 ChatOpenAI，专门用于处理 Qwen3 模型的特殊输出格式。
    主要功能包括：
    1. 处理模型输出的 reasoning_content 字段
    2. 支持流式输出的特殊处理
    3. 保持与标准 ChatOpenAI 接口的兼容性

    该模型特别适用于需要处理 Qwen3 模型特有输出格式的场景，
    比如当模型输出包含推理过程（reasoning_content）时。
    """

    def _create_chat_result(
        self,
        response: Union[dict, openai.BaseModel],
        generation_info: Optional[Dict] = None,
    ) -> ChatResult:
        rtn = super()._create_chat_result(response, generation_info)

        if not isinstance(response, openai.BaseModel):
            return rtn
        msg = response.choices[0].message
        if hasattr(msg, "reasoning_content"):  # type: ignore
            if not msg.content:
                rtn.generations[0].message.content = msg.reasoning_content  # type: ignore
            else:
                rtn.generations[0].message.additional_kwargs["reasoning_content"] = (
                    msg.reasoning_content  # type: ignore
                )
        return rtn

    def _convert_chunk_to_generation_chunk(
        self,
        chunk: dict,
        default_chunk_class: Type,
        base_generation_info: Optional[Dict],
    ) -> Optional[ChatGenerationChunk]:
        generation_chunk = super()._convert_chunk_to_generation_chunk(
            chunk,
            default_chunk_class,
            base_generation_info,
        )
        if (choices := chunk.get("choices")) and generation_chunk:
            top = choices[0]
            if reasoning_content := top.get("delta", {}).get("reasoning_content"):
                if isinstance(generation_chunk.message, AIMessageChunk):
                    if not generation_chunk.message.content:
                        generation_chunk.message.content = reasoning_content
                    else:
                        generation_chunk.message.additional_kwargs[
                            "reasoning_content"
                        ] = reasoning_content
        return generation_chunk
