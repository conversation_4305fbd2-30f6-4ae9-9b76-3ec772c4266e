import os
from enum import Enum
from functools import cache

from langchain.chat_models import init_chat_model
from langchain.embeddings.base import Embeddings
from langchain.retrievers.document_compressors.base import BaseDocumentCompressor
from langchain_community.chat_models.tongyi import Cha<PERSON><PERSON><PERSON><PERSON>
from langchain_core.language_models.chat_models import BaseChatModel
from langfuse import Langfuse

from settings import get_or_creat_settings_ins
from settings.common import LLMModel
from settings.settings import ModelSettings

from .custom_models import VllmQwen3ChatModel

config = get_or_creat_settings_ins()


@cache
def get_chat_model(
    model_name: str = "default", temperature: float | None = None
) -> BaseChatModel:
    models: ModelSettings = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm: LLMModel = config.llms[model_cfg]
    # 可以覆盖默认的temperature, 便于我在一些脚本中定制一些不同温度的策略
    if temperature is not None:
        llm.temperature = temperature
    if llm.provider == "qwen":
        return ChatTongyi(
            top_p=llm.temperature,
            model=llm.model,
            **(llm.external_args or {}),
        )
    elif llm.provider == "vllm_qwen3":
        return VllmQwen3ChatModel(
            model=llm.model,
            temperature=llm.temperature,
            extra_body=llm.extra_body,
            **(llm.external_args or {}),
        )
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        temperature=llm.temperature,
        max_tokens=llm.max_tokens,
        **(llm.external_args or {}),
    )


@cache
def get_embedder_model(
    is_query: bool = False,
) -> Embeddings:
    from src.infra.clients.embedding_factory import get_embedding_client

    return get_embedding_client(is_query=is_query)


def get_reranker_model() -> BaseDocumentCompressor:
    from .custom_models import Qwen3Rerank

    return Qwen3Rerank()


@cache
def get_langfuse_client() -> Langfuse:
    return Langfuse()


class PromptName(str, Enum):
    PLANNER = "planner"
    STEPS_EXECUTOR = "steps_executor"
    TALKER = "talker"
    TALKER_V2 = "talker-v2"
    GEN_QUESTION = "gen_question"
    SUMMARY = "summary"


@cache
def get_prompt_from_langfuse(prompt_name: PromptName) -> list[tuple[str, str]]:
    """从Langfuse获取指定名称的prompt模板

    Args:
        prompt_name (PromptName): prompt模板名称,可选值见PromptName枚举

    Returns:
        list[tuple[str, str]]: 返回LangChain格式的prompt模板列表,每个元素为(role, content)元组
    """
    langfuse_env = os.getenv("LANGFUSE_TRACING_ENVIRONMENT")
    if langfuse_env is None:
        raise ValueError("LANGFUSE_TRACING_ENVIRONMENT is not set")
    prompt = get_langfuse_client().get_prompt(prompt_name, label=langfuse_env)

    if prompt is None:
        raise ValueError(
            f"Prompt {prompt_name} not found, 去langfuse创建{prompt_name}的prompt模板"
        )
    return prompt.get_langchain_prompt()
    return prompt.get_langchain_prompt()
