from pydantic import BaseModel, Field
from typing import Dict, Union, Literal


class AuthorizationSchema(BaseModel):
    action: str = Field(description="authorization action")
    args: Dict = Field(description="authorization args")

    class Config:
        arbitrary_types_allowed = True


class InputSchema(BaseModel):
    prompt: str = Field(description="interaction prompt")


class InterruptSchema(BaseModel):
    type: Literal["auth", "human"] = Field(description="interrupt type")
    body: Union[AuthorizationSchema, InputSchema] = Field(
        description="interrupt body")
