from typing import Any, Dict
from langgraph.types import interrupt

__all__ = ["require_authorization", "require_input"]
def _interrupt(value: Any) -> Any:
    return interrupt(value)


def require_authorization(action: str, args: Dict[str, Any]) -> Any:
    return _interrupt(
        {
            "type": "auth",
            "body": {
                "action": action,
                "args": args,
            }
        }
    )

def require_input(prompt: str) -> Any:
    return _interrupt(
        {
            "type": "interaction",
            "body": {
                "prompt": prompt,
            }
        }
    )