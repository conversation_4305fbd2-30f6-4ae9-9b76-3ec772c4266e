from typing import Any, Optional
from urllib.parse import urljoin

from pydantic import BaseModel, Field, PrivateAttr
from requests import Session, session

__all__ = ["ConStackClient"]

# 牵星客户端
class ConStackClient(BaseModel):
    base_url: str = Field(..., description="constack host")
    token: str = Field(..., description="constack token")
    _session: Optional[Session] = PrivateAttr(default=None)

    def __init__(self, **data: Any):
        super().__init__(**data)
        _session = session()
        _session.headers["X-TOKEN"] = data.get("token")  # type: ignore
        self._session = _session

    def post(self, path: str, **kwargs):
        assert self._session
        url = urljoin(self.base_url, path)
        return self._session.post(url=url, **kwargs)

    def get(self, path: str, **kwargs):
        assert self._session
        url = urljoin(self.base_url, path)
        return self._session.get(url=url, **kwargs)

    def delete(self, path: str, **kwargs):
        assert self._session
        url = urljoin(self.base_url, path)
        return self._session.delete(url=url, **kwargs)
