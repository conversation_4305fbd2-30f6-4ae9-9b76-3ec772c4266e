from src.api.v2.example import *
from src.api.v2.scheduler import *
from src.infra.web.api import sys_router
from src.infra.web.http import http_server

API_V2 = "/api/v2"

http_server.register_router(router=sys_router, tags=["system"])
# example
http_server.register_router(router=example_router, tags=["example"], prefix=API_V2)
# scheduler
http_server.register_router(router=scheduler_router, tags=["scheduler"], prefix=API_V2)
