from src.infra.app import app
from src.infra.web.http import http_server
from src.infra.web.schema import HttpResponse

__all__ = [
    "scheduler_router",
]

scheduler_router = http_server.new_router()

prefix = "/scheduler"


@scheduler_router.delete(f"{prefix}/job/delete")
def delete_job(job_id: str):
    app.scheduler.remove_job(job_id)


@scheduler_router.get(f"{prefix}/job/list")
def list_jobs():
    data = []
    for job in app.scheduler.get_jobs():
        data.append({
            "job_id": job.id,
            "job_name": job.name,
            "job_args": job.args,
            "job_kwargs": job.kwargs,
            "job_next_run_time": job.next_run_time,
            "job_misfire_grace_time": job.misfire_grace_time
        })
    return HttpResponse(
        data=data
    )


@scheduler_router.get(f"{prefix}/pause")
def scheduler_pause():
    app.scheduler.pause()
    return HttpResponse(
        data={
            "message": "Scheduler paused"
        }
    )


@scheduler_router.get(f"{prefix}/resume")
def scheduler_resume():
    app.scheduler.resume()
    return HttpResponse(
        data={
            "message": "Scheduler resumed"
        }
    )
