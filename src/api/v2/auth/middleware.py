"""
SSO认证中间件

提供认证检查、权限验证等中间件功能
"""

from typing import Optional, Callable, List
from functools import wraps
from fastapi import HTTPException, Request, Depends
from fastapi.responses import RedirectResponse
from loguru import logger

from .utils import get_current_user, is_safe_redirect_url
from .models import AuthUser


class AuthMiddleware:
    """认证中间件类"""
    
    def __init__(self, 
                 login_url: str = "/auth/login-redirect/",
                 excluded_paths: Optional[List[str]] = None,
                 allowed_redirect_hosts: Optional[List[str]] = None):
        """
        初始化认证中间件
        
        Args:
            login_url: 登录页面URL
            excluded_paths: 不需要认证的路径列表
            allowed_redirect_hosts: 允许重定向的主机列表
        """
        self.login_url = login_url
        self.excluded_paths = excluded_paths or [
            "/auth/sso-ticket-auth/",
            "/auth/sso-logout/",
            "/auth/login-redirect/",
            "/auth/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/ping"
        ]
        self.allowed_redirect_hosts = allowed_redirect_hosts or []
    
    async def __call__(self, request: Request, call_next):
        """
        中间件处理函数
        
        Args:
            request: FastAPI请求对象
            call_next: 下一个处理函数
            
        Returns:
            响应对象
        """
        # 检查是否是排除路径
        if self._is_excluded_path(request.url.path):
            return await call_next(request)
        
        # 获取当前用户
        current_user = get_current_user(request)
        
        # 如果用户未认证，重定向到登录页面
        if not current_user:
            logger.info(f"用户未认证，重定向到登录页面: {request.url.path}")
            
            # 构建登录重定向URL
            target_url = str(request.url)
            if is_safe_redirect_url(target_url, self.allowed_redirect_hosts):
                login_redirect_url = f"{self.login_url}?target={target_url}"
            else:
                login_redirect_url = self.login_url
            
            return RedirectResponse(url=login_redirect_url, status_code=302)
        
        # 将用户信息添加到请求状态中
        request.state.current_user = current_user
        
        # 继续处理请求
        return await call_next(request)
    
    def _is_excluded_path(self, path: str) -> bool:
        """
        检查路径是否在排除列表中
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否排除
        """
        for excluded_path in self.excluded_paths:
            if path.startswith(excluded_path):
                return True
        return False


# 全局认证中间件实例
auth_middleware = AuthMiddleware()


def require_auth(request: Request) -> AuthUser:
    """
    认证依赖函数
    
    用于FastAPI的Depends，确保接口需要认证
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        AuthUser: 当前认证用户
        
    Raises:
        HTTPException: 当用户未认证时抛出401异常
    """
    current_user = get_current_user(request)
    
    if not current_user:
        logger.warning(f"未认证用户尝试访问受保护资源: {request.url.path}")
        raise HTTPException(
            status_code=401,
            detail="未认证，请先登录",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return current_user


def require_permissions(required_permissions: List[str]):
    """
    权限检查装饰器
    
    Args:
        required_permissions: 需要的权限列表
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # 从kwargs中查找
                request = kwargs.get('request')
            
            if not request:
                logger.error("无法获取request对象进行权限检查")
                raise HTTPException(status_code=500, detail="权限检查失败")
            
            # 获取当前用户
            current_user = get_current_user(request)
            if not current_user:
                raise HTTPException(status_code=401, detail="未认证，请先登录")
            
            # 检查权限（这里需要根据实际的权限系统实现）
            # 目前简化处理，假设所有认证用户都有权限
            user_permissions = getattr(current_user, 'permissions', [])
            
            # 检查是否有所需权限
            missing_permissions = []
            for permission in required_permissions:
                if permission not in user_permissions:
                    missing_permissions.append(permission)
            
            if missing_permissions:
                logger.warning(
                    f"用户 {current_user.username} 缺少权限: {missing_permissions}"
                )
                raise HTTPException(
                    status_code=403,
                    detail=f"权限不足，需要权限: {', '.join(missing_permissions)}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def optional_auth(request: Request) -> Optional[AuthUser]:
    """
    可选认证依赖函数
    
    用于FastAPI的Depends，获取当前用户但不强制要求认证
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Optional[AuthUser]: 当前用户，未认证时返回None
    """
    return get_current_user(request)


class RateLimitMiddleware:
    """简单的速率限制中间件"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        初始化速率限制中间件
        
        Args:
            max_requests: 时间窗口内最大请求数
            window_seconds: 时间窗口大小（秒）
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.request_counts = {}  # 简单的内存存储，生产环境应使用Redis
    
    async def __call__(self, request: Request, call_next):
        """
        速率限制处理函数
        
        Args:
            request: FastAPI请求对象
            call_next: 下一个处理函数
            
        Returns:
            响应对象
        """
        import time
        
        # 获取客户端IP
        client_ip = request.client.host
        current_time = int(time.time())
        window_start = current_time - self.window_seconds
        
        # 清理过期记录
        if client_ip in self.request_counts:
            self.request_counts[client_ip] = [
                timestamp for timestamp in self.request_counts[client_ip]
                if timestamp > window_start
            ]
        else:
            self.request_counts[client_ip] = []
        
        # 检查是否超过限制
        if len(self.request_counts[client_ip]) >= self.max_requests:
            logger.warning(f"客户端 {client_ip} 超过速率限制")
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后重试",
                headers={"Retry-After": str(self.window_seconds)}
            )
        
        # 记录当前请求
        self.request_counts[client_ip].append(current_time)
        
        return await call_next(request)


# 全局速率限制中间件实例
rate_limit_middleware = RateLimitMiddleware()
