"""
SSO单点登录接口实现

根据运维平台SSO接入文档实现票据验证和用户认证
"""

import time
from typing import Optional
from fastapi import HTTPException, Query, Request
from fastapi.responses import RedirectResponse
from loguru import logger

from border_secure_core.utils import YamlParser
from src.infra.web.http import http_server
from src.infra.web.schema import HttpResponse
from .models import SSOUser, SSOValidationResult
from .utils import create_user_token, get_sso_config_path

__all__ = ["sso_router"]

sso_router = http_server.new_router()


@sso_router.get("/sso-ticket-auth/")
async def sso_ticket_auth(
    request: Request,
    ticket: str = Query(..., description="SSO登录票据"),
    target: str = Query("/", description="登录成功后跳转地址")
):
    """
    SSO票据认证接口
    
    这是SSO系统的回调接口，用于处理SSO登录票据验证
    
    Args:
        request: FastAPI请求对象
        ticket: SSO登录票据
        target: 登录成功后的跳转地址
        
    Returns:
        RedirectResponse: 重定向到目标页面，并设置认证Cookie
        
    Raises:
        HTTPException: 当票据验证失败时抛出异常
    """
    try:
        logger.info(f"收到SSO票据认证请求: ticket={ticket[:10]}..., target={target}")
        
        # 1. 加载SSO配置
        sso_config_path = get_sso_config_path()
        yaml_parser = YamlParser(sso_config_path)
        
        # 2. 请求运维平台校验ticket有效性
        logger.info("开始验证SSO票据...")
        validation_result = yaml_parser.request_validate_api(ticket=ticket)
        
        # 3. 检查验证结果
        if validation_result.get('errors'):
            error_msg = validation_result['errors']
            logger.error(f"SSO票据验证失败: {error_msg}")
            
            # 根据错误码返回不同的错误信息
            error_code = validation_result.get('code', -1)
            if error_code == -1001:
                raise HTTPException(status_code=401, detail="登录票据已过期，请重新登录")
            elif error_code == -1002:
                raise HTTPException(status_code=401, detail="登录票据已使用，请重新登录")
            elif error_code == -1003:
                raise HTTPException(status_code=400, detail="登录参数错误")
            else:
                raise HTTPException(status_code=401, detail=f"登录验证失败: {error_msg}")
        
        # 4. 解密用户信息
        logger.info("开始解密用户信息...")
        ciphertext = validation_result['data']['ciphertext']
        decrypted_info = yaml_parser.decrypt(ciphertext)
        
        # 解析用户信息: {时间戳}:{2位随机数}:{1～2位数字id}:{username}:{1～2位数字id}
        info_parts = decrypted_info.split(':')
        if len(info_parts) < 5:
            logger.error(f"用户信息格式错误: {decrypted_info}")
            raise HTTPException(status_code=500, detail="用户信息解析失败")
        
        timestamp, feature, uid, username, sid = info_parts[:5]
        
        # 5. 验证时间戳（防止重放攻击）
        current_time = int(time.time())
        ticket_time = int(timestamp)
        if current_time - ticket_time > 300:  # 5分钟超时
            logger.warning(f"票据时间戳过期: {ticket_time}, 当前时间: {current_time}")
            raise HTTPException(status_code=401, detail="登录票据已过期")
        
        # 6. 创建或获取用户信息
        sso_user = SSOUser(
            username=username,
            uid=uid,
            sid=sid,
            timestamp=ticket_time,
            feature=feature
        )
        
        logger.info(f"用户认证成功: {username} (uid={uid})")
        
        # 7. 生成访问令牌
        access_token = create_user_token(sso_user)
        
        # 8. 设置Cookie并重定向
        response = RedirectResponse(url=target, status_code=302)
        
        # 从配置中获取Cookie设置
        config = yaml_parser.loaded
        cookie_config = config.get('data', {}).get('app_config', {}).get('cookie', {})
        
        response.set_cookie(
            key=cookie_config.get('name', 'Authorization'),
            value=access_token,
            domain=cookie_config.get('domain'),
            path=cookie_config.get('path', '/'),
            secure=cookie_config.get('secure', False),
            httponly=cookie_config.get('httponly', True),
            samesite=cookie_config.get('samesite', 'lax'),
            max_age=cookie_config.get('max_age', 86400)
        )
        
        logger.info(f"用户登录成功，重定向到: {target}")
        return response
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.exception(f"SSO认证过程发生异常: {e}")
        raise HTTPException(status_code=500, detail="登录认证失败，请稍后重试")


@sso_router.get("/sso-logout/")
async def sso_logout(request: Request):
    """
    SSO单点登出接口
    
    清除本地认证状态并重定向到SSO登出页面
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        RedirectResponse: 重定向到SSO登出页面
    """
    try:
        logger.info("收到SSO登出请求")
        
        # 1. 加载SSO配置
        sso_config_path = get_sso_config_path()
        yaml_parser = YamlParser(sso_config_path)
        config = yaml_parser.loaded
        
        # 2. 获取登出API地址
        logout_api = config.get('data', {}).get('logout_api')
        if not logout_api:
            logger.error("未找到logout_api配置")
            raise HTTPException(status_code=500, detail="登出配置错误")
        
        # 3. 创建重定向响应
        response = RedirectResponse(url=logout_api, status_code=302)
        
        # 4. 清除认证Cookie
        cookie_config = config.get('data', {}).get('app_config', {}).get('cookie', {})
        cookie_name = cookie_config.get('name', 'Authorization')
        
        response.delete_cookie(
            key=cookie_name,
            domain=cookie_config.get('domain'),
            path=cookie_config.get('path', '/')
        )
        
        logger.info(f"用户登出成功，重定向到: {logout_api}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"SSO登出过程发生异常: {e}")
        raise HTTPException(status_code=500, detail="登出失败，请稍后重试")


@sso_router.get("/login-redirect/")
async def login_redirect(
    target: str = Query("/", description="登录成功后跳转地址")
):
    """
    登录重定向接口
    
    当用户未登录时，重定向到SSO登录页面
    
    Args:
        target: 登录成功后的跳转地址
        
    Returns:
        RedirectResponse: 重定向到SSO登录页面
    """
    try:
        # 1. 加载SSO配置
        sso_config_path = get_sso_config_path()
        yaml_parser = YamlParser(sso_config_path)
        config = yaml_parser.loaded
        
        # 2. 构建SSO登录URL
        sso_domain = config.get('data', {}).get('sso_domain')
        sc = config.get('data', {}).get('sc')
        
        if not sso_domain or not sc:
            logger.error("SSO配置不完整")
            raise HTTPException(status_code=500, detail="SSO配置错误")
        
        # 构建登录URL: sso_api?sc=xx&target=local_url
        login_url = f"{sso_domain}/sso/?sc={sc}&target={target}"
        
        logger.info(f"重定向到SSO登录页面: {login_url}")
        return RedirectResponse(url=login_url, status_code=302)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"登录重定向过程发生异常: {e}")
        raise HTTPException(status_code=500, detail="登录重定向失败")


@sso_router.get("/health")
async def sso_health_check():
    """
    SSO健康检查接口
    
    检查SSO配置和连通性
    
    Returns:
        HttpResponse: 健康检查结果
    """
    try:
        # 1. 检查配置文件
        sso_config_path = get_sso_config_path()
        yaml_parser = YamlParser(sso_config_path)
        config = yaml_parser.loaded
        
        # 2. 检查必要配置项
        required_keys = ['sc', 'validate_api', 'logout_api', 'private_key']
        missing_keys = []
        
        data_config = config.get('data', {})
        for key in required_keys:
            if not data_config.get(key):
                missing_keys.append(key)
        
        if missing_keys:
            return HttpResponse(
                code=1,
                message=f"SSO配置不完整，缺少: {', '.join(missing_keys)}",
                data={"status": "unhealthy", "missing_keys": missing_keys}
            )
        
        # 3. 返回健康状态
        return HttpResponse(
            data={
                "status": "healthy",
                "sso_domain": data_config.get('sso_domain'),
                "sc": data_config.get('sc'),
                "environment": data_config.get('environment', 'unknown')
            }
        )
        
    except Exception as e:
        logger.exception(f"SSO健康检查失败: {e}")
        return HttpResponse(
            code=1,
            message=f"SSO健康检查失败: {str(e)}",
            data={"status": "unhealthy"}
        )
