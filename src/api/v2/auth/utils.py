"""
SSO认证相关的工具函数
"""

import os
import time
import jwt
import hashlib
from typing import Optional
from pathlib import Path
from loguru import logger

from .models import SSOUser, UserToken, AuthUser

# JWT密钥 - 实际使用时应该从环境变量或配置文件中获取
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "ops-brain-sso-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24


def get_sso_config_path() -> str:
    """
    获取SSO配置文件路径
    
    Returns:
        str: SSO配置文件的绝对路径
    """
    # 优先从环境变量获取
    config_path = os.getenv("SSO_CONFIG_PATH")
    if config_path and os.path.exists(config_path):
        return config_path
    
    # 默认使用项目根目录下的配置文件
    project_root = Path(__file__).parent.parent.parent.parent.parent
    default_path = project_root / "sso_conf.yaml"
    
    if default_path.exists():
        return str(default_path)
    
    # 如果都不存在，返回默认路径（让调用方处理文件不存在的情况）
    return str(default_path)


def create_user_token(sso_user: SSOUser) -> str:
    """
    为SSO用户创建JWT访问令牌
    
    Args:
        sso_user: SSO用户信息
        
    Returns:
        str: JWT访问令牌
    """
    current_time = int(time.time())
    expire_time = current_time + (JWT_EXPIRE_HOURS * 3600)
    
    payload = {
        "username": sso_user.username,
        "uid": sso_user.uid,
        "iat": current_time,  # issued at
        "exp": expire_time,   # expires at
        "type": "access_token"
    }
    
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    logger.debug(f"为用户 {sso_user.username} 创建访问令牌")
    
    return token


def verify_user_token(token: str) -> Optional[UserToken]:
    """
    验证用户访问令牌
    
    Args:
        token: JWT访问令牌
        
    Returns:
        Optional[UserToken]: 验证成功返回用户令牌信息，失败返回None
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # 检查令牌类型
        if payload.get("type") != "access_token":
            logger.warning("令牌类型不正确")
            return None
        
        # 检查必要字段
        username = payload.get("username")
        uid = payload.get("uid")
        issued_at = payload.get("iat")
        expires_at = payload.get("exp")
        
        if not all([username, uid, issued_at, expires_at]):
            logger.warning("令牌缺少必要字段")
            return None
        
        # 检查是否过期
        current_time = int(time.time())
        if current_time > expires_at:
            logger.warning(f"令牌已过期: {expires_at} < {current_time}")
            return None
        
        return UserToken(
            username=username,
            uid=uid,
            issued_at=issued_at,
            expires_at=expires_at
        )
        
    except jwt.ExpiredSignatureError:
        logger.warning("JWT令牌已过期")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"JWT令牌无效: {e}")
        return None
    except Exception as e:
        logger.error(f"验证令牌时发生异常: {e}")
        return None


def extract_token_from_request(request) -> Optional[str]:
    """
    从请求中提取访问令牌
    
    支持从Cookie和Authorization头中提取令牌
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Optional[str]: 访问令牌，未找到返回None
    """
    # 1. 尝试从Cookie中获取
    token = request.cookies.get("Authorization")
    if token:
        logger.debug("从Cookie中获取到令牌")
        return token
    
    # 2. 尝试从Authorization头中获取
    auth_header = request.headers.get("Authorization")
    if auth_header:
        # 支持 "Bearer <token>" 格式
        if auth_header.startswith("Bearer "):
            token = auth_header[7:]  # 去掉 "Bearer " 前缀
            logger.debug("从Authorization头中获取到令牌")
            return token
        # 直接使用整个头部值作为令牌
        else:
            logger.debug("从Authorization头中获取到令牌（无Bearer前缀）")
            return auth_header
    
    logger.debug("未找到访问令牌")
    return None


def get_current_user(request) -> Optional[AuthUser]:
    """
    获取当前认证用户信息
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Optional[AuthUser]: 当前用户信息，未认证返回None
    """
    # 1. 提取令牌
    token = extract_token_from_request(request)
    if not token:
        return None
    
    # 2. 验证令牌
    user_token = verify_user_token(token)
    if not user_token:
        return None
    
    # 3. 构建认证用户信息
    return AuthUser(
        username=user_token.username,
        uid=user_token.uid,
        is_authenticated=True,
        login_time=None,  # 可以从令牌的iat字段获取
        last_activity=None  # 可以从缓存或数据库获取
    )


def generate_state_token(user_id: str, timestamp: int) -> str:
    """
    生成状态令牌，用于防止CSRF攻击
    
    Args:
        user_id: 用户ID
        timestamp: 时间戳
        
    Returns:
        str: 状态令牌
    """
    data = f"{user_id}:{timestamp}:{JWT_SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()[:16]


def verify_state_token(token: str, user_id: str, timestamp: int, max_age: int = 300) -> bool:
    """
    验证状态令牌
    
    Args:
        token: 状态令牌
        user_id: 用户ID
        timestamp: 时间戳
        max_age: 最大有效期（秒）
        
    Returns:
        bool: 验证是否成功
    """
    # 检查时间戳是否在有效期内
    current_time = int(time.time())
    if current_time - timestamp > max_age:
        logger.warning(f"状态令牌已过期: {timestamp}")
        return False
    
    # 生成期望的令牌并比较
    expected_token = generate_state_token(user_id, timestamp)
    if token != expected_token:
        logger.warning("状态令牌验证失败")
        return False
    
    return True


def is_safe_redirect_url(url: str, allowed_hosts: Optional[list] = None) -> bool:
    """
    检查重定向URL是否安全
    
    Args:
        url: 重定向URL
        allowed_hosts: 允许的主机列表
        
    Returns:
        bool: URL是否安全
    """
    if not url:
        return False
    
    # 相对路径被认为是安全的
    if url.startswith('/') and not url.startswith('//'):
        return True
    
    # 如果没有指定允许的主机，只允许相对路径
    if not allowed_hosts:
        return False
    
    # 检查URL是否在允许的主机列表中
    from urllib.parse import urlparse
    
    try:
        parsed = urlparse(url)
        if parsed.hostname in allowed_hosts:
            return True
    except Exception as e:
        logger.warning(f"解析重定向URL失败: {url}, 错误: {e}")
    
    return False


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    遮蔽敏感数据
    
    Args:
        data: 原始数据
        mask_char: 遮蔽字符
        visible_chars: 可见字符数量
        
    Returns:
        str: 遮蔽后的数据
    """
    if not data or len(data) <= visible_chars:
        return mask_char * len(data) if data else ""
    
    visible_part = data[:visible_chars]
    masked_part = mask_char * (len(data) - visible_chars)
    
    return visible_part + masked_part
