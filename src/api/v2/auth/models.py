"""
SSO认证相关的数据模型
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class SSOUser(BaseModel):
    """SSO用户信息模型"""
    
    username: str = Field(..., description="用户名")
    uid: str = Field(..., description="用户ID")
    sid: str = Field(..., description="会话ID")
    timestamp: int = Field(..., description="票据生成时间戳")
    feature: str = Field(..., description="特征码")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SSOValidationResult(BaseModel):
    """SSO票据验证结果模型"""
    
    success: bool = Field(..., description="验证是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="验证成功时的数据")
    errors: Optional[str] = Field(None, description="验证失败时的错误信息")
    code: Optional[int] = Field(None, description="错误码")


class UserToken(BaseModel):
    """用户访问令牌模型"""
    
    username: str = Field(..., description="用户名")
    uid: str = Field(..., description="用户ID")
    issued_at: int = Field(..., description="令牌签发时间戳")
    expires_at: int = Field(..., description="令牌过期时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AuthUser(BaseModel):
    """认证用户信息模型"""
    
    username: str = Field(..., description="用户名")
    uid: str = Field(..., description="用户ID")
    is_authenticated: bool = Field(True, description="是否已认证")
    login_time: Optional[datetime] = Field(None, description="登录时间")
    last_activity: Optional[datetime] = Field(None, description="最后活动时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LoginRequest(BaseModel):
    """登录请求模型"""
    
    ticket: str = Field(..., description="SSO登录票据")
    target: str = Field("/", description="登录成功后跳转地址")


class LoginResponse(BaseModel):
    """登录响应模型"""
    
    success: bool = Field(..., description="登录是否成功")
    message: str = Field(..., description="响应消息")
    redirect_url: Optional[str] = Field(None, description="重定向地址")
    user: Optional[AuthUser] = Field(None, description="用户信息")


class LogoutRequest(BaseModel):
    """登出请求模型"""
    
    redirect_url: Optional[str] = Field(None, description="登出后重定向地址")


class LogoutResponse(BaseModel):
    """登出响应模型"""
    
    success: bool = Field(..., description="登出是否成功")
    message: str = Field(..., description="响应消息")
    redirect_url: Optional[str] = Field(None, description="重定向地址")


class UserProfile(BaseModel):
    """用户档案模型"""
    
    username: str = Field(..., description="用户名")
    uid: str = Field(..., description="用户ID")
    display_name: Optional[str] = Field(None, description="显示名称")
    email: Optional[str] = Field(None, description="邮箱地址")
    department: Optional[str] = Field(None, description="部门")
    role: Optional[str] = Field(None, description="角色")
    permissions: Optional[list] = Field(default_factory=list, description="权限列表")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SSOConfig(BaseModel):
    """SSO配置模型"""
    
    sc: str = Field(..., description="应用代码")
    name: str = Field(..., description="应用名称")
    environment: str = Field(..., description="环境")
    sso_domain: str = Field(..., description="SSO域名")
    validate_api: str = Field(..., description="票据验证API")
    logout_api: str = Field(..., description="登出API")
    private_key: str = Field(..., description="RSA私钥")
    salt: str = Field(..., description="签名盐值")
    
    class Config:
        # 敏感信息不在日志中显示
        fields = {
            'private_key': {'write_only': True},
            'salt': {'write_only': True}
        }
