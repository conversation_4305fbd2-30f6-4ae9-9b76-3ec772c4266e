"""
用户管理接口

提供用户信息查询、权限管理等功能
"""

from typing import Optional
from fastapi import Depends, HTTPException, Request
from loguru import logger

from src.infra.web.http import http_server
from src.infra.web.schema import HttpResponse
from .models import AuthUser, UserProfile
from .middleware import require_auth, optional_auth

__all__ = ["user_router"]

user_router = http_server.new_router()


@user_router.get("/profile")
async def get_user_profile(
    current_user: AuthUser = Depends(require_auth)
) -> HttpResponse:
    """
    获取当前用户档案信息
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        HttpResponse: 用户档案信息
    """
    try:
        logger.info(f"获取用户档案: {current_user.username}")
        
        # 构建用户档案信息
        # 在实际应用中，这些信息可能来自数据库或其他用户管理系统
        user_profile = UserProfile(
            username=current_user.username,
            uid=current_user.uid,
            display_name=current_user.username,  # 可以从用户系统获取真实姓名
            email=f"{current_user.username}@company.com",  # 可以从用户系统获取
            department="运维部",  # 可以从用户系统获取
            role="运维工程师",  # 可以从权限系统获取
            permissions=[  # 可以从权限系统获取
                "chat:read",
                "chat:write", 
                "system:monitor",
                "alert:view"
            ]
        )
        
        return HttpResponse(data=user_profile.dict())
        
    except Exception as e:
        logger.exception(f"获取用户档案失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户档案失败")


@user_router.get("/info")
async def get_user_info(
    current_user: AuthUser = Depends(require_auth)
) -> HttpResponse:
    """
    获取当前用户基本信息
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        HttpResponse: 用户基本信息
    """
    try:
        logger.info(f"获取用户信息: {current_user.username}")
        
        user_info = {
            "username": current_user.username,
            "uid": current_user.uid,
            "is_authenticated": current_user.is_authenticated,
            "login_time": current_user.login_time,
            "last_activity": current_user.last_activity
        }
        
        return HttpResponse(data=user_info)
        
    except Exception as e:
        logger.exception(f"获取用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户信息失败")


@user_router.get("/permissions")
async def get_user_permissions(
    current_user: AuthUser = Depends(require_auth)
) -> HttpResponse:
    """
    获取当前用户权限列表
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        HttpResponse: 用户权限列表
    """
    try:
        logger.info(f"获取用户权限: {current_user.username}")
        
        # 在实际应用中，权限信息应该从权限管理系统获取
        # 这里提供一个示例权限列表
        permissions = [
            {
                "code": "chat:read",
                "name": "聊天查看",
                "description": "查看聊天记录和对话"
            },
            {
                "code": "chat:write", 
                "name": "聊天发送",
                "description": "发送消息和创建对话"
            },
            {
                "code": "system:monitor",
                "name": "系统监控",
                "description": "查看系统监控信息"
            },
            {
                "code": "alert:view",
                "name": "告警查看",
                "description": "查看系统告警信息"
            }
        ]
        
        return HttpResponse(data={
            "user": current_user.username,
            "permissions": permissions,
            "total": len(permissions)
        })
        
    except Exception as e:
        logger.exception(f"获取用户权限失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户权限失败")


@user_router.post("/check-permission")
async def check_user_permission(
    request: Request,
    current_user: AuthUser = Depends(require_auth)
) -> HttpResponse:
    """
    检查用户是否具有指定权限
    
    Args:
        request: FastAPI请求对象
        current_user: 当前认证用户
        
    Returns:
        HttpResponse: 权限检查结果
    """
    try:
        # 获取请求体
        body = await request.json()
        permission_code = body.get("permission")
        
        if not permission_code:
            raise HTTPException(status_code=400, detail="权限代码不能为空")
        
        logger.info(f"检查用户权限: {current_user.username} -> {permission_code}")
        
        # 在实际应用中，这里应该查询权限管理系统
        # 目前简化处理，假设用户有基本权限
        user_permissions = [
            "chat:read",
            "chat:write",
            "system:monitor", 
            "alert:view"
        ]
        
        has_permission = permission_code in user_permissions
        
        return HttpResponse(data={
            "user": current_user.username,
            "permission": permission_code,
            "has_permission": has_permission
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"检查用户权限失败: {e}")
        raise HTTPException(status_code=500, detail="检查用户权限失败")


@user_router.get("/session")
async def get_user_session(
    current_user: Optional[AuthUser] = Depends(optional_auth)
) -> HttpResponse:
    """
    获取用户会话信息
    
    Args:
        current_user: 当前用户（可选）
        
    Returns:
        HttpResponse: 会话信息
    """
    try:
        if not current_user:
            return HttpResponse(data={
                "authenticated": False,
                "message": "用户未登录"
            })
        
        logger.info(f"获取用户会话: {current_user.username}")
        
        session_info = {
            "authenticated": True,
            "user": {
                "username": current_user.username,
                "uid": current_user.uid
            },
            "login_time": current_user.login_time,
            "last_activity": current_user.last_activity
        }
        
        return HttpResponse(data=session_info)
        
    except Exception as e:
        logger.exception(f"获取用户会话失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户会话失败")


@user_router.post("/update-activity")
async def update_user_activity(
    current_user: AuthUser = Depends(require_auth)
) -> HttpResponse:
    """
    更新用户最后活动时间
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        HttpResponse: 更新结果
    """
    try:
        from datetime import datetime
        
        logger.debug(f"更新用户活动时间: {current_user.username}")
        
        # 在实际应用中，这里应该更新数据库或缓存中的用户活动时间
        current_time = datetime.now()
        
        return HttpResponse(data={
            "user": current_user.username,
            "last_activity": current_time.isoformat(),
            "message": "活动时间已更新"
        })
        
    except Exception as e:
        logger.exception(f"更新用户活动时间失败: {e}")
        raise HTTPException(status_code=500, detail="更新用户活动时间失败")


@user_router.get("/health")
async def user_health_check() -> HttpResponse:
    """
    用户模块健康检查
    
    Returns:
        HttpResponse: 健康检查结果
    """
    try:
        return HttpResponse(data={
            "status": "healthy",
            "module": "user",
            "message": "用户模块运行正常"
        })
        
    except Exception as e:
        logger.exception(f"用户模块健康检查失败: {e}")
        return HttpResponse(
            code=1,
            message=f"用户模块健康检查失败: {str(e)}",
            data={"status": "unhealthy"}
        )
