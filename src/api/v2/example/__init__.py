from sqlalchemy import select

from src.core.repo.postgre.example import Parent
from src.infra.app import app
from src.infra.web.http import http_server

__all__ = ["example_router"]

example_router = http_server.new_router()


@example_router.post("/query")
async def query():
    # with app.orm_session() as sess:
    #     stmt = select(Parent)
    #     result = await sess.execute(stmt)
    #     parents = result.unique().scalars().all()
    #     return [parent.to_dict() for parent in parents]
    pass
