from typing import Callable, Literal, List

from apscheduler.job import Job
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from settings import get_or_creat_settings_ins

# trigger 的参数参考
_ = CronTrigger
_ = DateTrigger
_ = IntervalTrigger


class AppScheduler:
    INTERVAL = "interval"
    CRON = "cron"
    DATE = "date"

    def __init__(self):
        self.config = get_or_creat_settings_ins()
        self._scheduler: BackgroundScheduler = self._init_scheduler()

    def _init_scheduler(self) -> BackgroundScheduler:
        pgsql_url = (
            f"postgresql+psycopg://"
            f"{self.config.postgres.user}:{self.config.postgres.password}"
            f"@{self.config.postgres.host}:{self.config.postgres.port}/{self.config.postgres.database}"
        )
        config = {
            "apscheduler.jobstores.default": {"type": "sqlalchemy", "url": pgsql_url},
            "apscheduler.executors.default": {
                "class": "apscheduler.executors.pool:ThreadPoolExecutor",
                "max_workers": "32",
            },
        }
        return BackgroundScheduler(config)

    @classmethod
    def job_id(cls, func_name: str) -> str:
        return f"job@{func_name}"

    def add_job(
        self,
        func: Callable,
        trigger: Literal["interval", "cron", "date"],
        id=None,
        replace_existing=True,
        **kwargs_,
    ):
        if id is None:
            id = self.job_id(func.__name__)
        self._scheduler.add_job(
            func, trigger, id=id, replace_existing=replace_existing, **kwargs_
        )

    def modify_job(self, id: str, **kwargs_):
        self._scheduler.modify_job(id, **kwargs_)

    def remove_job(self, id: str):
        self._scheduler.remove_job(id)

    def get_jobs(self) -> List[Job]:
        return self._scheduler.get_jobs()

    def pause(self):
        self._scheduler.pause()

    def resume(self):
        self._scheduler.resume()

    def start(self):
        # 暂停状态下启动调度程序，即没有第一次唤醒调用
        self._scheduler.start()

    def stop(self):
        self._scheduler.shutdown()


def ok():
    logger.info("Scheduler is ready for work...")


scheduler = AppScheduler()  # run once
# scheduler.add_job(ok, 'date', run_date=datetime.now() + timedelta(seconds=5), id=scheduler.job_id("ok"))
