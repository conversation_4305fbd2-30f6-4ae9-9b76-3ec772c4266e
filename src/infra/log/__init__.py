import argparse
import os
import sys

from loguru import logger

from settings import Settings, get_or_creat_settings_ins

__all__ = ["logger"]

def init_logger():
    config = get_or_creat_settings_ins()
    logger.remove()
    parser = argparse.ArgumentParser()
    parser.add_argument("--log", help="log level", default=config.app.log_level)
    args, _ = parser.parse_known_args()
    logger.add(sys.stdout, level=args.log)


init_logger()






