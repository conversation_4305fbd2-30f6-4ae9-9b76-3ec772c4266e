import asyncio
import os
from functools import cache
from typing import Any, List

from src.schema import RUNTIME_ENV, consts, env

__all__ = [
    "get_work_dir",
    "get_current_version",
    "get_event_loop",
    "safe_close_event_loop",
    "ThreadSafeDict",
    "get_current_env",
]


@cache
def get_current_env() -> str:
    """
    获取当前环境
    """
    return os.environ.get(env.RUNTIME_ENV) or consts.DEFAULT_ENV


@cache
def get_user_email_and_name() -> tuple[str, str]:
    """
    获取当前用户邮箱和名称
    """
    email = os.environ.get(env.USER_EMAIL) or ""
    name = os.environ.get(env.USER_NAME) or ""
    return email, name


@cache
def get_work_dir() -> str:
    """
    获取工作目录
    """
    return os.environ.get(env.WORK_DIR) or os.getcwd()


@cache
def get_toml_dir() -> List[str]:
    """
    获取toml配置目录
    """
    env = os.environ.get(RUNTIME_ENV, "noprod")
    return [
        os.path.join("settings/toml", "common.toml"),
        os.path.join("settings/toml", f"{env}.toml"),
        os.path.join("settings/toml", "local.toml"),
    ]


@cache
def get_current_version() -> str:
    """
    获取当前版本
    """
    return os.environ.get(env.CURRENT_VERSION) or "0.0.0"


def get_event_loop() -> asyncio.AbstractEventLoop:
    try:
        asyncio.set_event_loop_policy(asyncio.DefaultEventLoopPolicy())
        return asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop


def safe_close_event_loop() -> None:
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        return  # 没有运行中的事件循环

    if loop.is_closed():
        return  # 循环已关闭

    # 取消所有任务
    tasks = [t for t in asyncio.all_tasks(loop) if not t.done()]
    if not tasks:
        return

    for task in tasks:
        task.cancel()

    # 安排停止和关闭
    def stop_loop() -> None:
        try:
            # 收集取消后的任务异常
            loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
            loop.stop()
            # 只有在不是由 asyncio.run() 启动时才手动关闭
            if not isinstance(getattr(loop, "_closed", False), bool):
                loop.close()
        finally:
            # 确保资源释放
            loop.run_until_complete(loop.shutdown_asyncgens())
            loop.run_until_complete(loop.shutdown_default_executor())

    try:
        loop.call_soon_threadsafe(stop_loop)
    except Exception as e:
        print(f"Failed to stop event loop: {e}")


import threading
from collections.abc import MutableMapping


class ThreadSafeDict(MutableMapping):
    """线程安全的字典实现，支持大多数标准字典操作"""

    def __init__(self, *args, **kwargs):
        self._dict = dict(*args, **kwargs)
        self._lock = threading.RLock()  # 使用可重入锁，避免死锁

    def __getitem__(self, key):
        with self._lock:
            return self._dict[key]

    def __setitem__(self, key, value):
        with self._lock:
            self._dict[key] = value
            # print(f"ThreadSafeDict: Set {key} = {value}")

    def __delitem__(self, key):
        with self._lock:
            del self._dict[key]

    def __iter__(self):
        with self._lock:
            return iter(self._dict.copy())  # 返回副本的迭代器

    def __len__(self):
        with self._lock:
            return len(self._dict)

    def __repr__(self):
        with self._lock:
            return f"ThreadSafeDict({self._dict!r})"

    def get(self, key, default=None):
        with self._lock:
            return self._dict.get(key, default)

    def keys(self):
        with self._lock:
            return list(self._dict.keys())

    def values(self):
        with self._lock:
            return list(self._dict.values())

    def items(self):
        with self._lock:
            return list(self._dict.items())

    def pop(self, key, default=None):
        with self._lock:
            return self._dict.pop(key, default)

    def popitem(self):
        with self._lock:
            return self._dict.popitem()

    def clear(self):
        with self._lock:
            self._dict.clear()

    def update(self, *args, **kwargs):
        with self._lock:
            self._dict.update(*args, **kwargs)

    def setdefault(self, key, default=None):
        with self._lock:
            return self._dict.setdefault(key, default)

    def copy(self):
        with self._lock:
            return ThreadSafeDict(self._dict.copy())

    def __contains__(self, key):
        with self._lock:
            return key in self._dict

    def __eq__(self, other):
        with self._lock:
            if isinstance(other, ThreadSafeDict):
                with other._lock:
                    return self._dict == other._dict
            return self._dict == other

    # 原子操作方法
    def get_or_set(self, key, default_value) -> Any:
        """原子操作：获取键值，如果不存在则设置默认值"""
        with self._lock:
            if key not in self._dict:
                self._dict[key] = default_value
            return self._dict[key]
