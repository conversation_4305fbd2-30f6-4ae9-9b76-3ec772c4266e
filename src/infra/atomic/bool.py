import threading
from typing import Optional


class AtomicBool:
    """
    线程安全的布尔类型原子变量实现。
    提供了对布尔值的原子操作，确保在多线程环境下的安全访问。
    """

    def __init__(self, initial_value: bool = False):
        """
        初始化原子布尔变量。

        Args:
            initial_value: 初始布尔值，默认为False
        """
        self._value = initial_value
        self._lock = threading.Lock()

    def get(self) -> bool:
        """
        获取当前布尔值。

        Returns:
            当前布尔值
        """
        with self._lock:
            return self._value

    def set(self, new_value: bool) -> None:
        """
        设置新的布尔值。

        Args:
            new_value: 要设置的新布尔值
        """
        with self._lock:
            self._value = new_value

    def get_and_set(self, new_value: bool) -> bool:
        """
        原子性地设置新值并返回旧值。

        Args:
            new_value: 要设置的新布尔值

        Returns:
            设置前的旧布尔值
        """
        with self._lock:
            old_value = self._value
            self._value = new_value
            return old_value

    def compare_and_set(self, expected_value: bool, new_value: bool) -> bool:
        """
        原子性地比较当前值与期望值，如果相等则设置为新值。

        Args:
            expected_value: 期望的当前值
            new_value: 要设置的新值

        Returns:
            如果当前值等于期望值并成功设置新值，则返回True；否则返回False
        """
        with self._lock:
            if self._value == expected_value:
                self._value = new_value
                return True
            return False

    def get_and_invert(self) -> bool:
        """
        原子性地获取当前值并将其反转（True变为False，False变为True）。

        Returns:
            反转前的布尔值
        """
        with self._lock:
            old_value = self._value
            self._value = not self._value
            return old_value

    def __bool__(self) -> bool:
        """
        允许将AtomicBool实例直接用于布尔上下文。

        Returns:
            当前布尔值
        """
        return self.get()

    def __str__(self) -> str:
        """
        返回AtomicBool的字符串表示。

        Returns:
            当前布尔值的字符串表示
        """
        return str(self.get())

    def __repr__(self) -> str:
        """
        返回AtomicBool的详细字符串表示。

        Returns:
            AtomicBool的详细字符串表示
        """
        return f"AtomicBool({self.get()})"
