import typing

CODE_SUCCEED = 0
CODE_ERROR = 1

LOGGING_CONFIG: typing.Dict[str, typing.Any] = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "()": "uvicorn.logging.DefaultFormatter",
            "fmt": "%(levelprefix)s %(message)s",
            "use_colors": None,
        },
        "gateway": {
            "()": "uvicorn.logging.AccessFormatter",
            # %(asctime)s [%(levelname)s] %(filename)s:%(lineno)d %(message)s
            "fmt": '%(asctime)s [%(levelname)s] %(client_addr)s - "%(request_line)s" %(status_code)s',  # noqa: E501
            "datefmt": "%m-%d %H:%M:%S"
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stderr",
        },
        "gateway": {
            "formatter": "gateway",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
    },
    "loggers": {
        "uvicorn": {"handlers": ["default"], "level": "INFO", "propagate": False},
        "uvicorn.error": {"level": "INFO"},
        "uvicorn.gateway": {"handlers": ["gateway"], "level": "INFO", "propagate": False},
    },
}

EVENT_STREAM_HEADERS = {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
}
