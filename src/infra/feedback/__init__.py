import json
from datetime import datetime
from typing import Optional

from langchain_core.runnables import RunnableConfig
from loguru import logger
from pydantic import BaseModel, Field

from src.core.repo.postgre.indicatorCollection import IndicatorCollection
from src.infra.app import app


class UserTaskFeedBack(BaseModel):
    session_id: str = Field(..., description="会话id")
    user_id: str = Field(..., description="用户id")
    end_type: str = Field("", description="结束类型")
    feedback: Optional[str] = Field("", description="用户反馈")

    @classmethod
    def update_field(cls, config: RunnableConfig, end_type: str):
        try:
            session_id = config["configurable"]["thread_id"]
            """Generic method to update any field"""
            task = cls.get_working_tasks(session_id)
            task.end_type = end_type
            task.save_to_redis()
            task.save_feedback()
        except Exception as e:
            logger.error(f"Error updating UserTaskFeedBack: {e}")

    @classmethod
    def get_or_create(cls, config: RunnableConfig) -> Optional["UserTaskFeedBack"]:
        try:
            session_id = config["configurable"]["thread_id"]
            user_id = config["configurable"]["session_context"].user_info.id
            task = cls.get_working_tasks(session_id)
            if task:
                update_task = UserTaskFeedBack(
                    session_id=task.session_id,
                    user_id=task.user_id,
                )
                update_task.save_to_redis()
                return update_task

            if not user_id:
                return None

            new_task = cls(
                session_id=session_id,
                user_id=user_id,
            )
            if new_task.save_to_redis():
                return new_task
        except Exception as e:
            logger.error(f"Error creating UserTaskFeedBack: {e}")
        return None

    @classmethod
    def get_working_tasks(cls, session_id: str) -> Optional["UserTaskFeedBack"]:
        if not app.redis_cli:
            raise ValueError("Redis client is not initialized.")
        if not session_id:
            raise ValueError("session_id is required.")
        data = app.redis_cli.get("feedback-" + session_id)
        if not data:
            return None
        try:
            # Parse the JSON data and create a UserTask instance
            return cls.parse_raw(data)
        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error deserializing UserTask: {e}")
            return None

    def save_to_redis(self) -> bool:
        if not self.session_id:
            raise ValueError("session_id is required.")
        try:
            data = self.json()
            return bool(app.redis_cli.set("feedback-" + self.session_id, data, ex=600))
        except Exception as e:
            print(f"Error saving UserTask to Redis: {e}")
            return False

    def save_feedback(self):
        try:
            with app.orm_session() as sess:
                # 查询是否已有该 session_id 的记录
                existing = (
                    sess.query(IndicatorCollection)
                    .filter_by(session_id=self.session_id)
                    .first()
                )
                if existing:
                    existing.end_type = self.end_type
                else:
                    feedback = IndicatorCollection(
                        session_id=self.session_id,
                        user_id=self.user_id,
                        end_type=self.end_type,
                        feedback=self.feedback,
                    )
                    sess.add(feedback)
                sess.commit()
        except Exception as e:
            logger.error(f"Error saving feedback to database: {e}")

    @staticmethod
    def update_feedback(session_id: str, feed_message: str):
        try:
            with app.orm_session() as sess:
                feedbacks = (
                    sess.query(IndicatorCollection)
                    .filter_by(session_id=session_id)
                    .all()
                )
                if feedbacks and len(feedbacks) > 0:
                    for feedback in feedbacks:
                        feedback.feedback = (
                            feed_message if feed_message else feedback.feedback
                        )
                        feedback.end_type = (
                            feedback.end_type if feed_message else "final"
                        )
                        feedback.feedback_time = datetime.now()
                        sess.add(feedback)
                sess.commit()
        except Exception as e:
            logger.error(f"Error saving feedback to database: {e}")

    @staticmethod
    def error_feedback(session_id: str, feed_message: str):
        try:
            with app.orm_session() as sess:
                feedbacks = (
                    sess.query(IndicatorCollection)
                    .filter_by(session_id=session_id)
                    .all()
                )
                if feedbacks and len(feedbacks) > 0:
                    for feedback in feedbacks:
                        feedback.feedback = (
                            feed_message if feed_message else feedback.feedback
                        )
                        feedback.end_type = "error"
                        feedback.feedback_time = datetime.now()
                        sess.add(feedback)
                sess.commit()
        except Exception as e:
            logger.error(f"Error saving feedback to database: {e}")
