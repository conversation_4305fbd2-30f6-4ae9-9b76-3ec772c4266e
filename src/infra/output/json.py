from langchain_core.output_parsers import BaseOutputParser, JsonOutputParser

JSON_FORMAT_INSTRUCTIONS = """The output should be formatted as a JSON instance that conforms to the JSON schema below.

Here is the output schema:
```
{schema}
```"""


class JsonMultiformatOutputParser(JsonOutputParser):
    """
    A parser that can parse the output of a model into a json object.
    """

    format_instructions: str = ""

    def get_format_instructions(self) -> str:
        if not self.format_instructions:
            return JSON_FORMAT_INSTRUCTIONS.format(schema=self.format_instructions)
        return super().get_format_instructions()

    @property
    def _type(self) -> str:
        return "multi_json_output_parser"
