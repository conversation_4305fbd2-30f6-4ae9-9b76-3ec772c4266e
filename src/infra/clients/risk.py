from typing import Any, Optional, List
from urllib.parse import urljoin

from pydantic import BaseModel, Field, PrivateAttr
from requests import Session, session
from loguru import logger

__all__ = ["RiskClient"]


class RiskClient(BaseModel):
    base_url: str = Field(default="https://tt-telemetry-risk.ttyuyin.com", description="risk service host")
    _session: Optional[Session] = PrivateAttr(default=None)

    def __init__(self, **data: Any):
        super().__init__(**data)
        self._session = session()

    def get_risk_info(self, page_num: int = 1, page_limit: int = 1000,
                      index_ids: Optional[List[str]] = None,
                      username: str = None) -> dict:
        """获取风险信息

        Args:
            page_num (int, optional): 页码. Defaults to 1.
            page_limit (int, optional): 每页数量. Defaults to 10.
            index_ids (List[str], optional): 索引ID列表.
            username (str, optional): 用户名. Defaults to None.

        Returns:
            dict: 风险信息响应数据
        """
        if index_ids is None:
            index_ids = [
                "63a5c252903b45594ff6b2ef",
                "63a5bea4903b45594ff6b27a",
                "63a5c274903b45594ff6b2f4",
                "6405ed52bf4c48c1a1b23aa7"
            ]

        assert self._session

        params = {"page_num": page_num, "page_limit": page_limit, "username": username, "index_id[]": index_ids,
                  "risk_level[]": ["中", "高"]}

        url = urljoin(self.base_url, "/risk/risk")
        response = self._session.get(url=url, params=params)
        data = response.json()
        risk_level_priority = {
            "高": 1,
            "中": 2,
        }
        if data["data"]["records"] is None:
            return data
        sorted_list = sorted(
            data["data"]["records"],
            key=lambda x: risk_level_priority.get(x.get("risk_level", ""), 99)
        )
        data["data"]["records"] = sorted_list

        high_level_count = len([record for record in sorted_list if record["risk_level"] == "高"])
        middle_level_count = len([record for record in sorted_list if record["risk_level"] == "中"])
        logger.info(f"High level count: {high_level_count}, Middle level count: {middle_level_count}")
        data["data"]["high_level_count"] = high_level_count
        data["data"]["middle_level_count"] = middle_level_count
        data["data"]["records"] = data["data"]["records"][:10]
        return data
