import json
from datetime import datetime
from typing import Dict, Any

from loguru import logger
from sqlalchemy import create_engine, Engine, Column, Integer, DateTime, Date
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import DeclarativeBase, sessionmaker, Session

from settings import Settings


def init_db_engine(config: Settings) -> Engine:
    link = f"postgresql+psycopg://{config.postgres.user}:{config.postgres.password}@{config.postgres.host}:{config.postgres.port}/{config.postgres.database}"
    engine = create_engine(link,
                           pool_pre_ping=True,
                           pool_size=config.postgres.min_size,
                           pool_recycle=config.postgres.pool_recycle,
                           max_overflow=config.postgres.max_size,
                           echo=config.postgres.debug,
                           )
    try:
        engine.connect()
        logger.info("Database orm connection successful")
    except Exception as e:
        raise Exception(f"Database orm connection failed: {e}")

    return engine


class BaseModel(DeclarativeBase):
    __allow_unmapped__ = True

    def to_dict(self) -> Dict[str, Any]:
        res = {}
        for c in self.__table__.columns:
            res[c.name] = getattr(self, c.name)
        return res

    def to_json(self) -> str:
        res = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value, datetime):
                value = value.timestamp() * 1000
            res[c.name] = value
        return json.dumps(res)


class IdModelMixin(BaseModel):
    __abstract__ = True
    id = Column(Integer, primary_key=True, autoincrement=True)


class DateTimeModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(DateTime, default=datetime.now, nullable=False, comment='创建日期')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新日期')


class DateModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(Date, default=datetime.now, nullable=False, comment='创建日期')
    updated_at = Column(Date, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新日期')


SessionLocal: sessionmaker


def init_orm_session(engine: Engine):
    global SessionLocal
    SessionLocal = sessionmaker(bind=engine, autoflush=True)
    


class OrmSessionContext:
    def __init__(self):
        self.sess = None
        try:
            self.sess = SessionLocal()
        except OperationalError as e:
            logger.exception(f"db session init failed. {e}")
            if "Postgres server has gone away" in str(e) or "Broken pipe" in str(e):
                self.sess = SessionLocal()

    def __enter__(self) -> Session:
        return self.sess

    def __exit__(self, exc_type, exc_value, traceback):
        self.sess.close()
