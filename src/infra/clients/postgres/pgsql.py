from functools import cache
from typing import Optional

import psycopg
from loguru import logger
from psycopg_pool import ConnectionPool, AsyncConnectionPool
from settings import get_or_creat_settings_ins

__all__ = [
    "get_sync_pg_cli", "get_async_pg_cli",
]


@cache
def get_sync_pg_cli() -> Optional[ConnectionPool]:
    config = get_or_creat_settings_ins()

    try:
        pool = ConnectionPool(
            conninfo=(
                f"postgresql://{config.postgres.user}:{config.postgres.password}"
                f"@{config.postgres.host}:{config.postgres.port}"
                f"/{config.postgres.database}"
            ),
            min_size=config.postgres.min_size,
            max_size=config.postgres.max_size,
        )
        pool.wait()  # 等待连接池初始化
        # 验证连接有效性
        with pool.connection() as conn:

            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                logger.debug("Sync Postgres connection test succeeded")

        logger.info("Sync Postgres connection pool initialized (psycopg3)")
        return pool
    except psycopg.Error as e:
        logger.error(f"Sync Postgres connection failed: {str(e)}")
        return None


@cache
async def get_async_pg_cli() -> Optional[AsyncConnectionPool]:
    """初始化异步连接池"""
    config = get_or_creat_settings_ins()

    try:
        pool = AsyncConnectionPool(
            conninfo=(
                f"postgresql://{config.postgres.user}:{config.postgres.password}"
                f"@{config.postgres.host}:{config.postgres.port}"
                f"/{config.postgres.database}"
            ),
            min_size=config.postgres.min_size,
            max_size=config.postgres.max_size,
            open=False,
            kwargs={
                "autocommit": False,
            },

        )
        await pool.open()
        logger.info("Async Postgres connection pool initialized (psycopg3)")
        return pool
    except psycopg.Error as e:
        logger.error(f"Async Postgres connection failed: {str(e)}")
        return None
