# from langchain_huggingface import HuggingFaceEndpointEmbeddings


from settings import get_or_creat_settings_ins
from src.infra.clients.embedding_factory import get_embedding_client

config = get_or_creat_settings_ins()

# HTTPEmbeddingClient 相关代码已不再使用，已迁移到 Qwen3Embedding
# class HTTPEmbeddingClientConfig(BaseModel):
#     model: str = config.embedding.model  # 例如 http://1xxx:xx
#     api_key: str = None  # 如果有认证
#     timeout: int = 30  # 超时时间
#     batch_size: int = 32  # 批处理大小


# class HTTPEmbeddingClient:
#     def __init__(self, config: HTTPEmbeddingClientConfig):
#         self.config = config
#         self.headers = {"Content-Type": "application/json"}
#         if self.config.api_key:
#             self.headers["Authorization"] = f"Bearer {self.config.api_key}"

#     def embed(self, texts: List[str]) -> List[List[float]]:
#         """同步请求"""
#         # 分批次处理大输入
#         embeddings = []
#         for i in range(0, len(texts), self.config.batch_size):
#             batch = texts[i : i + self.config.batch_size]
#             response = requests.post(
#                 str(self.config.model),
#                 headers=self.headers,
#                 json={"inputs": batch},  # 根据实际 API 调整请求体结构
#                 timeout=self.config.timeout,
#             )
#             response.raise_for_status()  # 自动抛出 HTTP 错误
#             embeddings.extend(response.json())  # 根据实际响应结构调整
#         return embeddings

# 使用新的embedding工厂创建客户端
embedding_client = get_embedding_client(is_query=False)

if __name__ == "__main__":
    # 测试代码
    texts = ["查询 k8s 资源，xxx pod 的信息"]
    embeddings = embedding_client.embed_documents(texts)
    print(embeddings[0])
