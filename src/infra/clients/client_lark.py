from functools import cache
from typing import List

import lark_oapi as lark

from settings import get_or_creat_settings_ins

__all__ = ["get_lark_client"]

from src.core.schema.chatbot import RobotType

config = get_or_creat_settings_ins()

@cache
def get_lark_client(chatbot_type: RobotType = RobotType.Chat) -> lark.Client:
    chatbot_types: List = list(RobotType)
    for ct in chatbot_types:
        chatbot_config = getattr(config.lark, ct.value)
        if chatbot_type == chatbot_config.type:
            return (
                lark.Client.builder()
                .app_id(chatbot_config.app_id)
                .app_secret(chatbot_config.app_secret)
                .timeout(30)
                .build()
            )

    raise Exception("Invalid chatbot type")
