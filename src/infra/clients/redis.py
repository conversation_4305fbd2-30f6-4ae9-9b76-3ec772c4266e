from functools import cache

from typing import Optional, Dict, Any
import redis
from redis.asyncio import Redis as AsyncRedis
from loguru import logger
from settings import Settings, get_or_creat_settings_ins

__all__ = ["get_redis_cli"]


@cache
def get_redis_config() -> Dict[str, Any]:
    config = get_or_creat_settings_ins()
    info = {
        "host": config.redis.host,
        "port": config.redis.port,
        "db": config.redis.db,
        "decode_responses": True,
        "max_connections": config.redis.max_conn,
        "socket_connect_timeout": config.redis.conn_timeout,
        "retry_on_timeout": True,
    }

    if config.redis.password:
        info["password"] = config.redis.password

    return info


@cache
def get_redis_cli() -> redis.Redis:
    config = get_redis_config()

    try:
        cli = redis.Redis(**config)
        cli.ping()  # 测试连接
        logger.info("Redis connect succeed.")
        return cli
    except Exception as e:
        raise Exception(f"Redis connection failed: {e}")


@cache
async def get_async_redis_cli() -> Optional[AsyncRedis]:
    config = get_redis_config()
    try:
        cli = AsyncRedis(**config)
        await cli.ping()  # 测试连接
        logger.info("Async Redis connect succeed.")
        return cli
    except Exception as e:
        logger.error(f"Async Redis connection failed: {e}")
        return None
