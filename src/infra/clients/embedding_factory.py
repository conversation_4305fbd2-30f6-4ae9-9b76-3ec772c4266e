"""
Embedding 工厂类，支持多种embedding类型的创建和管理
"""

from typing import Optional

from langchain.embeddings.base import Embeddings
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_openai import OpenAIEmbeddings

from settings import get_or_creat_settings_ins
from settings.settings import EmbeddingSettings
from src.helper.custom_models.qwen3_embedding import Qwen3Embedding


class EmbeddingFactory:
    """Embedding工厂类，根据配置创建不同类型的embedding实例"""

    @staticmethod
    def create_embedding(
        config: Optional[EmbeddingSettings] = None, is_query: bool = False
    ) -> Embeddings:
        """
        根据配置创建embedding实例

        Args:
            config: embedding配置，如果为None则使用默认配置
            is_query: 是否用于查询（某些embedding模型对查询和文档有不同处理）

        Returns:
            Embeddings实例
        """
        if config is None:
            settings = get_or_creat_settings_ins()
            config = settings.embedding

        provider = config.provider.lower()

        if provider == "qwen3":
            return EmbeddingFactory._create_qwen3_embedding(config, is_query)
        elif provider == "openai":
            return EmbeddingFactory._create_openai_embedding(config)
        elif provider == "huggingface":
            return EmbeddingFactory._create_huggingface_embedding(config)
        else:
            raise ValueError(f"不支持的embedding provider: {provider}")

    @staticmethod
    def _create_qwen3_embedding(
        config: EmbeddingSettings, is_query: bool
    ) -> Qwen3Embedding:
        """创建Qwen3Embedding实例"""
        return Qwen3Embedding(
            base_url=config.base_url,
            model_name=config.model_name,
            timeout=config.timeout,
            is_query=is_query,
        )

    @staticmethod
    def _create_openai_embedding(config: EmbeddingSettings) -> OpenAIEmbeddings:
        """创建OpenAI Embedding实例"""
        kwargs = {
            "model": config.model_name,
            "timeout": config.timeout,
        }

        if config.api_key:
            kwargs["api_key"] = config.api_key

        if config.base_url:
            kwargs["base_url"] = config.base_url

        if config.extra_params:
            kwargs.update(config.extra_params)

        return OpenAIEmbeddings(**kwargs)

    @staticmethod
    def _create_huggingface_embedding(
        config: EmbeddingSettings,
    ) -> HuggingFaceEmbeddings:
        """创建HuggingFace Embedding实例"""
        kwargs = {
            "model_name": config.model_name,
        }

        if config.extra_params:
            kwargs.update(config.extra_params)

        return HuggingFaceEmbeddings(**kwargs)


def get_embedding_client(is_query: bool = False) -> Embeddings:
    """
    获取embedding客户端实例

    Args:
        is_query: 是否用于查询

    Returns:
        Embeddings实例
    """
    return EmbeddingFactory.create_embedding(is_query=is_query)
