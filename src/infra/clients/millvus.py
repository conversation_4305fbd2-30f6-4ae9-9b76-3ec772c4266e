from functools import cache
from typing import Optional, Dict, Any

from loguru import logger
from pymilvus import MilvusClient, AsyncMilvusClient

from settings import get_or_creat_settings_ins

__all__ = ["get_sync_milvus", "get_async_milvus"]


@cache
def get_milvus_config() -> Dict[str, Any]:
    """生成 Milvus 连接配置"""

    config = get_or_creat_settings_ins()
    info = {
        "uri": config.milvus.uri,
        "token": f"{config.milvus.user}:{config.milvus.password}",
        "timeout": config.milvus.timeout,
        "db_name": config.milvus.db_name,
    }
    return info


@cache
def get_sync_milvus() -> Optional[MilvusClient]:
    try:
        _sync_client = MilvusClient(**get_milvus_config())

        # 验证连接有效性
        _sync_client.list_collections()
        
        logger.info("Sync Milvus client initialized successfully")
        return _sync_client
    except Exception as e:
        logger.error(f"Sync Milvus initialization failed: {e}")
        _sync_client = None
        return None


@cache
async def get_async_milvus() -> Optional[AsyncMilvusClient]:
    try:
        _async_client = AsyncMilvusClient(**get_milvus_config())
        logger.info("Async Milvus client initialized successfully")
        return _async_client
    except Exception as e:
        logger.error(f"Async Milvus initialization failed: {e}")
        _async_client = None
        return None
