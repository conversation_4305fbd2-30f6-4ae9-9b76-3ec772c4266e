import os
import time
from collections.abc import Callable
from concurrent.futures import Future, ThreadPoolExecutor
from typing import Optional

from loguru import logger
from psycopg_pool import ConnectionPool
from pymilvus import MilvusClient
from redis import Redis
from redis.asyncio import Redis as AsyncRedis

from settings import Settings, get_or_creat_settings_ins
from src.infra.clients import get_redis_cli, get_sync_milvus, get_sync_pg_cli
from src.infra.clients.postgres.orm import (
    OrmSessionContext,
    init_db_engine,
    init_orm_session,
)
from src.infra.clients.redis import get_async_redis_cli
from src.infra.log import init_logger
from src.infra.scheduler import scheduler
from src.infra.utils import get_current_env
from src.infra.web.http import http_server

init_logger()


class App:
    def __init__(self, config: Settings):
        self.config = config
        self.thread_pool: ThreadPoolExecutor = ThreadPoolExecutor(
            max_workers=self.config.app.thread_pool_size
        )
        self._redis_cli: Redis | None = None
        self._postgres_cli: ConnectionPool | None = None
        self._milvus_cli: MilvusClient | None = None

        self.logger = logger
        self.http_server = http_server
        self.scheduler = scheduler

        self._on_starts = []
        self._on_stops = []

    @property
    def current_env(self) -> str:
        return get_current_env()

    @property
    def milvus_cli(self) -> MilvusClient:
        if self._milvus_cli is None:
            self._milvus_cli = get_sync_milvus()
        return self._milvus_cli

    @property
    def redis_cli(self) -> Redis:
        if self._redis_cli is None:
            self._redis_cli = get_redis_cli()
        return self._redis_cli

    @property
    def postgres_cli(self) -> ConnectionPool:
        if self.postgres_cli is None:
            self._postgres_cli = get_sync_pg_cli()
        return self._postgres_cli

    def init(self):
        init_orm_session(init_db_engine(self.config))

    def orm_session(self) -> OrmSessionContext:
        """with app.orm_session() as see:
        see.query.xxx
        """
        return OrmSessionContext()

    async def async_redis_cli(self) -> AsyncRedis | None:
        return await get_async_redis_cli()

    def add_on_start(self, func: Callable) -> None:
        self._on_starts.append(func)

    def add_on_stop(self, func: Callable) -> None:
        self._on_stops.append(func)

    def _on_start_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        def function_wrapper(func):
            try:
                func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        for func in self._on_starts:
            self.thread_pool.submit(function_wrapper, func)

    def _on_stop_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        # func 需要 try catch
        def function_wrapper(func):
            try:
                func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        for func in self._on_stops:
            self.thread_pool.submit(function_wrapper, func)

        if len(self._on_stops) > 0:
            logger.info("Waiting 3 seconds for the cleanup thread...")
            time.sleep(3)

    def submit_concurrent_task(self, func: Callable, *args, **kwargs) -> Future:
        def wrap_func(func, *args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"concurrent task error: {e}")

        return self.thread_pool.submit(wrap_func, func, *args, **kwargs)

    def launch(self):
        self.http_server.add_event_handler("startup", self._on_start_with_thread)
        self.http_server.add_event_handler("shutdown", self._on_stop_with_thread)
        self.http_server.launch()
        os._exit(0)


app = App(config=get_or_creat_settings_ins())
