"""
生成问题任务, 平时可以用来生成问题, 手动出发任务
"""


import os
from datetime import datetime
from typing import List, TypedDict

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langfuse.callback import CallbackHandler
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from pydantic import BaseModel, Field

from src.core.cognition.agents.plan_and_execute.prompt import (
    FAILED_DESC_PROMPT,
    HELP_DESC_PROMPT,
)
from src.core.cognition.agents.plan_and_execute.utils import format_tool_desc
from src.core.execution.toolkits import MCPServerToolkit, get_chat_mcp_toolkits
from src.core.schema.session import UserInfo
from src.helper.utils import get_chat_model, get_prompt_from_langfuse, PromptName


date_time = datetime.now().strftime("%Y%m%d%H%M%S")
checkpointer = MemorySaver()


class Question(BaseModel):
    question: str = Field(description="具体的提问")
    required_tools: list[str] = Field(
        description="解决<question>问题可能用到的工具名称"
    )
    ability_requirements: str = Field(description="具体考验的能力")


class Questions(BaseModel):
    questions: list[Question]


class State(TypedDict):
    # 默认State就是覆盖
    questions: list[Question]
    # 统计每一样工具目前生成的次数
    tool_count: dict[str, int]
    toolkits: list[MCPServerToolkit]
    max_question_count: int


def print_info(state: State):
    print(f"tool_count: {state['tool_count']}")
    print(f"questions: {len(state['questions'])}")


def get_question_pipeline() -> Runnable:
    parser = PydanticOutputParser(pydantic_object=Questions)
    format_instructions = parser.get_format_instructions()
    prompt_template = get_prompt_from_langfuse(PromptName.GEN_QUESTION)
    langchain_prompt = ChatPromptTemplate(prompt_template)

    langchain_prompt: ChatPromptTemplate = langchain_prompt.partial(format_instructions=format_instructions)
    llm = get_chat_model("planner", temperature=0.8)

    chain = langchain_prompt | llm | parser
    return chain


async def gen_question_task(pipline: Runnable, toolkits) -> str:
    tools_desc = format_tool_desc(toolkits) + HELP_DESC_PROMPT + FAILED_DESC_PROMPT

    response = await pipline.ainvoke({"tools_desc": tools_desc})

    return response.questions


def save_question_task(questions: list[Question]):
    import pandas as pd

    questions_dict = [question.model_dump() for question in questions]
    df = pd.DataFrame(questions_dict)
    if not os.path.exists("data"):
        os.makedirs("data")
    df.to_csv("data/gen_question.csv", index=False, encoding="utf-8")


pipline = get_question_pipeline()


async def gen_question_node(state: State, config):
    tools_desc = (
        format_tool_desc(state["toolkits"]) + HELP_DESC_PROMPT + FAILED_DESC_PROMPT
    )
    tool_count = state["tool_count"]

    def format_tool_count(tool_count: dict[str, int]):
        return "\n".join([f"{tool}: {count}" for tool, count in tool_count.items()])

    questions: Questions = await pipline.ainvoke(
        {
            "tools_desc": tools_desc,
            "tool_count": format_tool_count(tool_count),
        },
        config,
    )
    for question in questions.questions:
        for tool in question.required_tools:
            if tool not in tool_count:
                tool_count[tool] = 1
            else:
                tool_count[tool] += 1
    state["questions"].extend(questions.questions)
    state["tool_count"] = tool_count
    return state


def check_tool_count(state: State):
    print_info(state)
    if len(state["questions"]) >= state["max_question_count"]:
        return END
    return "gen_question"


async def build_graph() -> Runnable:
    # Build workflow
    parallel_builder = StateGraph(State)
    parallel_builder.add_node("gen_question", gen_question_node)
    # 从 START 节点开始
    parallel_builder.add_edge(START, "gen_question")
    # 从 gen_question 节点结束
    parallel_builder.add_conditional_edges(
        "gen_question", check_tool_count, [END, "gen_question"]
    )
    graph = parallel_builder.compile(checkpointer=checkpointer)

    return graph


async def run(max_question_count: int):
    graph = await build_graph()
    user = UserInfo(id="706541654", name="moyueheng", email="<EMAIL>")
    toolkits: List[MCPServerToolkit] = get_chat_mcp_toolkits(user)
    tool_count = {}
    for toolkit in toolkits:
        for tool in toolkit.get_tools():
            tool_count[tool.name] = 0
    state = State(
        questions=[],
        tool_count=tool_count,
        toolkits=toolkits,
        max_question_count=max_question_count,
    )
    config = {
        "callbacks": [CallbackHandler()],
        "configurable": {"thread_id": f"gen_question_{date_time}"},
    }

    result = await graph.ainvoke(state, config)
    save_question_task(result["questions"])
