"""
从langfuse上统计一段时间的指标数据, 手动出发任务, 可以用于观察一周的使用情况
"""

import datetime as dt
from typing import List

from langfuse.api.resources.commons.types.session import Session

from src.helper.utils import get_langfuse_client
from src.schedule.eval.utils import fetch_all_pages_sessions, get_date_range

langfuse_client = get_langfuse_client()


class Metrics:
    """
    统计指标
    """

    def __init__(self):
        self.total_sessions_count = 0
        self.finish_count = 0
        self.agent_count = 0
        self.human_count = 0
        self.error_count = 0
        self.none_count = 0
        self.other_count = 0
        self.user_id_set = set()
        self.other_list = []


def _get_metrics(sessions: List[Session]) -> Metrics:
    metrics = Metrics()
    metrics.total_sessions_count = len(sessions)
    for session in sessions:
        session_id = session.id
        session_traces = langfuse_client.fetch_traces(session_id=session_id)
        try:
            final_trace = session_traces.data[0]
            output = final_trace.output
            metrics.user_id_set.add(final_trace.user_id)
            if isinstance(output, str) and "error" in output.lower():
                metrics.error_count += 1
            elif isinstance(output, dict):
                status = output.get("status", "")
                if status == "agent":
                    metrics.agent_count += 1
                elif status == "human":
                    metrics.human_count += 1
                elif status == "finish":
                    metrics.finish_count += 1
                else:
                    metrics.other_count += 1
                    metrics.other_list.append(final_trace)
            elif output is None:
                metrics.none_count += 1
            else:
                metrics.other_count += 1
                metrics.other_list.append(final_trace)
        except Exception as e:
            print(f"Error processing data item: {e}")
            metrics.other_count += 1

    print(f"total_sessions_count: {metrics.total_sessions_count}")
    print(f"user_count: {len(metrics.user_id_set)}")
    print(f"finish_count: {metrics.finish_count}")
    print(f"agent_count: {metrics.agent_count}")
    print(f"human_count: {metrics.human_count}")
    print(f"error_count: {metrics.error_count}")
    print(f"none_count: {metrics.none_count}")
    print(f"other_count: {metrics.other_count}")
    return metrics


def get_metrics(days_back: int = 14, end_date: dt.datetime = dt.datetime.now()):
    overall_start_date, overall_end_date = get_date_range(days_back, end_date)

    # 按天遍历日期范围
    metrics_list = []
    current_date = overall_start_date
    while current_date < overall_end_date:
        next_date = current_date + dt.timedelta(days=1)
        print(f"\n处理日期: {current_date.date()} 到 {next_date.date()}")

        # 获取当天的sessions
        sessions = fetch_all_pages_sessions(
            langfuse_client,
            from_timestamp=current_date,
            to_timestamp=next_date,
        )

        # 计算该天的指标
        metrics = _get_metrics(sessions)
        metrics_list.append(metrics)
        # 移动到下一天
        current_date = next_date

    # 汇总两周指标
    total_metrics = Metrics()
    total_user_ids = set()
    daily_user_counts = []  # 记录每天的活跃用户数

    # 累加每天的指标
    for daily_metrics in metrics_list:
        if len(daily_metrics.user_id_set) < 5:  # 没人用说明是周末
            continue
        total_metrics.finish_count += daily_metrics.finish_count
        total_metrics.agent_count += daily_metrics.agent_count
        total_metrics.human_count += daily_metrics.human_count
        total_metrics.error_count += daily_metrics.error_count
        total_metrics.none_count += daily_metrics.none_count
        total_metrics.other_count += daily_metrics.other_count
        total_metrics.total_sessions_count += daily_metrics.total_sessions_count
        total_metrics.other_list.extend(daily_metrics.other_list)
        total_user_ids.update(daily_metrics.user_id_set)
        daily_user_counts.append(len(daily_metrics.user_id_set))  # 添加每天的活跃用户数

    print("\n=== 两周总计 ===")
    print(f"DAU平均: {sum(daily_user_counts) / len(daily_user_counts):.1f}")
    print(f"总用户数: {len(total_user_ids)}")
    print(f"总完成对话: {total_metrics.finish_count}")
    print(f"总Agent回复: {total_metrics.agent_count}")
    print(f"总人工回复: {total_metrics.human_count}")
    print(f"总错误数: {total_metrics.error_count}")
    print(f"总空回复: {total_metrics.none_count}")
    print(f"总其他情况: {total_metrics.other_count}")
    print(
        f"正确率: {(total_metrics.finish_count + total_metrics.agent_count + total_metrics.human_count) / total_metrics.total_sessions_count}"
    )


if __name__ == "__main__":
    get_metrics(days_back=7)
