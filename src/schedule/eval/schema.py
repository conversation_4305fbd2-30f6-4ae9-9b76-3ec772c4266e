from enum import Enum
from typing import Any

from pydantic import BaseModel, Field

from src.core.cognition.agents.plan_and_execute.schema import PlanActionResponse


class TalkerTask(BaseModel):
    """
    任务模型，用于验证和序列化任务列表
    """

    id: int = Field(description="任务id")
    name: str = Field(description="任务名称")
    description: str = Field(description="任务描述")


class TalkerTasks(BaseModel):
    """
    任务模型，用于验证和序列化任务列表
    """

    tasks: list[TalkerTask] = Field(description="任务列表")


class TalkerDeleteInfo(BaseModel):
    """
    创建任务模型，用于验证和序列化创建任务列表
    """

    id: int = Field(description="任务id")
    name: str = Field(description="任务名称")
    reason: str = Field(description="放弃任务的原因")


class TalkerDelete(BaseModel):
    """
    删除任务模型，用于验证和序列化删除任务列表
    """

    delete: TalkerDeleteInfo = Field(description="删除任务")


class TalkerHuman(BaseModel):
    """
    信息收集模型，用于验证和序列化信息收集列表
    """

    human: str = Field(description="信息收集")


class TalkerMessage(BaseModel):
    """
    即时响应模型，用于验证和序列化即时响应列表
    """

    message: str = Field(description="即时响应")


talker_sechemas = [TalkerTasks, TalkerDelete, TalkerHuman, TalkerMessage]


planer_sechemas = [PlanActionResponse]


class NextAction(BaseModel):
    """
    下一步动作模型，用于验证和序列化下一步动作
    """

    next_action: str = Field(description="下一步动作")
    id: int = Field(description="动作id")
    thought: str = Field(description="思考过程")
    action: str = Field(description="action")


class LastAction(BaseModel):
    """
    最终动作模型，用于验证和序列化最终动作
    """

    next_action: str = Field(
        description="下一步动作", enum=["replan", "final", "givenup"]
    )
    thought: str = Field(description="思考过程")
    result: Any = Field(description="结果")


step_sechemas = [NextAction, LastAction]


class LanggraphActiveNode(str, Enum):
    """
    语言模型节点
    """

    TALKER = "talker"
    PLANNER = "planner"
    STEPS_EXECUTOR = "steps_executor"
    SUMMARY = "summarizer"


class LanggraphNode(str, Enum):
    """
    语言模型节点
    """

    TALKER = "talker"
    PLANNER = "planner"
    STEPS_EXECUTOR = "steps_executor"
    SUMMARY = "summarizer"
    UNKNOWN = "unknown"


class ShareGptSchema(BaseModel):
    """
    如果两个对象有相同的哈希值，Python 会调用 __eq__ 方法来确认它们是否真的相等
    只有当两个对象完全相等时，set 才会认为它们是重复的，不会添加重复项
    """

    conversations: list[dict]
    system: str
    metadata: dict
    model: str = Field(default="")
    langgraph_node: LanggraphNode = Field(default=LanggraphNode.UNKNOWN)
    tools: str = Field(default="")
    tag: list[str] = Field(default=[])

    def __hash__(self):
        hash_str = ""
        for conversation in self.conversations:
            hash_str += conversation["from"] + conversation["value"].strip()
        hash_str += self.model
        return hash(hash_str)

    def __eq__(self, other):
        if not isinstance(other, ShareGptSchema):
            return False
        return hash(self) == hash(other)

    def __str__(self):
        return f"ShareGptSchema(conversations={self.conversations}, system={self.system}, tools={self.tools}, model={self.model}, langgraph_node={self.langgraph_node})"


class SessionScore(BaseModel):
    """
    会话评分模型，用于存储对话系统各个方面的评分和评价
    继承自Pydantic的BaseModel，提供数据验证和序列化功能
    """

    is_pass: bool = Field(description="是否解决了用户问题")

    plan_score: float = Field(description="任务规划得分, 1~10分")
    plan_reason: str = Field(description="任务规划得分理由")
    step_score: float = Field(description="工具执行步骤得分, 1~10分")
    step_reason: str = Field(description="工具执行步骤得分理由")
    intent_score: float = Field(description="意图识别得分, 1~10分")
    intent_reason: str = Field(description="意图识别得分理由")
    score: float = Field(description="综合评分, 1~10分")
    reason: str = Field(description="综合评分理由")


class GeneralScore(BaseModel):
    """
    通用评分模型，用于存储对话系统各个方面的评分和评价
    """

    is_valid_json: bool = Field(description="是否有效json")
    human_feedback: int = Field(
        description="用户反馈, -1 的时候备注里面会写上用户反馈选项, 1的时候就是好"
    )
