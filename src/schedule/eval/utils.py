import datetime as dt

from langfuse import <PERSON>fuse
from langfuse.api.resources.commons.types.dataset_item import DatasetItem
from langfuse.client import FetchObservationsResponse, ObservationsView

from src.schedule.eval.schema import ShareGptSchema


def fetch_all_pages_sessions(
    langfuse_client: Langfuse,
    limit: int = 50,
    from_timestamp: dt.datetime | None = None,
    to_timestamp: dt.datetime | None = None,
):
    """从 Langfuse 分页获取所有对话数据

    Args:
        langfuse_client: Langfuse客户端实例
        name: 对话名称过滤
        user_id: 用户ID过滤
        limit: 每页数据量限制
        from_timestamp: 开始时间
        to_timestamp: 结束时间

    Returns:
        list: 所有获取到的对话数据
    """
    page = 1
    all_data = []

    while True:
        response = langfuse_client.fetch_sessions(
            page=page,
            limit=limit,
            from_timestamp=from_timestamp,
            to_timestamp=to_timestamp,
        )
        if not response.data:
            break
        all_data.extend(response.data)
        page += 1
    return all_data


def fetch_all_pages_generations(
    langfuse_client: Langfuse,
    name=None,
    user_id=None,
    total_limit=5000,
    from_timestamp=None,
    to_timestamp=None,
    filter_by_human_feedback=True,
) -> list[ObservationsView]:
    """从 Langfuse 分页获取所有 generation 数据

    Args:
        langfuse_client: Langfuse客户端实例
        name: 对话名称过滤
        user_id: 用户ID过滤
        total_limit: 最大获取数据量限制,默认5000条
        from_timestamp: 开始时间
        to_timestamp: 结束时间
        filter_by_human_feedback: 是否只获取有用户用户点赞的 Generation

    Returns:
        list[ObservationsView]: 所有获取到的生成数据列表,每个元素为一条生成记录
    """
    
    all_data: list[ObservationsView] = []

    if filter_by_human_feedback:
        trace_ids = set()
        page_num = 1
        # 获取有用户点赞的 trace_id
        while True:
            scores = langfuse_client.api.score.get(
                page=page_num,
                limit=50,
                name="user-feedback",
                operator="=",
                value=1,
                from_timestamp=from_timestamp,
                to_timestamp=to_timestamp,
            )
            if not scores.data:
                break
            for score in scores.data:
                if score.trace_id in trace_ids:
                    continue
                trace_ids.add(score.trace_id)
            page_num += 1
        # 获取有用户点赞的 Generation
        for trace_id in trace_ids:
            generation: FetchObservationsResponse = (
                langfuse_client.fetch_observations(
                    trace_id=trace_id, type="GENERATION"
                )
            )

            all_data.extend(generation.data)
            if len(all_data) > total_limit:
                return all_data[:total_limit]
        
    else:
        while True:
            generations: FetchObservationsResponse = langfuse_client.get_generations(
                name=name,
                limit=50,
                user_id=user_id,
                page=page_num,
                from_start_time=from_timestamp,
                to_start_time=to_timestamp,
                type="GENERATION",
            )
            if not generations.data:
                break

            all_data.extend(generations.data)
            page_num += 1
            if len(all_data) > total_limit:
                return all_data[:total_limit]
    return all_data

def get_date_range(days_back=7, end_date=dt.datetime.now()):
    """获取指定天数范围的起止时间

    Args:
        days_back (int): 向前追溯的天数，默认为7天

    Returns:
        tuple: (start_date, end_date) 开始和结束时间
    """
    start_date = end_date - dt.timedelta(days=days_back)
    return start_date, end_date


def genneration_to_share_gpt_format(generation: ObservationsView) -> ShareGptSchema:
    """将 Langfuse 对话数据转换为训练数据格式

    {'model', 'planner', 'steps_executor', 'talker'}
    langgraph_node = generation.metadata['langgraph_node']

    langgraph_node 有四种类型, 目前全部都保存了, 但是后面应该是需要做区分的


    将对话转换为以下格式：
    {
        "conversations": [
            {"from": "human", "value": "用户输入"},
            {"from": "gpt", "value": "AI回复"}
        ],
        "system": "系统提示词",
        "tools": "工具描述",
        "metadata": "元数据",
        "model": "模型信息"
    }

    Args:
        generation: Langfuse对话数据对象

    Returns:
        ShareGptSchema: 转换后的训练数据格式
    """
    system = generation.input[0]["content"]
    conversations = []

    # 转换对话内容
    for i in range(1, len(generation.input)):
        if generation.input[i]["role"] == "user":
            conversations.append({
                "from": "human",
                "value": generation.input[i]["content"],
            })
        elif generation.input[i]["role"] == "assistant":
            conversations.append({
                "from": "gpt",
                "value": generation.input[i]["content"],
            })

    conversations.append({
        "from": "gpt",
        "value": generation.output["content"],
    })

    return ShareGptSchema(
        conversations=conversations,
        system=system,
        tools="",  # 为了符合格式, 留空
        metadata=generation.metadata,
        model=generation.model,
        langgraph_node=generation.metadata["langgraph_node"],  # 作为角色分类字段
    )


def dataitem_to_share_gpt_format(dataitem: DatasetItem) -> ShareGptSchema:
    """将 Langfuse 对话数据转换为训练数据格式

    dataitem.input 是 list[dict], 其中第一个元素是系统提示词, 后面是用户和AI的对话

    langgraph_node 有四种类型, 目前全部都保存了, 但是后面应该是需要做区分的


    将对话转换为以下格式：
    {
        "conversations": [
            {"from": "human", "value": "用户输入"},
            {"from": "gpt", "value": "AI回复"}
        ],
        "system": "系统提示词",
        "tools": "工具描述",
        "metadata": "元数据",
        "model": "模型信息"
    }

    Args:
        generation: Langfuse对话数据对象

    Returns:
        ShareGptSchema: 转换后的训练数据格式
    """
    system = dataitem.input[0]["content"]
    conversations = list()
    for item in dataitem.input[1:]:
        if item.get('role') == "user":
            new_item = {
                "from": "human",
                "value": item["content"]
            }
        elif item.get('role') == "assistant":
            new_item = {
                "from": "gpt",
                "value": item["content"]
            }
        else:
            raise ValueError(f"Unknown role: {item.get('role')}")

        new_item["value"] = new_item["value"].strip()
        conversations.append(new_item)
    conversations.append({
        "from": "gpt",
        "value": dataitem.expected_output["content"]
    })

    return ShareGptSchema(
        conversations=conversations,
        system=system,
        tools="",  # 为了符合格式, 留空
        metadata=dataitem.metadata,
        langgraph_node=dataitem.metadata["langgraph_node"],  # 作为角色分类字段
    )



def convert_to_dict(data: list[ShareGptSchema]) -> list[dict]:
    """将 ShareGptSchema 对象列表转换为字典列表"""
    res = [item.dict() for item in data]
    return res