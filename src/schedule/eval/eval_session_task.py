"""
对一段时间的 session 进行评估, 并推送评分结果到 langfuse, 可作为定时任务
"""


import asyncio
import datetime as dt

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langfuse.api.resources.commons.types import TraceWithDetails
from langfuse.api.resources.commons.types.session import Session
from langfuse.api.resources.commons.types.session_with_traces import SessionWithTraces
from langfuse.callback import CallbackHandler
from langfuse.client import FetchSessionsResponse, FetchTracesResponse
from loguru import logger

from src.core.execution.toolkits.mcp import get_tools_desc
from src.core.schema.session import UserInfo
from src.helper.utils import get_chat_model, get_langfuse_client

from .prompts import eval_system_prompt
from .schema import SessionScore


class EvalSessionTask:
    """
    对话评估器类，用于评估对话系统的性能
    包含对话获取、评分计算和评分推送等功能
    """

    # Langfuse客户端
    langfuse = get_langfuse_client()

    # Langfuse处理器
    langfuse_handler = CallbackHandler()

    # 配置
    config = {"callbacks": [langfuse_handler]}

    # LLM模型
    llm = get_chat_model("planner")  # 使用推理模型去评估
    # 这个唯一的作用就是去拿工具, 所以随便写一个用户占位就行
    user = UserInfo(id="706541654", email="<EMAIL>", name="莫岳恒")

    # 并发限制信号量
    semaphore = asyncio.Semaphore(10)  # 限制最大并发数为10

    # 系统提示词
    system_prompt = eval_system_prompt

    # 用户提示词
    user_prompt = """
# 所有工具
{all_tools}

# 对话记录
{conversation}
"""

    def __get_conversation(
        self, trace: TraceWithDetails, join_str: str = "------\n"
    ) -> str:
        """
        获取单个追踪的对话内容

        Args:
            trace: 追踪详情对象
            join_str: 用于连接不同部分对话的分隔符

        Returns:
            str: 格式化后的对话内容
        """
        conversation_list = []
        generations = self.langfuse.get_generations(trace_id=trace.id)
        for generation in reversed(
            generations.data
        ):  # 默认都是给咱们最新的, 但是我们需要按时间顺序来
            output = generation.output
            if generation.metadata["langgraph_node"] == "talker":
                conversation_list.append(
                    f"id: {generation.id} 意图识别: \n{output['content'].strip()}\n"
                )
            elif generation.metadata["langgraph_node"] == "planner":
                conversation_list.append(
                    f"id: {generation.id} 任务规划: \n{output['content'].strip()}\n"
                )
            elif generation.metadata["langgraph_node"] == "steps_executor":
                conversation_list.append(
                    f"id: {generation.id} 工具执行: \n{output['content'].strip()}\n"
                )  # TODO 第一次工具执行不是一个 generation, 需要单独处理
            elif generation.metadata["langgraph_node"] == "history_summary":
                conversation_list.append(
                    f"id: {generation.id} 历史对话总结: \n{output['content'].strip()}\n"
                )
            else:
                conversation_list.append(
                    f"id: {generation.id} 其他: \n{output['content'].strip()}\n"
                )
        conversation: str = join_str.join(conversation_list)
        return conversation

    def get_conversations(self, session: Session, join_str: str = "------\n") -> str:
        """
        获取会话中的所有对话内容

        Args:
            session: 会话对象
            join_str: 用于连接不同对话的分隔符

        Returns:
            str: 包含所有对话轮次的字符串
        """
        traces: FetchTracesResponse = self.langfuse.fetch_traces(session_id=session.id)
        conversations = []
        for j, trace in enumerate(reversed(traces.data)):
            conversation = f"------ 第{j + 1}轮对话 ------\n"
            conversation += self.__get_conversation(trace)
            conversations.append(conversation)
        conversations_str = "\n".join(conversations)
        return conversations_str

    def get_score_pipeline(self) -> Runnable:
        """
        创建评分管道，包含提示词模板和输出解析器

        Returns:
            Chain: LangChain处理链，用于生成评分
        """
        parser = PydanticOutputParser(pydantic_object=SessionScore)
        format_instructions = parser.get_format_instructions()
        prompt = ChatPromptTemplate(
            [
                ("system", self.system_prompt),
                ("user", self.user_prompt),
            ],
            input_variables=["all_tools", "conversation"],
            partial_variables={"format_instructions": format_instructions},
        )
        chain: Runnable = prompt | self.llm | parser
        return chain

    async def push_score(self, session: Session, score: SessionScore):
        """
        将评分结果推送到Langfuse

        Args:
            session: 会话对象
            score: 评分结果对象
        """
        traces: FetchTracesResponse = self.langfuse.fetch_traces(session_id=session.id)
        for trace in traces.data:
            generations = self.langfuse.get_generations(trace_id=trace.id)
            for generation in generations.data:
                # 先删除
                # 如果查到了, 应该删除再创建
                # TODO: 现在分数太多了, 应该去重的, 所以如果有了分数应该删除, 再添加, 但是langfuse的api太垃圾
                # 删除
                if generation.metadata["langgraph_node"] == "steps_executor":
                    self.langfuse.score(
                        trace_id=trace.id,
                        observation_id=generation.id,
                        name="step_score",
                        value=score.step_score,
                        comment=score.step_reason,
                        data_type="NUMERIC",
                    )
                    logger.info(
                        f"session: {session.id} 中的 trace_id: {trace.id} generation_id: {generation.id} 评估完成，工具执行得分: {score.step_score}"
                    )
                elif generation.metadata["langgraph_node"] == "planner":
                    self.langfuse.score(
                        trace_id=trace.id,
                        observation_id=generation.id,
                        name="plan_score",
                        value=score.plan_score,
                        comment=score.plan_reason,
                        data_type="NUMERIC",
                    )
                    logger.info(
                        f"session: {session.id} 中的 trace_id: {trace.id} generation_id: {generation.id} 评估完成，任务规划得分: {score.plan_score}"
                    )
                elif generation.metadata["langgraph_node"] == "talker":
                    self.langfuse.score(
                        trace_id=trace.id,
                        observation_id=generation.id,
                        name="intent_score",
                        value=score.intent_score,
                        comment=score.intent_reason,
                        data_type="NUMERIC",
                    )
                    logger.info(
                        f"session: {session.id} 中的 trace_id: {trace.id} generation_id: {generation.id} 评估完成，意图识别得分: {score.intent_score}"
                    )
                else:
                    logger.warning(
                        f"session: {session.id} 中的 trace_id: {trace.id} generation_id: {generation.id} 评估完成，未知节点: {generation.metadata['langgraph_node']}"
                    )
            # 推送综合评分
            self.langfuse.score(
                trace_id=trace.id,
                name="overall_score",
                value=score.score,
                comment=score.reason,
                data_type="NUMERIC",
            )
            self.langfuse.score(
                trace_id=trace.id,
                name="is_pass",
                value=score.is_pass,
                data_type="BOOLEAN",
            )
            logger.info(
                f"session: {session.id} 中的 trace_id: {trace.id} 评估完成，综合得分: {score.score}"
            )

    async def async_eval_session(
        self, session: Session, all_tools: str, score_pipeline: Runnable
    ):
        """
        异步评估单个会话

        Args:
            session: 会话对象
            all_tools: 所有可用工具的描述
            score_pipeline: 评分管道
        """
        logger.info(f"开始评估会话 {session.id}")
        conversations_str = self.get_conversations(session)
        retry_count = 0
        max_retries = 3
        while retry_count < max_retries:
            try:
                score = await score_pipeline.ainvoke(
                    {
                        "all_tools": all_tools,
                        "conversation": conversations_str,
                    },
                    config=self.config,
                )
                await self.push_score(session, score)
                logger.info(f"会话 {session.id} 评估完成，综合得分: {score.score}")
                break
            except Exception as e:
                retry_count += 1
                import traceback

                error_stack = traceback.format_exc()
                if retry_count == max_retries:
                    logger.error(
                        f"会话 {session.id} 评估失败，重试{max_retries}次后仍然失败。\n"
                        f"错误信息: {str(e)}\n"
                        f"错误堆栈:\n{error_stack}"
                    )
                    return
                logger.warning(
                    f"会话 {session.id} 评估失败，第{retry_count}次重试。\n"
                    f"错误信息: {str(e)}\n"
                    f"错误堆栈:\n{error_stack}"
                )
                await asyncio.sleep(1)  # 重试前等待1秒

    async def eval(self, days: int = 10, limit: int = 100):
        """
        评估用户的对话会话

        Args:
            days: 评估最近几天的会话，默认10天
            limit: 评估的会话数量限制，默认100个
        """
        all_tools = get_tools_desc(self.user)
        from_timestamp = dt.datetime.now() - dt.timedelta(days=days)
        sessions: FetchSessionsResponse = self.langfuse.fetch_sessions(
            limit=limit, from_timestamp=from_timestamp
        )
        logger.info(f"获取到 {len(sessions.data)} 个会话")
        score_pipeline = self.get_score_pipeline()

        async def eval_with_semaphore(session):
            async with self.semaphore:
                return await self.async_eval_session(session, all_tools, score_pipeline)

        scoring_tasks = [eval_with_semaphore(session) for session in sessions.data]
        await asyncio.gather(*scoring_tasks)
        logger.info(f"用户 {self.user.id} 的所有会话评估完成")

    async def eval_session_by_id(self, session_id: str):
        """
        评估用户的对话会话

        Args:
            session_id: 会话ID
        """
        logger.info(f"开始评估指定会话 {session_id}")
        session: SessionWithTraces = self.langfuse.client.sessions.get(
            session_id=session_id
        )
        all_tools = get_tools_desc(self.user)
        score_pipeline = self.get_score_pipeline()
        await self.async_eval_session(session, all_tools, score_pipeline)
        logger.info(f"指定会话 {session_id} 评估完成")
