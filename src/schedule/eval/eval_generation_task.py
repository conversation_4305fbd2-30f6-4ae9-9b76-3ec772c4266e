"""
对一段时间的 generation 进行评估, 并推送评分结果到 langfuse, 目前还在考虑是否应该吧这些数据存到 langfuse, 可作为定时任务
"""

from dataclasses import dataclass
from typing import Dict, List, Optional

from langchain.evaluation.parsing.json_schema import JsonSchemaEvaluator
from langchain_core.output_parsers.json import JsonOutputParser
from langfuse.client import Langfuse, ObservationsView
from loguru import logger
from pydantic import BaseModel, ConfigDict

from src.helper.utils import get_langfuse_client
from src.schedule.eval.schema import (
    GeneralScore,
    planer_sechemas,
    step_sechemas,
    talker_sechemas,
)
from src.schedule.eval.utils import fetch_all_pages_generations, get_date_range

langfuse = get_langfuse_client()


@dataclass
class EvaluationResult:
    """评估结果数据类"""

    total_generations: int
    valid_generations: int
    valid_ratio: float

    def __str__(self) -> str:
        return (
            f"总生成数: {self.total_generations}\n"
            f"有效生成数: {self.valid_generations}\n"
            f"有效比率: {self.valid_ratio:.2%}"
        )


class EvalGenerationTask(BaseModel):
    """评估生成任务"""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    talker_sechemas: List[BaseModel] = talker_sechemas
    step_sechemas: List[BaseModel] = step_sechemas
    planer_sechemas: List[BaseModel] = planer_sechemas

    json_schema_evaluator: JsonSchemaEvaluator = JsonSchemaEvaluator()
    json_output_parser: JsonOutputParser = JsonOutputParser()

    def _validate_schema(self, json_dict: Dict, schemas: List[BaseModel]) -> bool:
        """验证JSON是否符合任一schema"""
        for schema in schemas:
            result = self.json_schema_evaluator.evaluate_strings(
                prediction=json_dict,
                reference=schema.model_json_schema(),
            )
            if result["score"]:
                return True
        return False

    def _get_schema_for_type(self, gen_type: str) -> Optional[List[BaseModel]]:
        """根据生成类型获取对应的schema列表"""
        schema_map = {
            "talker": self.talker_sechemas,
            "steps_executor": self.step_sechemas,
            "planner": self.planer_sechemas,
        }
        return schema_map.get(gen_type)

    def _process_json_generation(self, generation: ObservationsView) -> bool:
        """处理单个生成结果，返回是否有效"""
        try:
            content = generation.output
            if not content:
                logger.warning(f"Generation {generation.id} has no content")
                return False

            content_str = content.get("content", "")
            if not content_str:
                logger.warning(f"Generation {generation.id} has no content_str")
                return False

            gen_type = generation.metadata["langgraph_node"]
            if gen_type == "summarizer":
                return True

            json_dict = self.json_output_parser.parse(content_str)
            schemas = self._get_schema_for_type(gen_type)

            if not schemas:
                logger.warning(f"Unknown generation type: {gen_type}")
                return False

            return self._validate_schema(json_dict, schemas)

        except Exception as e:
            import traceback

            logger.error(
                f"Error processing generation {generation.id}:\n"
                f"Exception: {str(e)}\n"
                f"Traceback:\n{traceback.format_exc()}\n"
            )
            return False

    def _get_score(self, generation: ObservationsView) -> GeneralScore:
        """获取 generation 评分
        """
        is_valid_json = self._process_json_generation(generation)
        if not is_valid_json:
            return GeneralScore(is_valid_json=False)
        # TODO 把用户反馈扩展到每个Generation

        # TODO agent 评分指标 https://www.deepeval.com/docs/metrics-task-completion

        return GeneralScore(is_valid_json=True)

    def eval(self, days_back: int = 1) -> EvaluationResult:
        """评估生成任务"""
        langfuse = get_langfuse_client()
        start_date, end_date = get_date_range(days_back=days_back)

        logger.info(f"Fetching generations from {start_date} to {end_date}")
        generations: List[ObservationsView] = fetch_all_pages_generations(
            langfuse, from_timestamp=start_date, to_timestamp=end_date
        )

        # 先走一轮，获取所有有效 generation
        for generation in generations:
            score = self._get_score(generation)

            self.push_score(langfuse, generation, score)

    def push_score(
        self, langfuse: Langfuse, generation: ObservationsView, score: GeneralScore
    ):
        """推送评分结果"""
        for key, value in score.model_dump().items():
            if isinstance(value, bool):
                data_type = "BOOLEAN"
            elif isinstance(value, int):
                data_type = "NUMERIC"
            else:
                data_type = "CATEGORICAL"
            langfuse.score(
                name=key,
                value=value,
                trace_id=generation.trace_id,
                observation_id=generation.id,
                data_type=data_type,
            )


if __name__ == "__main__":
    eval_task = EvalGenerationTask()
    result = eval_task.eval(days_back=1)
    logger.info(str(result))
