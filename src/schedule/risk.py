import json
import time
from collections import defaultdict
from typing import List, Dict

from lark_oapi.api.contact.v3 import BatchGetIdUserRequest, BatchGetIdUserRequestBody
from loguru import logger

from src.core.perception.sender import IMessageSender
from src.core.schema.chatbot import RobotType
from src.infra import clients, app
from src.infra.clients.risk import RiskClient


def group_by_owner_and_limit(data_list: List[Dict], limit: int = 20) -> Dict[str, List[Dict]]:
    grouped = defaultdict(list)
    for item in data_list:
        owners = item.get("owner", [])
        for owner in owners:
            if len(grouped[owner]) < limit:
                grouped[owner].append(item)
    return dict(grouped)


def build_risk_card(records: List[Dict], data: dict) -> Dict:
    risk_elements = []

    if records and len(records) > 0:
        for risk in records:
            risk_content = \
                f"""
                处理风险：
                  • {risk['name']} ({risk['resource_type']})
                    - 风险等级: {risk['risk_level']}
                    - 资源名称：{risk['name']}
                    - 资源类型：{risk['resource_type']}
                    - cmdb_id：{risk['cmdb_id']}
                    - 风险类型: {risk['risk_type']}
                    - 风险提示: {risk['risk_tip']}
                    - 过期时间: {risk['expire_time']}
                """

            risk_card = {
                "tag": "interactive_container",
                "width": "fill",
                "height": "auto",
                "corner_radius": "",
                "elements": [
                    {
                        "tag": "interactive_container",
                        "width": "fill",
                        "height": "auto",
                        "corner_radius": "",
                        "elements": [
                            {
                                "tag": "column_set",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**🔢风险等级：** {risk['risk_level']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            }
                                        ],
                                        "vertical_spacing": "8px",
                                        "horizontal_align": "left",
                                        "vertical_align": "top",
                                        "weight": 1
                                    }
                                ]
                            },
                            {
                                "tag": "column_set",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**🎰资源名称：** {risk['name']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            },
                                            {
                                                "tag": "markdown",
                                                "content": f"**🎰资源类型：** {risk['resource_type']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            },
                                            {
                                                "tag": "markdown",
                                                "content": f"**🎰风险类型：** {risk['risk_type']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            },
                                            {
                                                "tag": "markdown",
                                                "content": f"**🕐过期时间：** {risk['expire_time']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            },
                                            {
                                                "tag": "markdown",
                                                "content": f"**📋风险提示：** {risk['risk_tip']}，"
                                                           f"cmdb_id：{risk['cmdb_id']}",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            },
                                            {
                                                "tag": "button",
                                                "text": {
                                                    "tag": "plain_text",
                                                    "content": "处理风险"
                                                },
                                                "type": "primary",
                                                "width": "default",
                                                "size": "medium",
                                                "icon": {
                                                    "tag": "standard_icon",
                                                    "token": "setting-inter_outlined"
                                                },
                                                "margin": "0px 0px 0px 0px",
                                                "behaviors": [
                                                    {
                                                        "type": "callback",
                                                        "value": {
                                                            "action": "deal_risk",
                                                            "content": f"{risk_content}"
                                                        }
                                                    }
                                                ],
                                            }
                                        ],
                                        "vertical_spacing": "8px",
                                        "horizontal_align": "left",
                                        "vertical_align": "top",
                                        "weight": 1
                                    }
                                ]
                            }
                        ],
                        "has_border": False,
                        "background_style": "color_lsrskfsuswr",
                        "padding": "8px 4px 8px 4px",
                        "direction": "vertical",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "margin": "0px 0px 0px 0px"
                    }
                ],
                "has_border": False,
                "background_style": "bg-white",
                "padding": "8px 4px 8px 4px",
                "direction": "vertical",
                "horizontal_spacing": "8px",
                "vertical_spacing": "8px",
                "horizontal_align": "left",
                "vertical_align": "top",
                "margin": "0px 0px 0px 0px"
            }
            risk_elements.append(risk_card)
    else:
        risk_elements = [
            {
                "tag": "interactive_container",
                "width": "fill",
                "height": "auto",
                "corner_radius": "",
                "elements": [
                    {
                        "tag": "interactive_container",
                        "width": "fill",
                        "height": "auto",
                        "corner_radius": "",
                        "elements": [
                            {
                                "tag": "column_set",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": "暂无中高级别风险",
                                                "text_align": "left",
                                                "text_size": "normal_v2"
                                            }
                                        ],
                                        "vertical_spacing": "8px",
                                        "horizontal_align": "left",
                                        "vertical_align": "top",
                                        "weight": 1
                                    }
                                ]
                            }
                        ],
                        "has_border": False,
                        "background_style": "color_lsrskfsuswr",
                        "padding": "8px 4px 8px 4px",
                        "direction": "vertical",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "margin": "0px 0px 0px 0px"
                    }
                ],
                "has_border": False,
                "background_style": "bg-white",
                "padding": "8px 4px 8px 4px",
                "direction": "vertical",
                "horizontal_spacing": "8px",
                "vertical_spacing": "8px",
                "horizontal_align": "left",
                "vertical_align": "top",
                "margin": "0px 0px 0px 0px"
            }
        ]
    sub_title = f"中高风险总数共：{data['totalCount']}" if data['totalCount'] > 0 else ""

    if 'high_level_count' in data and data['high_level_count'] > 0:
        sub_title += f"，高风险数量为：{data['high_level_count']}"

    if 'middle_level_count' in data and data['middle_level_count'] > 0:
        sub_title += f"，中风险数量为：{data['middle_level_count']}"

    return {
        "schema": "2.0",
        "config": {
            "update_multi": True,
            "style": {
                "text_size": {
                    "normal_v2": {
                        "default": "normal",
                        "pc": "normal",
                        "mobile": "heading"
                    }
                },
                "color": {
                    "color_k5je3f65sb": {
                        "light_mode": "rgba(250, 250, 250, 1)",
                        "dark_mode": "rgba(26, 25, 25, 1)"
                    },
                    "color_lsrskfsuswr": {
                        "light_mode": "rgba(250, 250, 250, 1)",
                        "dark_mode": "rgba(26, 25, 25, 1)"
                    }
                }
            }
        },
        "body": {
            "direction": "vertical",
            "horizontal_spacing": "8px",
            "vertical_spacing": "8px",
            "horizontal_align": "left",
            "vertical_align": "center",
            "padding": "4px 10px 4px 10px",
            "elements": risk_elements
        },
        "header": {
            "title": {
                "tag": "plain_text",
                "content": "当前中高风险列表"
            },
            "subtitle": {
                "tag": "plain_text",
                "content": sub_title
            },
            "template": "orange",
            "padding": "12px 12px 12px 12px"
        }
    }


def send_risk_list() -> None:
    risk_client = RiskClient()

    # 1. First query 1000 records to get the initial list
    risk_info = risk_client.get_risk_info()
    logger.info(f"Initial risk info response: {risk_info}")

    records = risk_info.get("data", {}).get("records", [])
    if not records or len(records) == 0:
        logger.info("No risk records to send.")
        return

    # 2. Extract unique owners from the records
    unique_owners = set()
    for record in records:
        owners = record.get("owner", [])
        for owner in owners:
            unique_owners.add(owner)

    logger.info(f"Found {len(unique_owners)} unique owners")

    # 3. Initialize message sender
    risk_sender = IMessageSender(
        client=clients.get_lark_client(RobotType.Risk)
    )

    # 4. For each owner, query their specific risk list and send message
    for owner in unique_owners:
        try:
            # Query this specific owner's risk list
            owner_risk_info = risk_client.get_risk_info(
                username=owner
            )

            data = owner_risk_info.get("data", {})

            owner_records = data.get("records", [])
            if not owner_records:
                logger.info(f"No risk records for owner {owner}")
                continue

            json_card = build_risk_card(owner_records, data)

            request: BatchGetIdUserRequest = BatchGetIdUserRequest.builder() \
                .user_id_type("user_id") \
                .request_body(BatchGetIdUserRequestBody.builder()
                              .emails([owner + "@52tt.com"])
                              .include_resigned(True)
                              .build()) \
                .build()

            response = risk_sender.lark_msg_sender.mo.client.contact.v3.user.batch_get_id(request)
            if response.success():
                risk_sender.send_custom_json_card_to_user(
                    json_card=json_card,
                    user_id=json.loads(response.raw.content)['data']['user_list'][0]['user_id']
                )
                logger.info(f"Sent risk card to {owner} with {len(owner_records)} records.")
                time.sleep(1)
        except Exception as e:
            logger.error(f"Failed to send risk card to {owner}: {e}")


def get_risk_list(email: str) -> str:
    risk_client = RiskClient()
    try:
        owner_risk_info = risk_client.get_risk_info(
            username=email.split("@")[0]
        )

        owner_records = owner_risk_info.get("data", {}).get("records", [])
        if not owner_records:
            logger.info(f"No risk records for owner {email}")
            return ""

        formatted_risks = ""
        for risk in owner_records:
            formatted_risks += f"""
        • {risk['name']} ({risk['resource_type']})
          - 风险等级: {risk['risk_level']}
          - 资源名称：{risk['name']} 
          - 资源类型：{risk['resource_type']}
          - cmdb_id：{risk['cmdb_id']}
          - 风险类型: {risk['risk_type']}
          - 风险提示: {risk['risk_tip']}
          - 过期时间: {risk['expire_time']}
        """
        return formatted_risks
    except Exception as e:
        logger.error(f"Failed to send risk card to {email}: {e}")
        return ""


def get_risk_card(user_info):
    risk_client = RiskClient()
    try:
        owner_risk_info = risk_client.get_risk_info(
            page_limit=20,
            username=user_info.name
        )

        risk_sender = IMessageSender(
            client=clients.get_lark_client(RobotType.Risk)
        )

        data = owner_risk_info.get("data", {})
        owner_records = data.get("records", [])
        # Build and send card for this owner
        json_card = build_risk_card(owner_records, data)
        risk_sender.send_custom_json_card_to_user(
            json_card=json_card,
            user_id=user_info.id
        )
    except Exception as e:
        logger.error(f"Failed to send risk card to {user_info.name}: {e}")
        raise e


app.scheduler.add_job(
    send_risk_list,
    trigger='cron',
    hour=9,
    minute=0,
    second=0,
    id=app.scheduler.job_id("send_risk_list")
)
