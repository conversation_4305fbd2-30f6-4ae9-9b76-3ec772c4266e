"""
TODO
1. 完全的蒸馏数据, 需要从 Langfuse 中获取, 然后转换为 ShareGptSchema 对象
2. 这里主要是提取通识, 让LLM观察整个处理轨迹, 然后抽取出来通识, 类似于蒸馏,用于训练小模型
"""

import datetime as dt
import json
import os
from html import escape

from langfuse.api.resources.commons.types.observations_view import ObservationsView
from loguru import logger

from src.helper.utils import get_langfuse_client
from src.schedule.eval.eval_generation_task import EvalGenerationTask
from src.schedule.eval.schema import LanggraphActiveNode, ShareGptSchema
from src.schedule.eval.utils import (
    fetch_all_pages_generations,
    genneration_to_share_gpt_format,
    get_date_range,
)


def filter_generation(generations: list[ObservationsView]) -> list[ObservationsView]:
    """过滤生成数据

    Args:
        generations (list[ObservationsView]): 生成数据列表
    """
    eval_generation_task = EvalGenerationTask()
    all_data = []
    for generation in generations:
        if eval_generation_task._process_json_generation(generation):
            all_data.append(generation)
    return all_data


async def collect_data_to_langfuse(days_back=7, end_date=dt.datetime.now()):
    """将从langfuse上获取数据, 分析过滤后
    数据推送到 Langfuse 的 dataset 中

    Args:
        share_gpt_data (ShareGptSchema): 对话数据对象
    """
    langfuse = get_langfuse_client()
    # 获取日期范围
    start_date, end_date = get_date_range(days_back, end_date)

    # 获取指定时间范围内的所有对话数据, 只获取点赞用户的反馈
    generations: list[ObservationsView] = fetch_all_pages_generations(
        langfuse_client=langfuse,
        from_timestamp=start_date,
        to_timestamp=end_date,
        filter_by_human_feedback=True,
    )
    # 过滤生成数据
    generations = filter_generation(generations)
    # 创建数据集
    date_range = f"{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}"
    node_dataset_map = {}
    for node in LanggraphActiveNode:
        dataset_name = f"{node.value}_{date_range}_{os.environ.get('LANGFUSE_TRACING_ENVIRONMENT')}"
        node_dataset_map[node] = dataset_name
        langfuse.create_dataset(
            name=dataset_name,
            # optional description
            description=f"{escape(date_range)}时间段用于训练 {escape(str(node.value))} 模型的数据",
            # optional metadata
            metadata={"author": "moyueheng"},
        )
    # 上传数据
    for generation in generations:
        try:
            langgraph_node = generation.metadata["langgraph_node"]
            langfuse.create_dataset_item(
                dataset_name=node_dataset_map[langgraph_node],
                input=generation.input,
                expected_output=generation.output,
                metadata=generation.metadata,
                source_trace_id=generation.trace_id,
                source_observation_id=generation.id,
            )
        except Exception as e:
            logger.error(f"Error pushing data to Langfuse: {e}")
            continue


def run(days_back=7, end_date=dt.datetime.now()):
    """主函数：获取并处理对话数据

    警告: 此函数已弃用,请使用 push_data_to_langfuse() 函数替代, 后续数据都先用langfuse的dataset管理

    Args:
        days_back (int): 需要获取的历史天数，默认7天
    """
    # 初始化 Langfuse 客户端
    langfuse = get_langfuse_client()

    # 获取日期范围
    start_date, end_date = get_date_range(days_back, end_date)

    # 获取指定时间范围内的所有对话数据, 只获取点赞用户的反馈
    generations: list[ObservationsView] = fetch_all_pages_generations(
        langfuse_client=langfuse,
        from_timestamp=start_date,
        to_timestamp=end_date,
        filter_by_human_feedback=True,
    )

    generations = filter_generation(generations)

    # 转换所有对话数据
    talker_train_data: set[ShareGptSchema] = set()
    model_train_data: set[ShareGptSchema] = set()
    planner_train_data: set[ShareGptSchema] = set()
    steps_executor_train_data: set[ShareGptSchema] = set()
    unknown_train_data: set[ShareGptSchema] = set()
    for generation in generations:
        try:
            if not generation.output:
                continue
            share_gpt_format = genneration_to_share_gpt_format(generation)
            langgraph_node = share_gpt_format.langgraph_node
            if share_gpt_format is None:
                continue
            if langgraph_node == "talker":
                # 筛选, talker就几种消息格式
                talker_train_data.add(share_gpt_format)
            elif langgraph_node == "model":
                model_train_data.add(share_gpt_format)
            elif langgraph_node == "planner":
                planner_train_data.add(share_gpt_format)
            elif langgraph_node == "steps_executor":
                steps_executor_train_data.add(share_gpt_format)
            else:
                unknown_train_data.add(share_gpt_format)
        except Exception as e:
            import traceback

            print(f"Error converting generation {generation.id}: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            continue

    # 创建输出目录和文件名
    # 保存所有类型的数据
    date_range = f"{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}"
    base_dir = f"data/langfuse/train_data/{date_range}"
    os.makedirs(base_dir, exist_ok=True)

    from src.schedule.eval.utils import convert_to_dict

    # 保存 talker 数据
    talker_filename = f"{base_dir}/talker_train_data_{date_range}.json"
    with open(talker_filename, "w") as f:
        json.dump(convert_to_dict(talker_train_data), f, indent=2, ensure_ascii=False)
    print(f"Saved {len(talker_train_data)} talker records to {talker_filename}")

    # 保存 model 数据
    model_filename = f"{base_dir}/model_train_data_{date_range}.json"
    with open(model_filename, "w") as f:
        json.dump(convert_to_dict(model_train_data), f, indent=2, ensure_ascii=False)
    print(f"Saved {len(model_train_data)} model records to {model_filename}")

    # 保存 planner 数据
    planner_filename = f"{base_dir}/planner_train_data_{date_range}.json"
    with open(planner_filename, "w") as f:
        json.dump(convert_to_dict(planner_train_data), f, indent=2, ensure_ascii=False)
    print(f"Saved {len(planner_train_data)} planner records to {planner_filename}")

    # 保存 steps_executor 数据
    steps_executor_filename = f"{base_dir}/steps_executor_train_data_{date_range}.json"
    with open(steps_executor_filename, "w") as f:
        json.dump(
            convert_to_dict(steps_executor_train_data), f, indent=2, ensure_ascii=False
        )
    print(
        f"Saved {len(steps_executor_train_data)} steps_executor records to {steps_executor_filename}"
    )

    # 保存未知数据
    unknown_filename = f"{base_dir}/unknown_train_data_{date_range}.json"
    with open(unknown_filename, "w") as f:
        json.dump(convert_to_dict(unknown_train_data), f, indent=2, ensure_ascii=False)
    print(f"Saved {len(unknown_train_data)} unknown records to {unknown_filename}")

    # 保存信息
    with open(f"{base_dir}/info_{date_range}.json", "w") as f:
        json.dump(
            {
                "date_range": date_range,
                "talker_num": len(talker_train_data),
                "model_num": len(model_train_data),
                "planner_num": len(planner_train_data),
                "steps_executor_num": len(steps_executor_train_data),
                "unknown_num": len(unknown_train_data),
            },
            f,
            indent=2,
            ensure_ascii=False,
        )
    print(f"Saved info to {f}")
