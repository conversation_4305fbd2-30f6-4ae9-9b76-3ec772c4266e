from typing import Optional, Union

import pandas as pd
from langgraph.graph.graph import CompiledGraph
from langgraph.store.base import BaseStore
from langgraph.types import All, Checkpointer, RetryPolicy
from qagent.meta.action_exector import BaseActionExecutor
from qagent.meta.workflow import WorkflowBuilder

from src.core.cognition.copilots.chat.nodes.schema import Session


class GetTrainDataByHistory(WorkflowBuilder):
    history_data_path = (
        "data/langfuse/history/question_eval_result_embeddings_cluster.xlsx"
    )

    class Config:
        arbitrary_types_allowed = True

    def build_workflow(
        self,
        checkpointer: Checkpointer = None,
        action_executor: Optional[BaseActionExecutor] = None,
        *,
        store: Optional[BaseStore] = None,
        interrupt_before: Optional[Union[All, list[str]]] = None,
        interrupt_after: Optional[Union[All, list[str]]] = None,
        debug: bool = False,
    ) -> CompiledGraph:
        from src.core.cognition.copilots.chat.nodes import Talker

        self.state_schema = Session
        self.add_node(Talker(msg_sender=self._msg_sender), retry=RetryPolicy())

        return self.compile(
            checkpointer=checkpointer,
            store=store,
            interrupt_before=interrupt_before,
            interrupt_after=interrupt_after,
            debug=debug,
        )

    def build(self, checkpointer: Checkpointer) -> CompiledGraph:
        workflow = self.build_workflow(checkpointer)
        import os

        png_path = (
            os.path.dirname(os.path.abspath(__file__))
            + "/"
            + "GetTrainDataByHistory.png"
        )
        workflow.get_graph().draw_mermaid_png(output_file_path=png_path)
        return workflow


def build_inputs(history_data_path: str):
    df = pd.read_excel(history_data_path)
    sessions = []
    for index, row in df.iterrows():
        session = Session(messages=[])
        sessions.append(session)
        session.history.append(row["question"])
        session.history.append(row["answer"])
    return session


async def run():
    history_data_path = (
        "data/langfuse/history/question_eval_result_embeddings_cluster.xlsx"
    )
    inputs = build_inputs(history_data_path)
    workflow = GetTrainDataByHistory()
    workflow.build(checkpointer=None)
    for input in inputs:
        await workflow.ainvoke(state=input)
