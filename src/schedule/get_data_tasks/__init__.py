import asyncio

from src.infra.app import app

from .get_train_data_by_history import run as get_train_data_by_history_run
from .get_train_data_task import collect_data_to_langfuse


def run_collect_data_to_langfuse():
    asyncio.run(collect_data_to_langfuse(7))


app.scheduler.add_job(
    run_collect_data_to_langfuse,
    trigger="cron",
    day_of_week="mon",
    hour=7,
    minute=0,
    second=0,
    id=app.scheduler.job_id("collect_data_to_langfuse"),
    replace_existing=True,
)

__all__ = ["get_train_data_by_history_run", "collect_data_to_langfuse"]
