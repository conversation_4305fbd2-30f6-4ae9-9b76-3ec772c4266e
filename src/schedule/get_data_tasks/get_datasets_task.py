"""
从 langfuse 获取某个 dataset 数据, 并保存到本地, 符合 ShareGptSchema 格式的数据
"""

import json

from langfuse.api.resources.dataset_items.types.paginated_dataset_items import (
    PaginatedDatasetItems,
)

from src.helper.utils import get_langfuse_client
from src.schedule.eval.schema import ShareGptSchema
from src.schedule.eval.utils import convert_to_dict, dataitem_to_share_gpt_format


def get_dataset_from_langfuse(dataset_name: str) -> list[ShareGptSchema]:
    """从 Langfuse 获取对话数据

    Args:
        days_back (int): 需要获取的历史天数，默认7天
    """
    langfuse = get_langfuse_client()
    dataset_items: PaginatedDatasetItems = langfuse.api.dataset_items.list(
        dataset_name=dataset_name
    )
    share_gpt_data: list[ShareGptSchema] = []
    for dataitem in dataset_items.data:
        share_gpt_data.append(dataitem_to_share_gpt_format(dataitem))
    return share_gpt_data


def save_share_gpt_data(share_gpt_data: list[ShareGptSchema], filename: str):
    """保存 ShareGptSchema 对象列表到文件

    Args:
        share_gpt_data (list[ShareGptSchema]): ShareGptSchema 对象列表
        filename (str): 文件名
    """
    data = convert_to_dict(share_gpt_data)
    with open(filename, "w") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def main(data_dir: str, dataset_name: str):
    """主函数"""
    import os

    os.makedirs(data_dir, exist_ok=True)
    share_gpt_data = get_dataset_from_langfuse(dataset_name)
    # 分类
    talker_train_data: set[ShareGptSchema] = set()
    model_train_data: set[ShareGptSchema] = set()
    planner_train_data: set[ShareGptSchema] = set()
    steps_executor_train_data: set[ShareGptSchema] = set()
    unknown_train_data: set[ShareGptSchema] = set()

    for share_gpt_data in share_gpt_data:
        if share_gpt_data.langgraph_node == "talker":
            talker_train_data.add(share_gpt_data)
        elif share_gpt_data.langgraph_node == "model":
            model_train_data.add(share_gpt_data)
        elif share_gpt_data.langgraph_node == "planner":
            planner_train_data.add(share_gpt_data)
        elif share_gpt_data.langgraph_node == "steps_executor":
            steps_executor_train_data.add(share_gpt_data)
        else:
            unknown_train_data.add(share_gpt_data)
    # 保存各类数据
    # 保存统计数据
    with open(f"{data_dir}statistic.json", "w") as f:
        json.dump(
            {
                "talker_train_data": len(talker_train_data),
                "model_train_data": len(model_train_data),
                "planner_train_data": len(planner_train_data),
                "steps_executor_train_data": len(steps_executor_train_data),
                "unknown_train_data": len(unknown_train_data),
            },
            f,
            indent=2,
            ensure_ascii=False,
        )
    save_share_gpt_data(list(talker_train_data), f"{data_dir}talker_train_data.json")
    save_share_gpt_data(list(model_train_data), f"{data_dir}model_train_data.json")
    save_share_gpt_data(list(planner_train_data), f"{data_dir}planner_train_data.json")
    save_share_gpt_data(
        list(steps_executor_train_data), f"{data_dir}steps_executor_train_data.json"
    )
    save_share_gpt_data(list(unknown_train_data), f"{data_dir}unknown_train_data.json")

    # 打印保存结果
    print(f"保存了 {len(talker_train_data)} 条talker数据")
    print(f"保存了 {len(model_train_data)} 条model数据")
    print(f"保存了 {len(planner_train_data)} 条planner数据")
    print(f"保存了 {len(steps_executor_train_data)} 条steps_executor数据")
    print(f"保存了 {len(unknown_train_data)} 条unknown数据")


if __name__ == "__main__":
    dataset_name = "chosen"
    data_dir = f"data/langfuse/train_data/{dataset_name}/"
    main(data_dir, dataset_name)
