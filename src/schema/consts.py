"""
constant
"""
APPNAME = "ops_brain"
NO_PROD_RUNTIME_ENV = "noprod"
TEST_RUNTIME_ENV = "test"
PROD_RUNTIME_ENV = "prod"
DEFAULT_ENV = NO_PROD_RUNTIME_ENV

# Redis Cache Prefix
CACHE_PREFIX = f"{APPNAME}"

CACHE_IMAGE_PREFIX = f"{CACHE_PREFIX}:images"
CACHE_LARK_MESSAGE_ID = f"{CACHE_PREFIX}:lark:message_id"
CACHE_LARK_CARD_ID = f"{CACHE_PREFIX}:lark:card_id"
# memory
CACHE_TASK_MEMORY = f"{CACHE_PREFIX}:memory:task"


# Console  Channel Prefix
SERVER_CHANNEL_PREFIX = f"{CACHE_PREFIX}:console:server"
CLIENT_CHANNEL_PREFIX = f"{CACHE_PREFIX}:console:client"

# Lark Message Queue
LARK_MSG_PRODUCE_STREAM_PREFIX = "lark-message-producer-stream"

# Console Message Queue
CONSOLE_MSG_PRODUCE_STREAM_PREFIX = "console-message-producer-stream"

# Message CROUP
MSG_CONSUMER_GROUP = "message-consumer-group"
