from functools import cache
from typing import Optional

from mem0 import Memory
from mem0.configs.base import (
    EmbedderConfig,
    LlmConfig,
    MemoryConfig,
    MemoryItem,
    VectorStoreConfig,
)
from mem0.utils.factory import EmbedderFactory
from settings import get_or_creat_settings_ins
from src.core.cognition.memory.prompts import (
    DEFAULT_UPDATE_MEMORY_PROMPT,
    FACT_RETRIEVAL_PROMPT,
)
from src.helper.utils import get_chat_model, get_embedder_model

settings = get_or_creat_settings_ins()


def __get_config() -> MemoryConfig:
    # TODO: 迁移到settings
    llm = get_chat_model()
    embedder = get_embedder_model()

    llm_config = LlmConfig(provider="langchain", config={"model": llm})

    embedder_config = EmbedderConfig(
        provider="langchain", config={"model": embedder, "embedding_dims": settings.mem0.embedding_model_dims}
    )

    vector_store_config = VectorStoreConfig(
        provider="milvus",
        config={
            "embedding_model_dims": settings.mem0.embedding_model_dims,
            "collection_name": settings.mem0.collection_name,
            "url": settings.milvus.uri,
            "token": settings.milvus.token,
            "metric_type": settings.mem0.metric_type,  # 默认走L2相似度, 也就是把距离作为score, 就会出现分数越小, 越相似
        },
    )

    custom_fact_extraction_prompt = FACT_RETRIEVAL_PROMPT
    custom_update_memory_prompt = DEFAULT_UPDATE_MEMORY_PROMPT
    config = MemoryConfig(
        llm=llm_config,
        embedder=embedder_config,
        vector_store=vector_store_config,
        custom_fact_extraction_prompt=custom_fact_extraction_prompt,
        custom_update_memory_prompt=custom_update_memory_prompt,
    )
    return config


class Mem0Memory(Memory):
    # TODO: rerank还没有实现
    def __init__(self, config: MemoryConfig):
        super().__init__(config)
        # qwem3的query和document是分开的
        self.query_embedding_model = EmbedderFactory.create(
            "langchain",
            {"model": get_embedder_model(is_query=True)},
            self.config.vector_store.config,
        )

    def _search_vector_store(
        self, query, filters, limit, threshold: Optional[float] = None
    ):
        embeddings = self.query_embedding_model.embed(query, "search")
        memories = self.vector_store.search(
            query=query, vectors=embeddings, limit=limit, filters=filters
        )

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
        ]

        core_and_promoted_keys = {
            "data",
            "hash",
            "created_at",
            "updated_at",
            "id",
            *promoted_payload_keys,
        }

        original_memories = []
        for mem in memories:
            memory_item_dict = MemoryItem(
                id=mem.id,
                memory=mem.payload["data"],
                hash=mem.payload.get("hash"),
                created_at=mem.payload.get("created_at"),
                updated_at=mem.payload.get("updated_at"),
                score=mem.score,
            ).model_dump()

            for key in promoted_payload_keys:
                if key in mem.payload:
                    memory_item_dict[key] = mem.payload[key]

            additional_metadata = {
                k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys
            }
            if additional_metadata:
                memory_item_dict["metadata"] = additional_metadata

            if threshold is None or mem.score >= threshold:
                original_memories.append(memory_item_dict)

        return original_memories


@cache
def get_mem0_memory() -> Mem0Memory:
    config = __get_config()
    return Mem0Memory(config)


if __name__ == "__main__":
    m = get_mem0_memory()
    print(m.search("今晚我打算看电影。有什么推荐吗？"))
