from src.core.schema.task import TaskList, TaskMeta, TaskStatus


def get_unfinished_task(state: TaskList) -> TaskMeta | None:
    """
    获取任务列表中第一个未完成的任务

    Args:
        state: 包含任务列表的状态对象

    Returns:
        TaskMeta | None: 返回第一个未完成的任务，如果所有任务都已完成则返回None。
        任务未完成的条件是:
        1. 任务没有结果(result is None)
        2. 任务状态为missing_context(缺少上下文信息)
    """
    if "tasks" not in state:
        return None
    for idx in range(len(state["tasks"])):
        if (
            state["tasks"][idx].result is None
            or state["tasks"][idx].result.status == TaskStatus.missing_context  # type: ignore
        ):
            return state["tasks"][idx]
    return None


def get_task_by_id(state: TaskList, task_id: int) -> TaskMeta | None:
    if "tasks" not in state or task_id < 0:
        return None
    for task in state["tasks"]:
        if task.id == task_id:
            return task
    return None


def format_task(task: TaskMeta) -> str:
    tast_status = (
        "已完成"
        if task.result and task.result.status == TaskStatus.finish
        else "未完成"
    )
    return f"{task.id}: {task.description}\n任务状态：{tast_status}"
