from typing import List, TypedDict

from langchain_core.messages import BaseMessage
from qagent.meta.workflow import MetaState

from src.core.schema.session import SessionStatus
from src.core.schema.task import TaskList


class SessionResponse(TypedDict):
    status: SessionStatus


class Session(MetaState, TaskList):
    history: List[BaseMessage]
    status: SessionStatus
    message_id: str
    chat_type: str
    summary: str 