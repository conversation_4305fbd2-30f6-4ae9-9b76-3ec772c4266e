from typing import Any, List

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from pydantic import BaseModel, Field

from src.helper.utils import PromptName, get_chat_model, get_prompt_from_langfuse

from .schema import Session

summary_prompt: List[tuple[str, str]] = get_prompt_from_langfuse(PromptName.SUMMARY)


class Summarizer(BaseModel, Runnable):
    """摘要生成器"""

    # 代理名称
    name: str = "summarizer"
    llm: BaseChatModel = Field(default_factory=lambda _: get_chat_model("summary"))
    prompt: ChatPromptTemplate = ChatPromptTemplate(summary_prompt)

    class Config:
        arbitrary_types_allowed = True

    def invoke(
        self, state: Session, config: RunnableConfig = None, **kwargs: Any
    ) -> dict[str, Any]:
        """
        调用摘要生成功能，处理会话历史并生成摘要。
        当历史消息为空时返回原状态，当对话轮次少于3轮时直接返回格式化的历史记录，
        否则使用LLM生成摘要。

        Args:
            state (Session): 当前会话状态，包含历史消息等信息
            config (RunnableConfig): LLM运行时配置参数
            **kwargs (Any): 额外的关键字参数，当前未使用

        Returns:
            dict[str, Any]: 包含摘要的字典，键为"summary"，值为摘要内容。
                          如果历史为空返回原状态，如果对话少于3轮返回格式化的历史记录，
                          否则返回LLM生成的摘要内容
        """
        history = state.get("history", [])
        if not history: # 跨session的history
            return {"summary": ""}
        history_str = self._format_history(history)

        if len(history) <= 3 * 2:  # 三轮对话不需要总结
            return {"summary": history_str}

        msgs = self.prompt.format_messages(
            history=history_str, current_msg=state["messages"][-1].content
        )
        summary = self.llm.invoke(msgs, config)

        return {
            "summary": summary.content,
        }

    def _format_history(self, history: List[BaseMessage]) -> str:
        return "\n".join(f"{message.type}: {message.content}" for message in history)
