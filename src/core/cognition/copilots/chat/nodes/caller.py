from typing import Any

from langchain_core.messages import AIMessage
from langchain_core.runnables import Runnable, RunnableConfig
from loguru import logger
from pydantic import BaseModel

from src.core.schema.session import SessionStatus
from src.core.schema.task import TaskStatus, UpdateTaskResult

from .schema import Session
from src.core.cognition.copilots.utils import get_unfinished_task


class Caller(BaseModel, Runnable):
    name: str = "caller"
    executor: Runnable

    class Config:
        arbitrary_types_allowed = True

    def invoke(
            self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Any:
        task = get_unfinished_task(state)
        assert task is not None
        rsp = self.executor.invoke({
            "task": task,
            "message_id": state["message_id"],
            "chat_type": state["chat_type"],
        }, config=config)
        logger.debug(f"caller invoke rsp: {rsp}")
        task_result = rsp["task"].result
        # 判断任务是否缺少上下文
        if task_result.status == TaskStatus.missing_context:
            # 如果缺少上下文,需要人工介入
            session_status = SessionStatus.human
        else:
            # 否则任务已完成
            session_status = SessionStatus.finish
        update_task_result = UpdateTaskResult(
            id=task.id,
            result=task_result
        )
        return {
            "tasks": update_task_result,
            "doing_task": task.id,
            "messages": AIMessage(content=task_result.result),
            "status": session_status,
            "message_id": state["message_id"],
        }

    async def ainvoke(
            self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Any:
        task = get_unfinished_task(state)
        assert task is not None
        rsp = await self.executor.ainvoke({
            "task": task,
            "message_id": state["message_id"],
            "chat_type": state["chat_type"],
        }, config=config)
        logger.debug(f"caller ainvoke rsp: {rsp}")
        return {
            "tasks": UpdateTaskResult(id=task.id, result=rsp["task"].result),
            "doing_task": task.id,
            "messages": AIMessage(content=rsp["task"].result.result),
            "status": (
                SessionStatus.finish
                if rsp["task"].result.status != TaskStatus.missing_context
                else SessionStatus.human
            ),
            "message_id": state["message_id"],
        }
