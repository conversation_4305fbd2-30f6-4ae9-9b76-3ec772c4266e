"""
Talker模块是一个基于LangChain的对话代理，专门用于处理运维相关的任务和对话。
它负责理解用户意图、管理任务生命周期，并与任务Agent协作完成工作。
"""

from datetime import datetime
from typing import Any, List

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.tools import tool
from loguru import logger
from pydantic import BaseModel, Field

from settings import get_or_creat_settings_ins
from src.core.cognition.memory import get_mem0_memory
from src.core.execution.toolkits import get_chat_mcp_summary
from src.core.perception.session import get_task_memory
from src.core.schema.session import SessionContext, SessionStatus
from src.core.schema.task import (
    DeleteTask,
    TaskMeta,
    TaskResult,
    TaskStatus,
    UpdateTaskResult,
)
from src.core.spec.imessage import IMessageSenderSpec
from src.helper.utils import PromptName, get_chat_model, get_prompt_from_langfuse
from src.infra.feedback import UserTaskFeedBack

from ...utils import get_task_by_id
from .schema import Session

get_or_creat_settings_ins()
settings = get_or_creat_settings_ins()


talker_prompt_template: list[tuple[str, str]] = get_prompt_from_langfuse(
    PromptName.TALKER_V2
)


class TasksModel(BaseModel):
    """任务模型，用于验证和序列化任务列表"""

    tasks: List[TaskMeta] = Field(description="任务列表")


@tool
def update_task(tasks: list[TaskMeta]) -> str:
    """Update a task."""

    return "任务更新成功"


@tool
def delete_task(task: DeleteTask) -> str:
    """Delete a task."""
    return "任务删除成功"


@tool
def add_task(tasks: list[TaskMeta]) -> str:
    """添加多个任务."""
    return "任务创建成功"


tools_map = {
    "update_task": update_task,
    "delete_task": delete_task,
    "add_task": add_task,
}


class Talker(BaseModel, Runnable):
    """
    Talker类是一个运维助手代理，负责：
    1. 理解用户意图
    2. 管理任务生命周期
    3. 与任务Agent协作
    4. 处理用户反馈: 只会去找用户要参数, 以免过度打扰 planer and executor
    """

    # 代理名称
    name: str = "talker"
    llm: BaseChatModel = Field(
        default_factory=lambda _: get_chat_model("talker").bind_tools([
            update_task,
            delete_task,
            add_task,
        ])
    )
    prompt: ChatPromptTemplate = ChatPromptTemplate(talker_prompt_template)
    msg_sender: IMessageSenderSpec = Field(..., description="消息发送器")

    class Config:
        arbitrary_types_allowed = True

    def build_system_messages(
        self,
        state: Session,
        sc: SessionContext,
        config: RunnableConfig,
        long_memory: str,
    ) -> List[BaseMessage]:
        """
        构建系统消息列表，包含任务状态和上下文信息

        Args:
            state: Session对象，包含当前会话状态、历史消息、任务列表等信息
            sc: SessionContext对象，包含用户信息、聊天类型等会话上下文

        Returns:
            List[BaseMessage]: 返回系统消息列表，包含:
            - 系统提示消息
            - 任务列表及其状态
            - 当前执行任务
            - 会话总结等信息
        """
        task_list = [
            f"{task.id}.{task.name}:\n任务描述： {task.description}\n任务状态：{self._get_task_status(task)}"
            for task in state["tasks"]
        ]
        doing_task_str = "没有正在进行的任务"
        if "doing_task" in state:
            doing_task_str = state["doing_task"]

        insturction_message: List[BaseMessage] = self.prompt.format_messages(
            tasks="---\n".join(task_list) if task_list else "当前无任何已识别任务",
            current_time=datetime.now(),
            env=f"{sc.user_info}",
            doing_task=doing_task_str,
            agent_scratchpad="",
            summary=state.get("summary", "当前无历史对话"),
            planer_desc=get_chat_mcp_summary(),
            long_memory=long_memory,
        )
        return insturction_message

    def _get_task_status(self, task: TaskMeta) -> str:
        """
        获取任务状态的字符串描述

        Args:
            task: 任务元数据

        Returns:
            任务状态的字符串描述
        """
        if not task.result:
            return "未开始"
        if task.result.status == TaskStatus.missing_context:
            return f"待补充信息\n 请补充如下信息=>{task.result.result}"
        elif task.result.status == TaskStatus.failed:
            return f"任务由于原因失败，无法执行，失败原因为{task.result.result}"
        elif task.result.status == TaskStatus.givenup:
            return f"放弃执行,原因为{task.result.result}"
        return f"已完成, 执行结果为{task.result.result}"

    def parse_result(
        self, result: AIMessage, state: Session, config: RunnableConfig
    ) -> dict[str, Any]:
        """
        解析结果
        """
        tool_calls = result.tool_calls
        update_state = {
            "status": SessionStatus.finish,
            "messages": AIMessage(content=result.content),
        }
        UserTaskFeedBack.get_or_create(config=config)
        for tool_call in tool_calls:
            tool_name = tool_call["name"]
            args = tool_call["args"]
            if (
                tool_name == "add_task"
            ):  # tools add 依赖 session的状态是不是p2p, 所以不能直接调用工具
                tasks_model: TasksModel = TasksModel(**args)
                update_state = {
                    "tasks": tasks_model.tasks,
                    "status": SessionStatus.agent,
                }
                if state["chat_type"] == "p2p":
                    self.msg_sender.send_stream_mission_to_user(
                        msg={
                            "content": tasks_model.model_dump()["tasks"],
                            "session_id": "none",
                        },
                        user_id=config["configurable"]["session_context"].user_info.id,
                    )
                    # 只记录单一任务的情况
                if len(tasks_model.tasks) == 1:
                    tsk = tasks_model.tasks[0]
                    get_task_memory().record_task(
                        config["configurable"]["thread_id"],
                        tsk.name,
                        tsk.description,
                    )
            elif tool_name == "update_task":
                tasks_model: TasksModel = TasksModel(**args)
                update_state = {
                    "tasks": tasks_model.tasks,
                    "status": SessionStatus.agent,
                }

            elif tool_name == "delete_task":
                delete_task = DeleteTask(**args["task"])
                t = UpdateTaskResult(
                    id=delete_task.id,
                    name="delete",
                    result=TaskResult(
                        status=TaskStatus.givenup,
                        result=delete_task.reason,
                    ),
                )
                update_state = {
                    "tasks": t,
                    "status": SessionStatus.finish,
                    "messages": AIMessage(
                        content=f"任务{delete_task.name}已被取消，原因为{delete_task.reason}"
                    ),
                }
        return update_state

    def format_user_message(
        self, sorted_history_msgs: List[BaseMessage]
    ) -> List[BaseMessage]:
        """
        合并相邻的msg, 如果相邻的msg是同一个角色, 则合并, 因为我们的任务是异步,所以可能会存在两条相邻的消息是一样的
        """
        res = []
        # 应该需要进行合并msg, 如果相邻的msg是同一个角色, 则合并
        for msg in sorted_history_msgs:
            if msg.type == "human":
                if res and res[-1].type == "human":
                    res[-1].content += msg.content
                else:
                    res.append(msg)
            elif msg.type == "ai":
                if res and res[-1].type == "ai":
                    res[-1].content += msg.content
                else:
                    res.append(msg)
            else:
                res.append(msg)
        return res

    def _get_long_memory(self, user_id: str, query: str) -> str:
        """
        获取长期记忆
        """
        result = "<长期记忆>\n"
        memory = get_mem0_memory()
        if query == "你记住了我什么":  # 特殊指令, 直接返回所有记忆
            memory = memory.search(query, user_id=user_id, threshold=None)
        else:
            memory = memory.search(
                query, user_id=user_id, threshold=settings.mem0.threshold
            )
        for mem in memory["results"]:
            logger.info(f"long memory: {mem['memory'], mem['score']}")
            result += f"- {mem['memory']}\n"
        result += "</长期记忆>"
        return result

    def invoke(
        self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> dict[str, Any]:
        """
        同步调用方法，处理用户输入并返回响应

        Args:
            state: 当前会话状态
            config: 运行配置
            **kwargs: 其他参数

        Returns:
            处理结果

        Raises:
            AssertionError: 当配置无效时抛出异常
        """
        assert config is not None and "configurable" in config
        try:
            # raise ValueError("test") # 实验一下error重试
            if "doing_task" in state:
                task = get_task_by_id(state, state["doing_task"])
                if task and task.result:
                    if task.result.status == TaskStatus.givenup:
                        return {
                            "status": SessionStatus.finish,
                            "messages": AIMessage(content=f"任务{task.name}已取消"),
                        }
                    elif task.result.status == TaskStatus.finish:
                        return {
                            "status": SessionStatus.finish,
                            "messages": AIMessage(content=task.result.result),
                        }

            session_context = config["configurable"]["session_context"]
            # long memory
            if settings.mem0.enable:
                long_memory = self._get_long_memory(
                    session_context.user_info.email, state["messages"][-1].content
                )
            else:
                long_memory = ""
            system_messages = self.build_system_messages(
                state=state, sc=session_context, config=config, long_memory=long_memory
            )
            # 不能直接作为msg, 需要转成类似于总结一样
            user_messages = self.format_user_message(state["messages"])

            base_input = system_messages + user_messages
            result = self.llm.invoke(base_input, config)
            # 解析结果去更新状态
            update_state = self.parse_result(result, state, config)
            # 直接返回给用户
            logger.info(f"talker result: {update_state}")
            return update_state
        except Exception as e:
            logger.error(f"talker error: {e}")
            raise Exception(str(e))

    async def ainvoke(
        self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Any:
        """
        异步调用方法，处理用户输入并返回响应

        Args:
            state: 当前会话状态
            config: 运行配置
            **kwargs: 其他参数

        Returns:
            处理结果

        Raises:
            AssertionError: 当配置无效时抛出异常
        """
        assert config is not None and "configurable" in config
        try:
            # raise ValueError("test") # 实验一下error重试
            if "doing_task" in state:
                task = get_task_by_id(state, state["doing_task"])
                if task and task.result:
                    if task.result.status == TaskStatus.givenup:
                        return {
                            "status": SessionStatus.finish,
                            "messages": AIMessage(content=f"任务{task.name}已取消"),
                        }
                    elif task.result.status == TaskStatus.finish:
                        return {
                            "status": SessionStatus.finish,
                            "messages": AIMessage(content=task.result.result),
                        }

            session_context = config["configurable"]["session_context"]
            system_messages = self.build_system_messages(
                state=state, sc=session_context, config=config
            )
            # 不能直接作为msg, 需要转成类似于总结一样
            user_messages = self.format_user_message(state["messages"])
            base_input = system_messages + user_messages
            result = await self.llm.ainvoke(base_input, config)
            # 解析结果去更新状态
            result = self.parse_result(result, state, config)
            # 直接返回给用户
            logger.info(f"talker result: {result}")
            return result
        except Exception as e:
            logger.error(f"talker error: {e}")
            raise Exception(str(e))
