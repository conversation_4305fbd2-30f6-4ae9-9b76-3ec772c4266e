import json
from typing import Any

from langchain_core.runnables import RunnableConfig
from langgraph.utils.runnable import RunnableCallable
from loguru import logger
from pydantic import BaseModel

from src.core.schema.messaage import ChatType, SenderType
from src.core.schema.session import SessionStatus
from src.core.spec.imessage import IMessageSenderSpec
from src.infra.app import app
from src.infra.feedback import UserTaskFeedBack
from src.schema import CACHE_IMAGE_PREFIX
from .schema import Session


class Human(BaseModel, RunnableCallable):
    name: str = "human"
    msg_sender: IMessageSenderSpec

    class Config:
        arbitrary_types_allowed = True

    def send_msg(
            self, content: str, rsp_type: str, config: RunnableConfig | None = None
    ):
        """
        发送消息给用户

        Args:
            content: 消息内容
            rsp_type: 响应类型
            config: 运行配置，包含会话上下文等信息

        Raises:
            AssertionError: 当config为空或不包含configurable时抛出异常
        """
        assert config and "configurable" in config
        session = config["configurable"]["session_context"]
        self.msg_sender.send_msg_to_user(
            rsp_type,
            {
                "content": content,
                "session_id": config["configurable"]["thread_id"],
            },
            session.user_info.id,
        )

    def reply_msg(self, content: str, rsp_type: str, state: Session, config: RunnableConfig) -> None:
        """
        回复消息给用户，支持p2p和群聊两种模式

        Args:
            content: 消息内容
            rsp_type: 响应类型
            state: 会话状态，包含消息ID、聊天类型等信息
            config: 运行配置，包含会话上下文等信息

        Raises:
            AssertionError: 当config为空或不包含configurable时抛出异常
        """
        assert config and "configurable" in config
        session = config["configurable"]["session_context"]

        images = []
        images_str = app.redis_cli.get(CACHE_IMAGE_PREFIX + ":" + config["configurable"]["thread_id"])
        app.redis_cli.delete(CACHE_IMAGE_PREFIX + ":" + config["configurable"]["thread_id"])
        if images_str:
            images = json.loads(images_str)

        if state["chat_type"] == ChatType.P2P:
            self.msg_sender.send_stream_message_to_user(
                rsp_type=rsp_type,
                msg={
                    "content": content,
                    "session_id": config["configurable"]["thread_id"],
                },
                user_id=session.user_info.id
            )
            # 存在图片则发送图片
            if images:
                self.msg_sender.send_msg_to_user(
                    rsp_type=rsp_type,
                    msg={
                        "images": images,
                        "session_id": config["configurable"]["thread_id"],
                    },
                    user_id=session.user_info.id
                )
        else:
            self.msg_sender.reply_msg_to_user(
                rsp_type,
                {
                    "content": content,
                    "images": images,
                    "session_id": config["configurable"]["thread_id"],
                },
                state["message_id"],
            )

    def response(
            self,
            state: Session,
            config: RunnableConfig | None = None,
    ) -> None:
        rsp_type = SenderType.Human if state["status"] == SessionStatus.human else SenderType.Final
        # self.send_msg(state["messages"][-1].content, rsp_type, config)  # type: ignore
        logger.info(f"[reply_msg] ({rsp_type}) {state['messages'][-1].content}")
        self.reply_msg(state["messages"][-1].content, rsp_type, state, config)

        UserTaskFeedBack.update_field(
            config=config,
            end_type=rsp_type
        )
        return None

    def invoke(
            self,
            state: Session,
            config: RunnableConfig | None = None,
            **kwargs: Any,
    ) -> None:
        result = self.response(state, config=config)
        return result

    async def ainvoke(
            self,
            state: Session,
            config: RunnableConfig | None = None,
            **kwargs: Any,
    ) -> None:
        return self.invoke(state, config=config)
