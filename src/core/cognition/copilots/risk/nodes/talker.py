import json
from datetime import datetime
from typing import Any, Dict, List

from langchain.output_parsers import OutputFixingParser
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from loguru import logger
from pydantic import BaseModel, Field

from src.core.schema.session import SessionContext, SessionStatus
from src.core.schema.task import TaskMeta, TaskResult, TaskStatus, UpdateTaskResult
from src.core.spec.imessage import IMessageSenderSpec
from src.helper.utils import get_chat_model
from src.infra.feedback import UserTaskFeedBack
from src.infra.output.json import JsonMultiformatOutputParser
from src.schedule import get_risk_list

from .schema import Session
from ...utils import get_task_by_id

intent_prompt_template = """
## 角色定义
你是一个专业的运维风险治理助手（Ops Assistant），名字是"jarvis-风险治理"（中文名字是"知了-风险治理"）。
你由技术保障线开发维护，专注于运维风险治理。

## 核心功能
1. 仅围绕“当前用户待处理风险列表”制定风险治理任务，任务管理（创建/更新/删除/状态跟踪）
2. 将制定的风险治理任务交由任务Agent执行，协助任务Agent完成工作，根据任务Agent返回，收集任务信息。
3. 你仅回答与风险治理有关信息，其他不相关问题你都拒绝回答。
4. 当用户没有待处理风险，不进行治理。
5. 只有用户要治理/处理风险时才创建风险治理任务。
6. 严格根据用户问题的意图，选择不同模型进行输出。

## 工作原则
- 仅围绕风险治理回答用户问题
- 只能基于用户最新的消息和“当前用户待处理风险列表”创建和更新任务
- 严格使用输出规范中指定JSON格式输出
- 禁止从历史记录中获取答案直接输出，必须使用指定的模版输出。
- 用户放弃或取消的任务绝不能再创建，无论任何理由。
- 一次仅处理一个风险，禁止同时处理多个风险，
- 当存在多个风险时，一定要用户选择一个风险再处理，禁止不经用户选择直接治理风险

## 环境信息
{env}

## 系统信息
- 所属团队：技术保障部
- 所属公司：趣丸科技有限公司
- 地理位置：中国广州
- 系统时间：{current_time}
- 语言：中文

## 输出规范
！！！请严格按照要求，使用输出规范中的模 版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
！！！请严格按照要求，使用输出规范中的模版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
{format_instructions}

## 任务处理规范
### 1. 任务识别
- 准确判断风险治理相关请求

### 2. 协作要求
- 与任务Agent保持同步
- 确保风险信息传递准确
- 及时更新任务状态

## 当前任务状态
- 用户任务列表与任务状态：
  {tasks}
  
- 当前执行任务ID：{doing_task}

## 当前用户待处理风险列表
{risks}

## 特别注意
！！！请严格按照要求，使用输出规范中的模版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
！！！请严格按照要求，使用输出规范中的模版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
！！！请严格按照要求，使用输出规范中的模 版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
！！！请严格按照要求，使用输出规范中的模版，按照模版JSON结构返回，禁止直接输出字符串，禁止使用非模版结构JSON。
"""

FORMAT_INSTRUCTIONS = """
四种输出模版均为JSON格式，根据用户当前意图，选择一个模版进行输出:

1.创建/更新任务
```json
{{
    "tasks":[
        {{
            "id": "int, 任务唯一标识符，新任务自增，更新任务保持原id",
            "name": "string, 简明的任务名称, 例如：用户xxx想要处理什么风险",
            "description": "string, 详细的任务描述，仅包含风险详细描述，不能省略，不包含评价性内容，不用改变用户意图"
        }}
    ]
}}
```

2.放弃任务
当用户主动放弃/取消任务时使用
```json
{{
    "delete":
        {{
            "id": "int, 任务唯一标识符",
            "name": "string, 简明的任务名称",
            "reason": "string, 放弃任务的原因"
        }}
}}
```

3.信息收集
```json
{{
    "human": "string, 清晰、具体的信息请求，只有任务状态为待补充信息时才可使用, 每次仅询问必要信息，严格禁止主动收集信息"
}}
```

4.回答用户
```json
{{
    "message": "string, 回答用户的内容"
}}
```
请严格按照要求，使用这四种输出模版，按照模版JSON结构返回。
"""


class TasksModel(BaseModel):
    tasks: List[TaskMeta] = Field(description="任务列表")


class Talker(BaseModel, Runnable):
    name: str = "talker"
    llm: BaseChatModel = Field(default_factory=lambda _: get_chat_model("talker"))
    prompt: SystemMessagePromptTemplate = SystemMessagePromptTemplate.from_template(
        template=intent_prompt_template
    )
    output_parser: BaseOutputParser = Field(
        default_factory=lambda _: OutputFixingParser.from_llm(
            parser=JsonMultiformatOutputParser(format_instructions=FORMAT_INSTRUCTIONS),
            llm=get_chat_model("fix"),
        )
    )
    msg_sender: IMessageSenderSpec = Field(..., description="消息发送器")

    class Config:
        arbitrary_types_allowed = True

    def get_task_messages(
            self, state: Session, sc: SessionContext
    ) -> List[BaseMessage]:
        task_list = [
            f"{task.id}.{task.name}:\n任务描述： {task.description}\n任务状态：{self._get_task_status(task)}"
            for task in state["tasks"]
        ]

        doing_task_str = "没有正在进行的任务"
        if "doing_task" in state:
            doing_task_str = state["doing_task"]

        risks = get_risk_list(sc.user_info.email)
        insturction_message = self.prompt.format_messages(
            tasks="---\n".join(task_list) if task_list else "当前无任何已识别任务",
            current_time=datetime.now(),
            env=f"{sc.user_info}",
            format_instructions=FORMAT_INSTRUCTIONS,
            doing_task=doing_task_str,
            agent_scratchpad="",
            risks=risks,
        )
        return state["system_messages"] + insturction_message

    def _get_task_status(self, task: TaskMeta) -> str:
        if not task.result:
            return "未开始"
        if task.result.status == TaskStatus.missing_context:
            return f"待补充信息\n 请补充如下信息=>{task.result.result}"
        elif task.result.status == TaskStatus.failed:
            return f"任务由于原因失败，无法执行，失败原因为{task.result.result}"
        elif task.result.status == TaskStatus.givenup:
            return f"放弃执行,原因为{task.result.result}"
        return f"已完成, 执行结果为{task.result.result}"

    def parse_result(
            self, response: str, state: Session, config: RunnableConfig
    ) -> Dict:
        try:
            UserTaskFeedBack.get_or_create(config=config)

            json = self.output_parser.parse(response)
            logger.info(f"tasks: {json}")
            if "tasks" in json:
                model = TasksModel.model_validate(json)
                task_result = {"tasks": model.tasks, "status": SessionStatus.agent}
                if state["chat_type"] == "p2p":
                    self.msg_sender.send_stream_mission_to_user(
                        msg={
                            "content": model.model_dump()["tasks"],
                            "session_id": "none",
                        },
                        user_id=config["configurable"]["session_context"].user_info.id,  # type: ignore
                    )

                return task_result
            elif "delete" in json:
                t = UpdateTaskResult(
                    id=json["delete"]["id"],
                    name="delete",
                    result=TaskResult(
                        status=TaskStatus.givenup,
                        result=json["delete"]["reason"],
                    ),
                )
                return {
                    "tasks": t,
                    "status": SessionStatus.finish,
                    "messages": AIMessage(
                        content=f"任务{json['delete']['name']}已被取消，原因为{json['delete']['reason']}"
                    ),
                }
            elif "human" in json:
                return {
                    "status": SessionStatus.human,
                    "messages": AIMessage(content=json["human"]),
                }
            elif "message" in json:
                return {
                    "status": SessionStatus.finish,
                    "messages": AIMessage(content=json["message"]),
                }
            else:
                raise Exception(f"json parse error: {json}")
        except Exception as e:
            logger.error(f"json parse error: {e}")
            raise Exception(f"json parse error: {e}")

    def invoke(
            self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Any:
        assert config is not None and "configurable" in config
        if "doing_task" in state:
            task = get_task_by_id(state, state["doing_task"])
            if task and task.result and task.result.status == TaskStatus.givenup:
                return {
                    "status": SessionStatus.finish,
                    "messages": AIMessage(content=f"任务{task.name}已取消"),
                }
            elif task and task.result and task.result.status == TaskStatus.finish:
                return {
                    "status": SessionStatus.finish,
                    "messages": AIMessage(content=task.result.result),
                }
        systems = self.get_task_messages(
            state=state, sc=config["configurable"]["session_context"]
        )
        history_list = self.get_history_list(state)

        input = (
                systems + history_list + state["messages"]
        )
        response = self.llm.invoke(input, config)
        assert isinstance(response.content, str)

        return self.parse_result(response.content, state, config)

    async def ainvoke(
            self, state: Session, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Any:
        assert config is not None and "configurable" in config

        if "doing_task" in state:
            task = get_task_by_id(state, state["doing_task"])
            if task and task.result and task.result.status == TaskStatus.givenup:
                return {
                    "status": SessionStatus.finish,
                    "messages": AIMessage(content=f"任务{task.name}已取消"),
                }
            elif task and task.result and task.result.status == TaskStatus.finish:
                return {
                    "status": SessionStatus.finish,
                    "messages": AIMessage(content=task.result.result),
                }
        systems = self.get_task_messages(
            state=state, sc=config["configurable"]["session_context"]
        )
        history_list = self.get_history_list(state)
        input = (
                systems + history_list + state["messages"]
        )
        response = await self.llm.ainvoke(input, config)
        assert isinstance(response.content, str)

        return self.parse_result(response.content, state, config)

    def get_history_list(self, state):
        history_list = []
        if "history" in state:
            history_list = state["history"]
            for message in history_list:
                if isinstance(message, AIMessage):
                    # 如果 message.content 已经是 JSON 并包含 "message" 键，则跳过
                    try:
                        content_dict = json.loads(message.content)
                        if isinstance(content_dict, dict) and "message" in content_dict:
                            continue
                    except json.JSONDecodeError:
                        pass
                    # 将原始内容包装成 {"message": content}
                    message.content = json.dumps({"message": message.content})
        return history_list
