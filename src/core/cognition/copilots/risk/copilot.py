from typing import Any, Optional, Type, Union

from langgraph.graph import END
from langgraph.graph.graph import CompiledGraph
from langgraph.store.base import BaseStore
from langgraph.types import All, Checkpointer, RetryPolicy
from qagent.meta.action_exector import BaseActionExecutor
from qagent.meta.workflow import WorkflowBuilder

from src.core.cognition.copilots.chat.nodes import Caller, Human
from src.core.cognition.copilots.chat.nodes.schema import Session, SessionResponse
from src.core.cognition.copilots.risk.nodes.talker import Talker
from src.core.cognition.copilots.utils import get_unfinished_task
from src.core.schema.session import SessionStatus
from src.core.spec.imessage import IMessageSenderSpec


class RiskCopilot(WorkflowBuilder):
    def __init__(
        self, msg_sender: IMessageSenderSpec, planner: CompiledGraph, **data: Any
    ):
        self._msg_sender = msg_sender
        self._planner = planner
        super().__init__(**data)

    state_schema: Optional[Type[Any]] = Session
    output: Optional[Type[Any]] = SessionResponse

    class Config:
        arbitrary_types_allowed = True

    def fsm(self, state: Session):
        """
        有限状态机，用于控制会话状态的转换

        当会话状态为完成时，检查是否还有未完成的任务。如果有，则将状态转为agent继续执行任务。
        否则保持当前状态不变。

        Args:
            state: Session对象，包含当前会话状态

        Returns:
            dict: 包含更新后的状态信息
                - doing_task: 正在执行的任务ID (仅当有未完成任务时)
                - status: 更新后的会话状态
        """
        if state["status"] == SessionStatus.finish:
            task = get_unfinished_task(state)
            if task:
                return {"doing_task": task.id, "status": SessionStatus.agent}
        return {"status": state["status"]}

    def is_in_agent_turn(self, state: Session):
        if state["status"] == SessionStatus.agent:
            return "caller"
        return "human"

    def session_status(self, state: Session):
        if (
                state["status"] == SessionStatus.human
                or state["status"] == SessionStatus.finish
        ):
            return END
        # 如果是状态是agent, 则调用caller
        return "caller"

    def build_workflow(
            self,
            checkpointer: Checkpointer = None,
            action_executor: Optional[BaseActionExecutor] = None,
            *,
            store: Optional[BaseStore] = None,
            interrupt_before: Optional[Union[All, list[str]]] = None,
            interrupt_after: Optional[Union[All, list[str]]] = None,
            debug: bool = False,
    ) -> CompiledGraph:
        self.add_node(Talker(msg_sender=self._msg_sender), retry=RetryPolicy())
        self.add_node("caller", Caller(executor=self._planner))
        self.add_node(Human(msg_sender=self._msg_sender))
        self.add_node("fsm", self.fsm)

        self.set_entry_point("talker")
        self.add_conditional_edges("talker", self.is_in_agent_turn, ["caller", "human"])
        self.add_edge("caller", "human")
        self.add_edge("human", "fsm")
        self.add_conditional_edges("fsm", self.session_status, ["caller", END])

        return self.compile(
            checkpointer=checkpointer,
            store=store,
            interrupt_before=interrupt_before,
            interrupt_after=interrupt_after,
            debug=debug,
        )

    def build(self, checkpointer: Checkpointer) -> CompiledGraph:
        workflow = self.build_workflow(checkpointer)
        # png_path = os.path.dirname(os.path.abspath(__file__)) + "/" + "Copilot.png"
        # workflow.get_graph().draw_mermaid_png(output_file_path=png_path)
        return workflow
