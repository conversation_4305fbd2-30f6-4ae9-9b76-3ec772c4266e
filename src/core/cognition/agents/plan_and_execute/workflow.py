from typing import Any, Dict, Optional, Type, Union

from langgraph.graph import END
from langgraph.graph.graph import CompiledGraph
from langgraph.store.base import BaseStore
from langgraph.types import All, Checkpointer, RetryPolicy
from loguru import logger
from qagent.meta.action_exector import BaseActionExecutor, ToolkitsActionExecutor
from qagent.meta.workflow import WorkflowBuilder

from src.core.schema.task import TaskResult, TaskStatus
from src.core.spec.imessage import IMessageSenderSpec

from .planner import Planner
from .schema import PlanStatus, TaskState
from .steps import StepsExecutor


class PlanAndExecute(WorkflowBuilder):
    state_schema: Optional[Type[Any]] = TaskState

    def action(self, state: TaskState) -> str:
        if "status" in state:
            logger.info(f"当前状态: {state['status'].status}")
            if (
                state["status"].status == PlanStatus.Finish
                or state["status"].status == PlanStatus.Failed
                or state["status"].status == PlanStatus.Human
                or state["status"].status == PlanStatus.Givenup
            ):
                logger.info("执行完成,返回final")
                return "final"
            elif state["status"].status == PlanStatus.Step:
                logger.info("继续执行步骤,返回steps_executor")
                return "steps_executor"
            elif state["status"].status == PlanStatus.Replan:
                logger.info("重新规划,返回planner")
                return "planner"
        return "planner"

    def finish_plan(self, state: TaskState) -> Dict[str, Any]:
        if state["status"].status == PlanStatus.Human:
            status = TaskStatus.missing_context
        elif state["status"].status == PlanStatus.Failed:
            status = TaskStatus.failed
        elif state["status"].status == PlanStatus.Givenup:
            status = TaskStatus.givenup
        else:
            status = TaskStatus.finish
        task = state["task"]
        assert task is not None
        task.result = TaskResult(result=state["status"].content, status=status)

        return {"task": task}

    def build_workflow(
        self,
        checkpointer: Checkpointer = None,
        action_executor: Optional[BaseActionExecutor] = None,
        msg_sender: Optional[IMessageSenderSpec] = None,
        *,
        store: Optional[BaseStore] = None,
        interrupt_before: Optional[Union[All, list[str]]] = None,
        interrupt_after: Optional[Union[All, list[str]]] = None,
        debug: bool = False,
    ) -> CompiledGraph:
        assert action_executor is not None and isinstance(
            action_executor, ToolkitsActionExecutor
        )
        toolkits = action_executor.get_toolkits()
        planner = Planner(toolkits=toolkits, msg_sender=msg_sender)
        self.add_node("planner", planner, retry=RetryPolicy())

        self.add_node(
            "steps_executor",
            StepsExecutor(
                runner=action_executor, toolkits=toolkits, msg_sender=msg_sender
            ),
        )

        self.add_node("final", self.finish_plan)

        self.set_entry_point("planner")
        self.add_edge("planner", "steps_executor")
        self.add_conditional_edges(
            "steps_executor", self.action, ["steps_executor", "planner", "final"]
        )
        self.add_edge("final", END)

        return self.compile(
            checkpointer=checkpointer,
            store=store,
            interrupt_before=interrupt_before,
            interrupt_after=interrupt_after,
            debug=debug,
        )

    def setup(
        self,
        checkpointer: Checkpointer = None,
        action_executor: Optional[BaseActionExecutor] = None,
        msg_sender: Optional[IMessageSenderSpec] = None,
    ):
        workflow = self.build_workflow(checkpointer, action_executor, msg_sender)
        # import os
        # png_path = os.path.dirname(os.path.abspath(__file__)) + "/" + "PlanAndExecute.png"
        # workflow.get_graph().draw_mermaid_png(output_file_path=png_path)
        return workflow
