import hashlib
import json
from typing import Any, Optional, Type, TypedDict, Union

from langchain_core.messages import AIMessage
from langchain_core.messages.tool import tool_call
from langgraph.graph import END
from langgraph.graph.graph import CompiledGraph
from langgraph.store.base import BaseStore
from langgraph.types import All, Checkpointer
from langgraph.utils.runnable import RunnableCallable
from qagent.meta.action_exector import BaseActionExecutor
from qagent.meta.workflow import MetaState, WorkflowBuilder


class FunctionState(MetaState):
    question: str
    function: str
    args: dict[str, Any]


class FunctionResponse(TypedDict):
    content: str


class FunctionPlan(WorkflowBuilder):
    state_schema: Optional[Type[Any]] = FunctionState
    output: Optional[Type[Any]] = FunctionResponse

    def __init__(
            self,
            name,
            **data: Any,
    ):
        self.name = name
        super().__init__(**data)

    def build_message(self, state: FunctionState):
        call_id = hashlib.md5(
            json.dumps(state["args"], sort_keys=True).encode()
        ).hexdigest()
        call = tool_call(name=state["function"], args=state["args"], id=call_id)
        return {
            "messages": [
                AIMessage(content="", tool_calls=[call]),
            ]
        }

    def parse_response(self, state: FunctionState):
        last_message = state["messages"][-1]
        return {"content": last_message.content}

    def build_workflow(
            self,
            checkpointer: Checkpointer = None,
            action_executor: Optional[BaseActionExecutor] = None,
            *,
            store: Optional[BaseStore] = None,
            interrupt_before: Optional[Union[All, list[str]]] = None,
            interrupt_after: Optional[Union[All, list[str]]] = None,
            debug: bool = False,
    ) -> CompiledGraph:
        self.add_node("input", RunnableCallable(self.build_message))

        assert action_executor is not None
        self.add_node("action", action_executor)

        self.add_node("output", RunnableCallable(self.parse_response))

        self.set_entry_point("input")
        self.add_edge("input", "action")
        self.add_edge("action", "output")
        self.add_edge("output", END)
        return self.compile(
            checkpointer=checkpointer,
            store=store,
            interrupt_before=interrupt_before,
            interrupt_after=interrupt_after,
            debug=debug,
        )
