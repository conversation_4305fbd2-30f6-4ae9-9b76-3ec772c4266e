import copy
from typing import Any, Dict, List, Optional, Sequence

from langchain.output_parsers import OutputFixingParser
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from loguru import logger
from pydantic import BaseModel, Field
from qagent.meta.toolkit import Qtoolkit

from src.helper.utils import PromptName, get_chat_model, get_prompt_from_langfuse
from src.infra.output.json import JsonMultiformatOutputParser

from .schema import (
    InnerToolName,
    PlanResult,
    PlanStatus,
    Step,
    StepActionStatus,
    TaskState,
)
from .utils import parse_action


def get_step_by_id(steps: List[Step], step_id: int) -> Step | None:
    """根据步骤ID获取对应的步骤对象

    Args:
        steps (List[Step]): 步骤列表
        step_id (int): 步骤ID

    Returns:
        Step | None: 找到的步骤对象，如果未找到则返回None
    """
    for step in steps:
        if step.id == step_id:
            return step
    return None


step_prompt_template: list[tuple[str, str]] = get_prompt_from_langfuse(
    PromptName.STEPS_EXECUTOR
)

format_instructions = """
你必须使用以下任意一种格式输出：
1.如果存在还没有执行过的步骤，使用此格式返回，输出格式为：
```json
{
    "next_action": "step",
    "id": "int, 计划id",
    "thought": "string, 思考过程，不要更改原始内容",
    "action": "string, 操作类型与工具参数，使用python函数调用格式，例如 action(arg1=number,arg2='escaped string',arg3=None)"
}
```
2. 如果任务完成/需要重新思考/或放弃任务时，输出格式为：
```json
{
    "next_action": "replan 或者 final 或者 givenup，具体意义参考以下描述",
    "thought": "string, 思考过程，不要更改原始内容",
    "result": "string, 任务结果, 根据当前的目标，将结果以人类阅读友好的格式返回"
}
```
next_action选项有三种类型:
    1. replan: 表示需要重新规划，当用户放弃执行时不能执行replan。
    2. final: 表示任务完成输出结果是答案, 答案要尽可能全面, 不要丢失信息。
    3. givenup: 表示用户放弃执行，用户明确拒绝继续执行工具时不需要重新规划。

以下情况需要执行replan:
1. 某一步执行结果不达预期，导致下一步的依赖无法获取(不包括用户拒绝/放弃执行)
2. 关注<当前目标>，任务规划不合理, 需要重新规划, 并在thought中描述原因
3. 其他计划无法执行情况
"""


class StepsExecutor(BaseModel, Runnable):
    """执行步骤的主要类，负责管理和执行任务的各个步骤

    主要功能:
    1. 执行任务步骤并分析结果
    2. 处理图片分析和图表转换
    3. 管理任务状态和执行流程
    4. 处理异常情况和用户交互

    属性:
        name (str): 执行器名称
        toolkits (Sequence[Qtoolkit]): 可用工具集合
        runner (Runnable): 实际执行步骤的运行器
        llm (BaseChatModel): 用于步骤分析的语言模型
        prompt (HumanMessagePromptTemplate): 提示模板
        output_parser (BaseOutputParser): 输出解析器
    """

    name: str = "steps"
    toolkits: Sequence[Qtoolkit]
    runner: Runnable
    llm: BaseChatModel = Field(default_factory=lambda _: get_chat_model("steps"))
    prompt: ChatPromptTemplate = ChatPromptTemplate(step_prompt_template)
    output_parser: BaseOutputParser = Field(
        default_factory=lambda _: OutputFixingParser.from_llm(
            parser=JsonMultiformatOutputParser(format_instructions=format_instructions),
            llm=get_chat_model("fix"),
        )
    )

    class Config:
        arbitrary_types_allowed = True

    def get_message(
        self, state: TaskState, past_steps: List[Step]
    ) -> List[BaseMessage]:
        """生成系统消息，包含任务状态和历史步骤信息

        Args:
            state (TaskState): 当前任务状态
            past_steps (List[Step]): 已执行的步骤列表

        Returns:
            List[BaseMessage]: 格式化的系统消息
        """
        past_steps_format = [str(step) for step in past_steps]
        steps_str = [str(step) for step in state["steps"]]

        messages = self.prompt.format_messages(
            # tool_descriptions=format_tool_desc(self.toolkits),
            format_instructions=format_instructions,
            target=f"{state['task'].name}:{state['task'].description}",
            global_steps="\n---\n".join(steps_str),
            past_steps="\n---\n".join(past_steps_format),
        )
        return messages

    def _get_current_action(self, state: TaskState) -> Dict[str, Any]:
        """获取当前需要执行的动作

        Args:
            state (TaskState): 当前任务状态

        Returns:
            Dict[str, Any]: 当前动作信息
        """
        if state.get("action"):  # 上一步已定义的动作
            return state["action"]
        else:
            # 新计划的第一步
            return {
                "next_action": "step",
                "id": state["steps"][0].id,
                "action": state["steps"][0].action,
            }

    def _handle_special_action(
        self, function: str, args: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """处理特殊动作（如Help或Failed）

        Args:
            function (str): 动作函数名
            args (Dict[str, Any]): 动作参数

        Returns:
            Optional[Dict[str, Any]]: 如果是特殊动作则返回相应状态，否则返回None
        """
        if function == InnerToolName.Help:
            return {
                "status": PlanResult(status=PlanStatus.Human, content=args["input"]),
            }
        elif function == InnerToolName.Failed:
            return {
                "status": PlanResult(status=PlanStatus.Failed, content=args["input"]),
            }
        return None

    def invoke(
        self, input: TaskState, config: Optional[RunnableConfig] = None, **kwargs: Any
    ) -> Dict[str, Any]:
        """执行单个步骤并分析结果

        Args:
            input (TaskState): 任务状态，包含以下字段:
                - action (Dict[str, Any]): 当前执行的动作，包含next_action、id和action字段
                - steps (List[Step]): 所有执行步骤的列表
                - past_steps (List[Step]): 已执行步骤的历史记录
                - message_id (str): 消息ID
                - task (TaskMeta): 任务信息
                - chat_type (str): 对话类型
                - reason (str): 任务原因
            config (Optional[RunnableConfig], optional): 运行时配置. Defaults to None.
            **kwargs (Any): 额外参数

        Returns:
            Dict[str, Any]: 用于更新下一个节点的state
        """
        # 获取当前步骤信息
        action = self._get_current_action(input)
        past_steps: List[Step] = input.get("past_steps", [])

        # 解析动作
        function, args = parse_action(action["action"])

        # 处理特殊动作
        special_result = self._handle_special_action(function, args)
        # 获取当前步骤
        step = get_step_by_id(input["steps"], action["id"])
        past_step = copy.deepcopy(step)

        assert step is not None
        if special_result:
            # 特殊动作也需要往 past_step 中记录, 因为方便后续数据清理也更符合语义
            past_steps.append(past_step)
            special_result.update({"past_steps": past_steps})
            return special_result
        
        # 执行步骤
        result = self.runner.invoke(
            {"question": step.plan, "function": function, "args": args},
            config,
            **kwargs,
        )
        logger.debug(f"steps executor result:\n {result['content']}")

        # 更新步骤信息
        step.action = action["action"]
        past_step.result = result["content"]
        past_steps.append(past_step)

        # 获取消息并继续处理
        messages = self.get_message(input, past_steps)
        return self._retry_invoke(messages, config, past_steps, input)

    async def ainvoke(
        self, input: TaskState, config: Optional[RunnableConfig] = None, **kwargs: Any
    ) -> Dict[str, Any]:
        """异步执行单个步骤并分析结果

        功能与invoke方法相同，但以异步方式执行

        Args:
            input (TaskState): 任务状态
            config (Optional[RunnableConfig], optional): 运行时配置
            **kwargs (Any): 额外参数

        Returns:
            Dict: 执行结果，格式与invoke方法相同
        """
        # 获取当前步骤信息
        action = self._get_current_action(input)
        past_steps = input.get("past_steps", [])

        # 解析动作
        function, args = parse_action(action["action"])

        # 处理特殊动作
        special_result = self._handle_special_action(function, args)
        if special_result:
            return special_result

        # 获取当前步骤
        step = get_step_by_id(input["steps"], action["id"])
        assert step is not None

        # 执行步骤
        result = await self.runner.ainvoke(
            {"question": step.plan, "function": function, "args": args},
            config,
            **kwargs,
        )

        # 更新步骤信息
        step.action = action["action"]
        past_step = copy.deepcopy(step)
        past_step.result = result["content"]
        past_steps.append(past_step)

        # 获取消息并继续处理
        messages = self.get_message(input, past_steps)
        return await self._async_retry_invoke(messages, config, past_steps, input)

    def _retry_invoke(
        self,
        messages: list[BaseMessage],
        config: RunnableConfig,
        past_steps: List[Step],
        state: TaskState,
    ) -> Dict[str, Any]:
        for attempt in range(3):
            rsp = self.llm.invoke(messages, config)
            try:
                assert isinstance(rsp.content, str)

                logger.debug(f"steps executor output:\n {rsp.content}")
                try:
                    output = self.output_parser.parse(rsp.content)
                except Exception:
                    raise Exception(
                        f"⚠️ 上次输出结果:{rsp.content}, 结果结构不符合要求, 请按照要求重新生成"
                    )

                logger.debug(f"steps executor parse:\n {output}, type: {type(output)}")
                res = self._process_llm_response(output, past_steps, state)
                return res
            except Exception as e:
                logger.warning(f"Steps 第 {attempt + 1} 次失败: {e}")
                if attempt < 2:
                    messages.extend([AIMessage(content=rsp.content), HumanMessage(content=str(e))])
                else:
                    raise e

    async def _async_retry_invoke(
        self,
        messages: list[BaseMessage],
        config: RunnableConfig,
        past_steps: List[Step],
        state: TaskState,
    ) -> Dict[str, Any]:
        for attempt in range(3):
            rsp = await self.llm.ainvoke(messages, config)
            try:
                assert isinstance(rsp.content, str)

                logger.debug(f"steps executor output:\n {rsp.content}")
                try:
                    output = self.output_parser.parse(rsp.content)
                except Exception:
                    raise Exception(
                        f"⚠️ 上次输出结果:{rsp.content}, 结果结构不符合要求, 请按照要求重新生成"
                    )

                logger.debug(f"steps executor parse:\n {output}, type: {type(output)}")
                return self._process_llm_response(output, past_steps, state)
            except Exception as e:
                logger.warning(f"Steps 第 {attempt + 1} 次失败: {e}")
                if attempt < 2:
                    messages.extend([AIMessage(content=rsp.content), HumanMessage(content=str(e))])
                else:
                    raise e

    def _process_llm_response(
        self, output: dict, past_steps: List[Step], state: TaskState
    ) -> Dict[str, Any]:
        """处理LLM响应的通用逻辑

        Args:
            output (dict): LLM的输出结果
            past_steps (List[Step]): 已执行的步骤列表
            state (TaskState): 当前任务状态

        Returns:
            Dict[str, Any]: 更新后的任务状态
        """
        if output["next_action"] == StepActionStatus.Step:
            return {
                "past_steps": past_steps,
                "action": output,
                "steps": state["steps"],
                "status": PlanResult(status=PlanStatus.Step, content="继续执行"),
            }
        elif output["next_action"] == StepActionStatus.Final:
            return {
                "past_steps": past_steps,
                "status": PlanResult(
                    status=PlanStatus.Finish, content=output["result"]
                ),
            }
        elif output["next_action"] == StepActionStatus.Givenup:
            return {
                "past_steps": past_steps,
                "status": PlanResult(
                    status=PlanStatus.Givenup, content=output["result"]
                ),
            }
        elif output["next_action"] == StepActionStatus.Replan:
            return {
                "past_steps": past_steps,
                "status": PlanResult(
                    status=PlanStatus.Replan, content=output["result"]
                ),
                "action": {},  # NOTE: 重规划, 这个action是上一个任务的最后一个steps的action, 会导致执行器不会去执行新计划到第一个step, 所以要设置为空
            }
        else:
            return {"past_steps": past_steps}
