from typing import Any, Dict, List, Sequence

from langchain.output_parsers import OutputFixingParser
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.output_parsers import <PERSON><PERSON>utputParser, PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from loguru import logger
from pydantic import BaseModel, Field
from qagent.meta.toolkit import Qtoolkit

from src.core.perception.session import get_task_memory
from src.core.schema.messaage import ChatType
from src.core.schema.session import SessionContext
from src.core.spec.imessage import IMessageSenderSpec
from src.helper.utils import PromptName, get_chat_model, get_prompt_from_langfuse
from .prompt import FAILED_DESC_PROMPT, HELP_DESC_PROMPT, FINAL_DESC_PROMPT
from .schema import PlanActionResponse, TaskState
from .utils import format_tool_desc, get_tools_names, parse_action

planner_prompt_template: list[tuple[str, str]] = get_prompt_from_langfuse(
    PromptName.PLANNER
)

obs_instructions_template = """
## Current Observation
原始的计划是:
<plans>
{plans}
</plans>

已经执行过如下计划：
<past_steps>
{past_steps}
</past_steps>
"""


format_instructions = """
```json
{
    "steps": [
        {
            "id": "int, 计划步骤的唯一标识符，每次都从1开始递增",
            "plan": "string, 步骤的详细描述，包含所需参数信息，包含当前步骤要实现的目的，不包含评价性内容",
            "action": "string, 工具调用格式，使用Python函数调用语法，如：action(arg1=number,arg2='escaped string value', arg3=None),可选参数可以不填充"
        }
    ]
}
```
"""


class Planner(BaseModel, Runnable):
    name: str = "planner"
    toolkits: Sequence[Qtoolkit]
    llm: BaseChatModel = Field(default_factory=lambda _: get_chat_model("planner"))
    prompt: ChatPromptTemplate = ChatPromptTemplate(planner_prompt_template)
    output_parser: BaseOutputParser = Field(
        default_factory=lambda _: OutputFixingParser.from_llm(
            parser=PydanticOutputParser(pydantic_object=PlanActionResponse),
            llm=get_chat_model("fix"),
        )
    )
    msg_sender: IMessageSenderSpec = Field(..., description="消息发送器")
    retry_times: int = Field(default=3, description="重试次数")

    class Config:
        arbitrary_types_allowed = True

    def invoke(
        self, state: TaskState, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Dict[str, Any]:
        session_context = config["configurable"]["session_context"]
        messages = self.get_messages(state, session_context)
        return self._retry_invoke(messages, config, state, session_context)

    async def ainvoke(
        self, state: TaskState, config: RunnableConfig | None = None, **kwargs: Any
    ) -> Dict[str, Any]:
        session_context = config["configurable"]["session_context"]
        messages = self.get_messages(state, session_context)
        return await self._async_retry_invoke(messages, config, state, session_context)

    def get_messages(self, state: TaskState, sc: SessionContext) -> List[BaseMessage]:
        past_steps_str = ""
        plans_str = ""
        if "steps" in state and len(state["steps"]):
            past_steps = [str(p) for p in state["past_steps"]]
            plans = [str(p) for p in state["steps"]]
            past_steps_str = "\n---\n".join(past_steps)
            plans_str = "\n---\n".join(plans)
            obs_instructions = obs_instructions_template.format(
                plans=plans_str,
                past_steps=past_steps_str,
            )
        else:
            obs_instructions = ""
        task = state["task"]
        messages = self.prompt.format_messages(
            tool_descriptions=format_tool_desc(self.toolkits)
            + HELP_DESC_PROMPT
            + FAILED_DESC_PROMPT
            + FINAL_DESC_PROMPT,
            task=f"{task.name}:{task.description}",
            env=f"{sc.user_info}",
            obs_instructions=obs_instructions,
            format_instructions=format_instructions,
        )

        return messages

    def _process_response(
        self,
        response: BaseMessage,
        state: TaskState,
        session_context: SessionContext,
        config: RunnableConfig,
    ) -> Dict[str, Any]:
        """处理LLM响应的通用逻辑

        Args:
            response: LLM的响应
            state: 任务状态
            session_context: 会话上下文

        Returns:
            dict: 处理后的结果, 用于更新state
        """
        reason = response.additional_kwargs.get("reasoning_content", "")
        if reason:
            logger.info(f"===== task: {state['task']} reason =====\n {reason}")
            get_task_memory().record_thought(config["configurable"]["thread_id"], reason)

        assert isinstance(response.content, str)

        if "```json" in response.content.strip():
            logger.info(f"===== task: {state['task']} body =====\n {response.content}")
        else:
            logger.warning(f"===== task: {state['task']} content为空 =====\n ")
            raise Exception(
                "生成计划结构不符合要求，无法解析成steps，请重新生成："
                + response.content
            )
        
        def extract_json_content(content: str) -> str: # 兼容一些非推理模型喜欢在content加入非纯json返回到情况
            if "```json" not in content:
                return content
            return content.split("```json")[1].split("```")[0]
        json_content = extract_json_content(response.content)
        
        try:
            plan: PlanActionResponse = self.output_parser.parse(json_content)
            # task memory
            plan_list: List[str] = []
            for step in plan.steps:
                plan_list.append(f"{step.id}.{step.plan}; {step.action}")
            get_task_memory().record_plan(config["configurable"]["thread_id"], plan_list)
        except Exception as e:
            logger.error(f"生成计划结构不符合要求，无法解析成steps，请重新生成：{e}")
            raise Exception(
                "生成计划结构不符合要求，无法解析成steps，请重新生成："
                + response.content
            )

        tool_names = get_tools_names(self.toolkits)
        for step in plan.steps:
            if not any(step.action.startswith(tool) for tool in tool_names):
                raise ValueError(
                    f"计划步骤：{response.content}，其中使用了不存在的工具：{step.action}，请重新生成"
                )
        # 还需要对第一个step的格式进行解析, 如果不对也要重试修复
        first_step = plan.steps[0]
        function, args = parse_action(first_step.action)

        logger.info(f"===== task: {state['task']} plan =====\n {plan.steps}")

        if state["chat_type"] == ChatType.P2P:
            is_replan = len(state.get("past_steps", [])) != 0
            self.msg_sender.send_stream_plan_to_user(
                msg={
                    "session_id": "none",
                    "content": plan.model_dump()["steps"],
                },
                user_id=session_context.user_info.id,
                is_replan=is_replan,
            )

        return {
            "steps": plan.steps,  # 应该是会被覆盖
            "reason": reason,
            "message_id": state["message_id"],
            # "past_steps": [], # 执行过的步骤一个一直累积, 不应该被清空
            "chat_type": state["chat_type"],
            "action": {},  # NOTE: 可能是重规划, 这个action是上一个任务的最后一个steps的action, 会导致执行器不会去执行新计划到第一个step, 所以要设置为空
        }

    def _retry_invoke(
        self,
        messages: list[BaseMessage],
        config: RunnableConfig,
        state: TaskState,
        session_context: SessionContext,
    ) -> Dict[str, Any]:
        for attempt in range(self.retry_times):
            response = self.llm.invoke(messages, config)
            try:
                return self._process_response(response, state, session_context, config)
            except Exception as e:
                logger.warning(f"解析失败（第 {attempt + 1} 次），错误：{e}")
                if attempt < self.retry_times - 1:
                    messages.extend(
                        [
                            AIMessage(content=response.content),
                            HumanMessage(content=str(e)),
                        ]
                    )  # 保持一条AI消息, 一条用户消息
                else:
                    raise e

    async def _async_retry_invoke(
        self,
        messages: list[BaseMessage],
        config: RunnableConfig,
        state: TaskState,
        session_context: SessionContext,
    ) -> Dict[str, Any]:
        for attempt in range(self.retry_times):
            response = await self.llm.ainvoke(messages, config)
            try:
                return self._process_response(response, state, session_context, config)
            except Exception as e:
                logger.warning(f"解析失败（第 {attempt + 1} 次），错误：{e}")
                if attempt < self.retry_times - 1:
                    messages.extend(
                        [
                            AIMessage(content=response.content),
                            HumanMessage(content=str(e)),
                        ]
                    )
                else:
                    raise e
