from enum import Enum
from typing import Annotated, Any, Dict, List, TypedDict

from pydantic import BaseModel, Field

from src.core.schema.task import TaskMeta


class InnerToolName(str, Enum):
    Failed = "FAILED"
    Help = "HELP"
    Final = "FINAL"


class StepActionStatus(str, Enum):
    Step = "step"
    Final = "final"
    Givenup = "givenup"
    Replan = "replan"


class PlanStatus(Enum):
    Finish = "finish"
    Human = "missing_context"
    Failed = "failed"
    Step = "step"
    Givenup = "givenup"
    Replan = "replan"


class Step(BaseModel):
    id: int = Field(
        description="计划id，每个计划都有一个唯一的id,id为整数，从1开始自增"
    )
    plan: str = Field(
        description="计划描述，需要包含对话中关于计划的参数信息，不要对计划做任何其他评价"
    )
    action: str = Field(
        description="操作类型与工具参数，使用python函数调用格式，例如action(arg1=number,arg2='escaped string')"
    )
    result: str | None = Field(
        default="",
        description="操作结果，如果操作失败，请返回失败原因，如果操作成功，请返回操作结果",
    )

    def __str__(self) -> str:
        """对action统一格式化输出

        Returns:
            str: 格式化后的action字符串
        """
        base = f"action_id: {self.id}, action: {self.action}, description: {self.plan}"
        if self.result:
            return f"{base}, result: {self.result}"  # 执行过的计划
        return base  # 原计划


class PlanActionResponse(BaseModel):
    steps: List[Step] = Field(description="返回的计划,不要返回已经执行过的计划")


class PlanResult(BaseModel):
    status: PlanStatus
    content: str



class TaskState(TypedDict):
    task: TaskMeta
    reason: str
    steps: List[Step]
    past_steps: List[Step]
    status: PlanResult
    message_id: str
    chat_type: str
    action: Dict[str, Any]
