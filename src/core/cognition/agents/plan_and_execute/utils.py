import ast
import json
import re
from typing import Any, Dict, Sequence, Tuple

from langchain_core.utils.function_calling import convert_to_openai_function
from loguru import logger
from qagent.meta.toolkit import Qtoolkit

from .schema import InnerToolName


def format_tool_desc(toolkits: Sequence[Qtoolkit]) -> str:
    tools_desc = []
    for toolkit in toolkits:
        for tool in toolkit.get_tools():
            tools_desc.append(
                json.dumps(
                    convert_to_openai_function(tool), indent=2, ensure_ascii=False
                )
            )
    return "\n".join(tools_desc)


def get_tools_names(toolkits: Sequence[Qtoolkit]) -> set[str]:
    tool_names = set()
    for toolkit in toolkits:
        for tool in toolkit.get_tools():
            tool_names.add(tool.name)
    for tool_name in InnerToolName:
        tool_names.add(tool_name.value)
    return tool_names


def parse_action(action: str) -> tuple[str, Dict[str, Any]]:
    try:
        if action.startswith("action:"):
            action = action[len("action:") :].strip()

        action = action.replace("null", "None")
        action = action.replace("\n", " ")
        expr = ast.parse(action, mode="eval").body
        if isinstance(expr, ast.Call):
            func_name = expr.func.id if isinstance(expr.func, ast.Name) else None
            kwargs = {}
            for kw in expr.keywords:
                try:
                    value = ast.literal_eval(kw.value)
                except Exception:
                    value = None
                kwargs[kw.arg] = value

            return func_name, kwargs
        else:
            raise ValueError("不是有效的函数调用格式")
    except Exception as e:
        logger.error(f"parse action error: {action}, {str(e)}")
        raise ValueError("action格式错误")


_dep_pattern = r"\$(\d+)\.(\w+)"


def extract_dependencies(args: str) -> Tuple[int, Any]:
    import warnings

    warnings.warn("该函数已废弃，请使用新方法替代", DeprecationWarning, stacklevel=2)

    match = re.match(_dep_pattern, args)
    if match:
        return int(match.group(1)), ast.literal_eval(match.group(2))
    raise ValueError("依赖格式错误")
