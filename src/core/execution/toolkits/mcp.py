import asyncio
import time
from concurrent.futures import Future
from typing import Any, Optional

from langchain_core.tools import BaseTool, StructuredTool
from loguru import logger
from mcp import ClientSession, ListToolsResult, Tool
from mcp.client.sse import sse_client
from pydantic import Field, create_model
from qagent.meta.toolkit import Qtoolkit

from settings import get_or_creat_settings_ins
from src.core.execution.toolkits.mcp_tool import MC<PERSON><PERSON>ool
from src.core.schema.session import UserInfo
from src.infra import utils
from src.infra.app import app

config = get_or_creat_settings_ins()


class MCPServerToolkit(Qtoolkit):
    name: str
    domain: str
    description: str
    email: str | None = None

    def load_tools(self) -> list[BaseTool]:
        loop = utils.get_event_loop()
        tools = loop.run_until_complete(self.load_tools_async())
        return tools

    async def load_tools_async(self) -> list[BaseTool]:
        result_tools: list[BaseTool] = []
        try:
            connections = config.mcp.servers or {}
            for server_name, connection in connections.items():
                connection_dict = connection.copy()

                url = (
                    connection_dict.pop("url")
                    + self.domain
                    + (self.email + "/" if self.email else "None/")
                )

                # 使用配置文件中的超时时间，适应长时间运行的工具加载
                sse_timeout = config.mcp.defaults.sse_read_timeout
                async with sse_client(url=url, sse_read_timeout=sse_timeout) as (
                    read,
                    write,
                ):
                    session = ClientSession(read, write)
                    async with session:
                        await session.initialize()
                        tools: ListToolsResult = await session.list_tools()

                        for tool in tools.tools:
                            mcp_tool = self.convert_mcp_tool_to_langchain_tool(
                                url, tool
                            )
                            result_tools.append(mcp_tool)
                        return result_tools
        except Exception as e:
            logger.error(f"Error loading {self.domain} tools: {e}")
            return result_tools

    def convert_mcp_tool_to_langchain_tool(
        self,
        url: str,
        tool: Tool,
    ) -> BaseTool:
        structured_tool = StructuredTool(
            name=tool.name,
            description=tool.description or "",
            args_schema=tool.inputSchema,
        )

        args_schema = self.dict_to_pydantic_model(
            structured_tool.args_schema, structured_tool.name + "Args"
        )

        mcp_tool = MCPTool(
            name=structured_tool.name,
            description=structured_tool.description,
            args_schema=args_schema,
            url=url,
            need_require_auth=tool.model_extra["need_require_auth"],
        )

        return mcp_tool

    def dict_to_pydantic_model(
        self, data: dict[str, Any], model_name: str = "DynamicModel"
    ) -> type:
        try:
            fields = {}
            properties = data.get("properties", {})
            required_fields = data.get("required", [])

            def parse_field(field_name: str, field_data: dict[str, Any]) -> None:
                field_type = field_data.get("type")
                description = field_data.get("description")
                is_required = field_name in required_fields

                def get_field_type(python_type):
                    if is_required:
                        return python_type, Field(description=description)
                    else:
                        # For optional fields, use None as default
                        return Optional[python_type], Field(
                            default=None, description=description
                        )

                if "anyOf" in field_data:
                    any_of_types = [item.get("type") for item in field_data["anyOf"]]
                    if "string" in any_of_types and "null" in any_of_types:
                        fields[field_name] = get_field_type(str)
                    else:
                        raise ValueError(
                            f"Unsupported anyOf types for {field_name}: {any_of_types}"
                        )
                elif field_type == "string":
                    fields[field_name] = get_field_type(str)
                elif field_type == "integer":
                    fields[field_name] = get_field_type(int)
                elif field_type == "number":
                    fields[field_name] = get_field_type(float)
                elif field_type == "boolean":
                    fields[field_name] = get_field_type(bool)
                elif field_type == "array":
                    array_items = field_data.get("items", {})
                    if array_items.get("type") == "string":
                        fields[field_name] = get_field_type(list[str])
                    elif array_items.get("type") == "integer":
                        fields[field_name] = get_field_type(list[int])
                    elif array_items.get("type") == "number":
                        fields[field_name] = get_field_type(list[float])
                    elif array_items.get("type") == "boolean":
                        fields[field_name] = get_field_type(list[bool])
                    else:
                        raise ValueError(
                            f"Unsupported array item type for {field_name}: {array_items.get('type')}"
                        )
                elif field_type == "object":
                    nested_properties = field_data.get("properties", {})
                    nested_required = field_data.get("required", [])
                    nested_data = {
                        "type": "object",
                        "properties": nested_properties,
                        "required": nested_required,
                    }
                    nested_model = self.dict_to_pydantic_model(
                        nested_data, field_name.capitalize() + "Model"
                    )
                    fields[field_name] = get_field_type(nested_model)
                elif field_type == "null":
                    fields[field_name] = (
                        None,
                        Field(default=None, description=description),
                    )
                else:
                    raise ValueError(
                        f"Unsupported field type for {field_name}: {field_type}"
                    )

            for key, value in properties.items():
                parse_field(key, value)

            # Create Pydantic model dynamically
            return create_model(model_name, **fields)
        except Exception as e:
            logger.error(f"Error converting dict to Pydantic model: {e}", exc_info=True)
            pass


def load_mcp_toolkits(user: UserInfo, toolkit_config: dict) -> list[MCPServerToolkit]:
    logger.info("Loading MCP toolkits...")
    mcp_toolkits = []
    future_list: list[Future] = []

    for toolkit_name, toolkit_info in toolkit_config.items():
        # Provide default description if not present
        description = toolkit_info.get(
            "description", toolkit_info.get("name", "Unknown toolkit")
        )

        future = app.submit_concurrent_task(
            MCPServerToolkit,
            name=toolkit_info["name"],
            domain=toolkit_info["domain"],
            description=description,
            email=user.email,
        )
        future_list.append(future)

    for future in future_list:
        while not future.done():
            time.sleep(0.1)
        toolkit = future.result()
        if toolkit:
            logger.info(f"Loaded MCP toolkit: {toolkit.name}")
            mcp_toolkits.append(toolkit)

    return mcp_toolkits


async def load_domain_prompt(toolkits: dict[str, Any]) -> dict[str, Any]:
    """
    Load domain prompts from configured MCP servers and update toolkit descriptions.

    Args:
        toolkits: Dictionary of toolkits to update with descriptions

    Returns:
        Updated toolkits dictionary with descriptions from MCP servers
    """
    if not toolkits:
        return {}

    # Create a deep copy to avoid modifying the original
    updated_toolkits = {k: v.copy() for k, v in toolkits.items()}

    # Ensure all toolkits have a description field as fallback
    for toolkit_id, toolkit in updated_toolkits.items():
        if "description" not in toolkit:
            toolkit["description"] = toolkit.get("name", "Unknown toolkit")

    # Get MCP connections from config
    connections = getattr(config, "mcp", None)
    if (
        not connections
        or not hasattr(connections, "servers")
        or not connections.servers
    ):
        logger.warning("No MCP connections configured")
        return updated_toolkits

    # Process each server sequentially to avoid task management issues
    for server_name, connection in connections.servers.items():
        try:
            await fetch_server_prompts(server_name, connection, updated_toolkits)
        except Exception as e:
            logger.error(f"Error processing server {server_name}: {e}")

    return updated_toolkits


async def fetch_server_prompts(
    server_name: str, connection: dict[str, Any], toolkits: dict[str, Any]
) -> None:
    """
    Fetch prompts from a single MCP server and update toolkit descriptions.

    Args:
        server_name: Name of the server
        connection: Connection details
        toolkits: Toolkits to update
    """
    try:
        connection_dict = connection.copy()
        base_url = connection_dict.pop("url", "")
        if not base_url:
            logger.error(f"Missing URL for server {server_name}")
            return

        url = f"{base_url}/domain/None/"

        # Use the sse_client as a standalone context manager
        # 使用配置文件中的超时时间，适应长时间运行的提示加载
        sse_timeout = config.mcp.defaults.sse_read_timeout
        async with sse_client(url=url, sse_read_timeout=sse_timeout) as (read, write):
            session = ClientSession(read, write)
            async with session:
                await session.initialize()
                prompts_result = await session.list_prompts()

                if not prompts_result or not hasattr(prompts_result, "prompts"):
                    logger.warning(f"No prompts returned from server {server_name}")
                    return
                update_toolkit_descriptions(toolkits, prompts_result.prompts)

    except Exception as e:
        logger.error(f"Error fetching prompts from {server_name}: {e}")


def update_toolkit_descriptions(toolkits: dict[str, Any], prompts: list[Any]) -> None:
    """
    Update toolkit descriptions with matching prompts.

    Args:
        toolkits: Toolkits to update
        prompts: List of prompts from server
    """
    prompt_dict = {prompt.name: prompt.description for prompt in prompts}

    for toolkit_id, toolkit in toolkits.items():
        toolkit_name = toolkit.get("name")
        if toolkit_name in prompt_dict:
            toolkit["description"] = prompt_dict[toolkit_name]
        else:
            # Ensure description field always exists, use name as fallback
            if "description" not in toolkit:
                toolkit["description"] = toolkit_name or "Unknown toolkit"
            logger.info(
                f"No prompt found for toolkit {toolkit_name}, using fallback description"
            )


def get_chat_mcp_toolkits(user: UserInfo) -> list[MCPServerToolkit]:
    """Get chat toolkits for a user."""
    try:
        toolkits = asyncio.run(load_domain_prompt(config.toolkits.chat))
    except Exception as e:
        logger.error(f"Error loading domain prompts for chat toolkits: {e}")
        # Fallback to original config with default descriptions
        toolkits = config.toolkits.chat
        for toolkit_id, toolkit in toolkits.items():
            if "description" not in toolkit:
                toolkit["description"] = toolkit.get("name", "Unknown toolkit")
    return load_mcp_toolkits(user, toolkits)


def get_risk_mcp_toolkits(user: UserInfo) -> list[MCPServerToolkit]:
    """Get risk toolkits for a user."""
    try:
        toolkits = asyncio.run(load_domain_prompt(config.toolkits.risk))
    except Exception as e:
        logger.error(f"Error loading domain prompts for risk toolkits: {e}")
        # Fallback to original config with default descriptions
        toolkits = config.toolkits.risk
        for toolkit_id, toolkit in toolkits.items():
            if "description" not in toolkit:
                toolkit["description"] = toolkit.get("name", "Unknown toolkit")
    return load_mcp_toolkits(user, toolkits)


def get_chat_mcp_summary() -> str:
    """Generate a summary of available chat toolkits."""
    summary = ""
    try:
        toolkits = asyncio.run(load_domain_prompt(config.toolkits.chat))
    except Exception as e:
        logger.error(f"Error loading domain prompts for chat summary: {e}")
        # Fallback to original config with default descriptions
        toolkits = config.toolkits.chat
        for toolkit_id, toolkit in toolkits.items():
            if "description" not in toolkit:
                toolkit["description"] = toolkit.get("name", "Unknown toolkit")

    for i, (toolkit_id, toolkit) in enumerate(toolkits.items()):
        description = toolkit.get("description", toolkit.get("name", "Unknown toolkit"))
        if isinstance(description, list) and description:
            description = description[0]
        summary += f"{i + 1}. {toolkit.get('name', 'Unnamed')}: {description}\n"
    return summary
