import ast
import asyncio
import datetime
import json
from typing import Any, Dict, List, Type

from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from loguru import logger
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import CallToolResult
from pydantic import BaseModel, Field

from settings import get_or_creat_settings_ins
from src.helper.graph import require_authorization
from src.helper.utils import get_chat_model
from src.infra import utils
from src.infra.app import app
from src.schema import CACHE_IMAGE_PREFIX


class MCPTool(BaseTool):
    name: str
    description: str
    need_require_auth: bool = False
    args_schema: Type[BaseModel] = Field(default_factory=lambda: BaseModel())
    url: str
    thread_id: str = ""

    def _run(
        self,
        config: RunnableConfig,
        **kwargs: Any,
    ) -> Any:
        session_ctx = config["configurable"].get("session_context", None)
        if session_ctx is None or session_ctx.user_info.email == "":
            return "请先登录"

        self.thread_id = config["configurable"]["thread_id"]

        if self.need_require_auth:
            require_auth_result = require_authorization(
                action=self.name,
                args=kwargs,
            )
            if require_auth_result != "yes":
                return f"用户明确拒绝继续执行工具：{self.name}"

        kwargs["email"] = session_ctx.user_info.email
        logger.info(f"call tool {self.name} with args {kwargs}")
        return utils.get_event_loop().run_until_complete(self._async_run(**kwargs))

    async def _async_run(self, **kwargs: Any) -> Any:
        config = get_or_creat_settings_ins()
        max_retries = config.mcp.defaults.max_retries
        retry_delay = config.mcp.defaults.retry_delay
        sse_timeout = config.mcp.defaults.sse_read_timeout
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 使用配置文件中的超时时间，适应长时间运行的工具
                async with sse_client(url=self.url, sse_read_timeout=sse_timeout) as (
                    read,
                    write,
                ):
                    session = ClientSession(read, write)
                    async with session:
                        await session.initialize()
                        result: CallToolResult = await session.call_tool(
                            self.name, kwargs
                        )
                        logger.info(f"call tool {self.name} result {result.content}")
                        return self.analysis_image(result.content[0].text)
            except Exception as e:
                retry_count += 1
                logger.error(f"MCP tool execution attempt {retry_count} failed: {e}")

                # 如果是连接相关的错误且还有重试机会，则重试
                if retry_count < max_retries and any(
                    keyword in str(e).lower()
                    for keyword in ["broken", "timeout", "connection", "sse", "stream"]
                ):
                    logger.info(
                        f"Retrying MCP tool {self.name} (attempt {retry_count + 1}/{max_retries}) after {retry_delay}s delay"
                    )
                    await asyncio.sleep(retry_delay)
                    continue

                # 其他错误或重试次数用完，返回错误信息
                return f"工具执行异常，已重试{retry_count}次：{str(e)}"

        return f"工具执行失败，重试{max_retries}次后仍然失败"

    def analysis_image(self, result: str):
        try:
            if self.name != "query_monitoring_data":
                return result
            content = ast.literal_eval(result)
            if "image" in content:
                description = f"""
                执行工具：{self.description}.
                执行结果：结果为图片，请结合执行计划对图片内容进行分析。
                - 如果图片为监控类数据图，则分析数据是否异常，出现异常给出优化建议，正常禁止给出优化建议。
                - 如果图片为普通类型图片（非监控图），请按执行计划进行常规内容分析，描述图片内容。
                """
                content_list: List[Dict[str, str]] = [
                    {"type": "text", "text": f"{description}"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{content['image']}"
                        },
                    },
                ]
                msg = HumanMessage(content=content_list)
                image_llm = get_chat_model("image")
                chain = ChatPromptTemplate.from_messages([msg]) | image_llm
                result = chain.invoke({}).content

                if (
                    "data" in content
                    and "result" in content["data"]
                    and len(content["data"]["result"]) > 0
                ):
                    self.convert_result_to_chart(
                        "监控折线图", content["data"]["result"], self.thread_id
                    )
                return result
            else:
                logger.info(f"image not found in result: {result}")
                return "工具未返回图片，无法进行分析"
        except Exception as e:
            logger.error(f"analysis image error: {e}")

    @staticmethod
    def convert_result_to_chart(title, result, thread_id: str):
        chart = {
            "type": "line",
            "title": {"text": title},
            "data": [{"id": "line", "values": []}],
            "xField": "time",
            "yField": "value",
            "seriesField": "name",
            "invalidType": "break",
            "point": {"visible": False},
            "axes": [
                {"orient": "left", "label": {"formatter": "{label}"}},
                {"orient": "bottom", "nice": True, "trimPadding": True},
            ],
            "label": {"visible": False},
            "legends": {
                "orient": "bottom",
                "selectMode": "multiple",
                "title": {
                    "visible": False,
                },
            },
        }

        values = chart["data"][0]["values"]
        selected_names = set()

        for entry in result:
            metric = entry.get("metric", {})
            line_name = ":".join(f"{v}" for k, v in sorted(metric.items()))
            selected_names.add(line_name)

            for ts, val in entry["values"]:
                time_str = datetime.datetime.fromtimestamp(ts).strftime("%H:%M")
                values.append({"name": line_name, "time": time_str, "value": val})

        cache_key = CACHE_IMAGE_PREFIX + ":" + thread_id
        images_str = app.redis_cli.get(cache_key)
        if images_str:
            images = json.loads(images_str)
            images.append(chart)
            app.redis_cli.set(cache_key, json.dumps(images), ex=600)
        else:
            app.redis_cli.set(cache_key, json.dumps([chart]), ex=600)
