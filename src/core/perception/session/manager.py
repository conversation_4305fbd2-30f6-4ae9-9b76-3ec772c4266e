from typing import List, Optional

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import AnyMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from loguru import logger

from src.core.perception.session import (
    MemoryIDGenerator,
    get_working_history,
)
from src.core.schema.session import SessionContext

__all__ = ["SessionManager"]


class SessionManager:
    def __init__(self, chat_id: Optional[str] = None, expired_time: int = 600, **data):
        self.id_generator: MemoryIDGenerator = MemoryIDGenerator(
            chat_id=chat_id, expired_time=expired_time
        )
        self.working_history: BaseChatMessageHistory = get_working_history(
            chat_id=chat_id, expired_time=expired_time
        )
        self._expired_time = expired_time
        super().__init__(**data)

    class Config:
        arbitrary_types_allowed = True

    @property
    def expired_time(self) -> int:
        return self._expired_time

    def close_session(
            self, messages: List[AnyMessage], config: RunnableConfig | None = None
    ) -> None:
        """
        关闭当前会话，保存会话消息并生成新的会话ID

        Args:
            messages: 需要保存的会话消息列表
            config: 运行时配置，可选参数

        Returns:
            None

        Note:
            - 如果消息列表为空会记录警告日志
            - 会将消息保存到工作内存中
            - 会生成新的会话ID用于下一个会话
        """
        if len(messages) == 0:
            logger.warning("session messages is empty.")
        self.working_history.add_messages(messages)
        self.id_generator.generate_session_id()

    def get_session_and_history(
            self, sc: SessionContext
    ) -> tuple[str, List[BaseMessage]]:
        session_id = self.get_session(sc)
        history = self.get_history()
        logger.debug(f"username：{sc.user_info}, session history: {history}")
        return session_id, history

    def get_session(self, sc: SessionContext) -> str:
        sess_id = f"{sc.user_info.id}_{sc.chat_info.chat_id}_{sc.chat_info.type}_{self.id_generator.get_session_id()}"
        logger.info(f"session id: {sess_id}")
        return sess_id

    def get_history(self) -> List[BaseMessage]:
        return self.working_history.messages
