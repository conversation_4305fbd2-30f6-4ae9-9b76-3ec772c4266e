from functools import cache
from typing import Optional

from langchain_core.chat_history import (
    BaseChatMessageHistory,
    InMemoryChatMessageHistory,
)
from langchain_redis import RedisChatMessageHistory
from loguru import logger

from src.core.perception.session.memory.longterm.task_memory import TaskMemory
from src.infra.app import app
from src.schema import consts


def get_working_history(chat_id: Optional[str] = None, expired_time: int = 600) -> BaseChatMessageHistory:
    if app.current_env == consts.NO_PROD_RUNTIME_ENV: # 非生产环境使用内存存储
        logger.info("Using working memory.")
        return InMemoryChatMessageHistory()
    logger.info("Using redis working memory.")
    assert app.redis_cli is not None, "redis client is not initialized."
    assert chat_id is not None, "chat_id is required."
    return RedisChatMessageHistory(
        redis_client=app.redis_cli,
        session_id=chat_id,
        ttl=expired_time,
        key_prefix=f"{consts.APPNAME}:",
    )


@cache
def get_task_memory() -> TaskMemory:
    return TaskMemory()


