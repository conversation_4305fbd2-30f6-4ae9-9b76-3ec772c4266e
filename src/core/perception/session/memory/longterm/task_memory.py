import json
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar

from langchain.output_parsers import OutputFixingParser
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser
from loguru import logger
from pydantic import BaseModel
from pymilvus import DataType, MilvusClient

from settings import get_or_creat_settings_ins
from src.core.perception.session.memory.longterm.schema import (
    TaskInfo,
    TaskMemoryMergeResult,
    TaskMemoryOptimizedInfo,
    TaskMemorySearchResult,
    TaskMemorySummary,
)
from src.helper.utils import get_chat_model
from src.infra.app import app
from src.infra.clients.embed import embedding_client
from src.schema import consts

T = TypeVar("T", bound=BaseModel)


class TaskMemory:
    def __init__(self):
        self.encoder = _TaskMemoryEncoding()
        self.store = _TaskStore()

    def save(self, task: TaskInfo, prefer_new_task: bool = False):
        self.encoder.save(task, prefer_new_task)

    def save_by_session_id(self, session_id: str, prefer_new_task: bool = False):
        # 获取任务名称和描述
        try:
            task_name, task_description = self.get_task(session_id)
            if task_name is None or task_description is None:
                logger.warning(f"[任务记忆] 任务信息不完整，无法保存: {session_id}")
                return

            history = self.get_history(session_id)
            if history is None:
                logger.warning(f"[任务记忆] 任务对话历史为空，无法保存: {session_id}")
                return

            plan = self.get_plan(session_id)
            if plan is None:
                logger.warning(f"[任务记忆] 任务执行计划为空，无法保存: {session_id}")
                return

            thought = self.get_thought(session_id)
            if thought is None:
                logger.warning(f"[任务记忆] 任务推理过程为空，无法保存: {session_id}")
                return

            self.encoder.save(
                TaskInfo.build(task_name, task_description, history, plan, thought),
                prefer_new_task,
            )
        except Exception as e:
            logger.exception(f"[任务记忆] 保存任务失败: {e}")

    def search(self, task_name: str, limit: int = 1) -> List[TaskMemorySearchResult]:
        """
        搜索任务
        """
        optimized_task_name = self.encoder.extract_keyword(task_name)
        return self.store.search(optimized_task_name, limit=limit)

    def record_task(self, session_id: str, task_name: str, task_description: str):
        cache_key = f"{consts.CACHE_TASK_MEMORY}:task:{session_id}"
        task_content = f"{task_name}\n{task_description}"
        logger.debug(f"[任务记忆] {session_id} 记录任务: {cache_key} {task_content}")
        app.redis_cli.set(cache_key, task_content, ex=3600)

    def get_task(self, session_id: str) -> Tuple[Optional[str], Optional[str]]:
        cache_key = f"{consts.CACHE_TASK_MEMORY}:task:{session_id}"
        task_content = app.redis_cli.get(cache_key)
        if task_content:
            task_name, task_description = task_content.split("\n")
            return task_name, task_description
        return None, None

    def record_plan(self, session_id: str, plan: List[str]):
        cache_key = f"{consts.CACHE_TASK_MEMORY}:plan:{session_id}"
        plan_content = "\n".join(plan)
        logger.debug(f"[任务记忆] {session_id} 记录计划: {cache_key} {plan_content}")
        app.redis_cli.set(cache_key, plan_content, ex=3600)

    def get_plan(self, session_id: str) -> Optional[List[str]]:
        cache_key = f"{consts.CACHE_TASK_MEMORY}:plan:{session_id}"
        plan_content = app.redis_cli.get(cache_key)
        if plan_content:
            return plan_content.split("\n")
        return None

    def record_thought(self, session_id: str, thought: str):
        cache_key = f"{consts.CACHE_TASK_MEMORY}:thought:{session_id}"
        logger.debug(f"[任务记忆] {session_id} 记录推理: {cache_key} {thought}")
        app.redis_cli.set(cache_key, thought, ex=3600)

    def get_thought(self, session_id: str) -> Optional[str]:
        cache_key = f"{consts.CACHE_TASK_MEMORY}:thought:{session_id}"
        thought = app.redis_cli.get(cache_key)
        if thought:
            return thought
        return None

    def record_history(self, session_id: str, history: List[BaseMessage]):
        cache_key = f"{consts.CACHE_TASK_MEMORY}:history:{session_id}"
        history_list = [{"role": m.type, "content": m.content} for m in history]
        history_content = json.dumps(history_list)
        logger.debug(
            f"[任务记忆] {session_id} 记录对话历史: {cache_key} {history_content}"
        )
        app.redis_cli.set(cache_key, history_content, ex=3600)

    def get_history(self, session_id: str) -> Optional[List[BaseMessage]]:
        cache_key = f"{consts.CACHE_TASK_MEMORY}:history:{session_id}"
        history_content = app.redis_cli.get(cache_key)
        if history_content:
            history = []
            history_list = json.loads(history_content)
            for h in history_list:
                history.append(BaseMessage(type=h["role"], content=h["content"]))
            return history
        return None


class _TaskStore:
    field_id = "id"
    field_vector = "vector"
    field_task_name = "task_name"
    field_task_info = "task_info"
    field_summary = "summary"
    field_optimized_info = "optimized_info"

    def __init__(self):
        self._score_threshold = 0.8
        self.vector_store = app.milvus_cli
        self.collection_name = "task_memory_qwen3"
        self.embedding_client = embedding_client
        # 从配置中获取向量维度
        self.settings = get_or_creat_settings_ins()
        self.vector_dim = self.settings.embedding.dimensions
        self._create_collection()

    def _create_collection(self):
        schema = MilvusClient.create_schema(
            auto_id=True,
            enable_dynamic_field=False,
        )

        schema.add_field(
            field_name=self.field_id,
            datatype=DataType.INT64,
            is_primary=True,
            max_length=256,
        )
        schema.add_field(
            field_name=self.field_vector,
            datatype=DataType.FLOAT_VECTOR,
            dim=self.vector_dim,
        )
        schema.add_field(
            field_name=self.field_task_name, datatype=DataType.VARCHAR, max_length=128
        )
        schema.add_field(field_name=self.field_task_info, datatype=DataType.JSON)
        schema.add_field(field_name=self.field_summary, datatype=DataType.JSON)
        schema.add_field(field_name=self.field_optimized_info, datatype=DataType.JSON)

        # add index
        index_params = self.vector_store.prepare_index_params()
        index_params.add_index(
            field_name=self.field_vector,
            index_name="vector_index",
            # IVF_FLAT
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 64},
        )

        self.vector_store.create_collection(
            collection_name=self.collection_name,
            schema=schema,
            index_params=index_params,
            shards_num=1,
            consistency_level="Eventually",
        )

        self.vector_store.load_collection(collection_name=self.collection_name)

    def search(
        self,
        query_task_name: str,
        output_fields: List[str] | None = None,
        limit: int = 1,
    ) -> List[TaskMemorySearchResult]:
        if output_fields is None:
            output_fields = [
                self.field_id,
                self.field_task_name,
                self.field_task_info,
                self.field_summary,
                self.field_optimized_info,
            ]

        vector = [self.embedding_client.embed_query(query_task_name)]
        # BUG: 查询总是失败 pymilvus.exceptions.MilvusException: <MilvusException: (code=100, message=collection not found[database=default][collection=task_memory_qwen3])>
        results: List[List[Dict[str, Any]]] = self.vector_store.search(
            collection_name=self.collection_name,
            data=vector,
            search_params={
                "metric_type": "COSINE",
                "params": {
                    "nprobe": 16,
                    "radius": self._score_threshold,
                    "range_filter": 10,
                },
            },
            limit=limit,
            output_fields=output_fields,
        )
        res = []
        for r in results[0]:
            entity = r["entity"]
            logger.info(f"[任务记忆] 搜索结果: {entity['task_name']}")
            res.append(
                TaskMemorySearchResult(
                    id=entity["id"],
                    distance=r["distance"],
                    task_name=entity["task_name"],
                    task_info=TaskInfo.model_validate(entity["task_info"]),
                    summary=TaskMemorySummary.model_validate(entity["summary"]),
                    optimized_info=TaskMemoryOptimizedInfo.model_validate(
                        entity["optimized_info"]
                    ),
                )
            )

        return res

    def add(
        self,
        vector: List[float],
        task_name: str,
        task_info: TaskInfo,
        summary: TaskMemorySummary,
        optimized_info: TaskMemoryOptimizedInfo,
    ):
        self.vector_store.insert(
            collection_name=self.collection_name,
            data=[
                {
                    "vector": vector,
                    "task_name": task_name,
                    "task_info": task_info.model_dump(),
                    "summary": summary.model_dump(),
                    "optimized_info": optimized_info.model_dump(),
                }
            ],
        )

    def delete(self, task_id: int):
        logger.info(f"[任务记忆] 删除旧的任务记忆: {task_id}")
        self.vector_store.delete(
            collection_name=self.collection_name,
            ids=[task_id],
        )


class _TaskMemoryEncoding:
    _task_info_tmpl = """
* 任务目标：
任务名称: {task_name}
任务描述: {task_description}

* 对话历史：
{history}

* 执行计划：
{plan}

* 推理过程：
{thoughts}

    """
    _summary_prompt = """
===== 角色 =====
* 你是一个专业的任务分析总结助手，请基于以下输入内容（包括对话历史、任务目标、执行步骤和推理过程），输出一份结构化的信息摘要报告，用于项目归档或经验复盘。

===== 输入信息 =====
{task_info}

===== 输出要求 =====
1. 任务名称(task_name)：概括核心任务。（例如，查询pod 信息）
2. 任务描述(task_description)：简要描述任务目标。
3. 知识要点(knowledge):
    * 执行逻辑(logic)：明确任务执行中的因果推导关系进行详细描述。
    * 关键决策(decision): 指出影响任务成败的重要选择节点。
4. 执行计划(plan): 描述计划的详情及调用的工具。

===== 输出格式(样例) =====
```json
{{
    "task_name": "查询pod 信息",
    "task_description": "查询pod 信息",
    "knowledge": {{
        "logic": "根据用户输入的pod名称，查询pod信息",
        "decision": "根据用户输入的pod名称，查询pod信息"
    }},
    "plan": [
        "1. xxxx",
        "2. xxxx"
    ]
}}
```

===== 开始分析 =====
* 需要根据<输入信息>, 遵循<输出要求>的字段要求进行填充，最后严格按着<输出格式>进行结果的输出
    """

    _merge_prompt = """
===== 角色 =====
* 你是一个专业的任务计划专家，以下给出新任务与历史相似的任务，请你分析它们的执行计划和推理过程，整合出一个更优的通用经验。

===== 新任务 =====
{new_case}

===== 历史任务 =====
{old_case}

===== 优化通用计划要求 =====
* 必须优先自动化，减少人工交互
* 失败或不匹配的工具应该剔除出计划
{prefer_new}


===== 输出要求 =====
1. 任务名称(task_name)：概括核心任务。（例如，查询pod 信息）
2. 任务描述(task_description)：简要描述任务目标。
3. 知识要点(knowledge):
    * 执行逻辑(logic)：明确任务执行中的因果推导关系进行详细描述。
    * 关键决策(decision): 详细指出影响任务成败的重要选择节点。
4. 通用执行计划(plan): 描述计划的详情及调用的工具。

===== 输出格式(样例) =====
```json
{{
    "task_name": "查询pod 信息",
    "task_description": "查询pod 信息",
    "knowledge": {{
        "logic": "根据用户输入的pod名称，查询pod信息",
        "decision": "根据用户输入的pod名称，查询pod信息"
    }},
    "plan": [
        "1. xxxx",
        "2. xxxx"
    ]
}}
```

===== 开始分析 =====
* 对比 <新任务> 与 <历史任务> 的执行计划和推理过程, 根据 <优化通用计划要求> 整合出一个更优的通用计划（经验）。
* 最后遵循<输出要求>的字段要求进行填充，严格按着<输出格式>进行结果的输出
        """

    _extract_keyword_prompt = """
==== 角色 ====
* 你是一位专业的任务名称概括专家，以下给出一个任务名称，请你进行核心概括。

==== 任务名称概括要求 ====
1. 概括核心内容，省略任务参数，保留核心诉求

==== 输入信息 ====
任务名称: {task_name}

==== 输出样例 ====
任务名称: 查询pod xxxxxx-xxxx 信息
概括后的结果：查询 pod 信息

===== 输出格式 =====
仅返回概括后的任务名称，若无需进行概括直接返回原文即可。
"""

    def __init__(self):
        self.simple_model = get_chat_model("default")
        self.thought_model = get_chat_model("task_memory")
        self._task_store = _TaskStore()
        self._embedding_client = embedding_client

    def _output_fix(self, output: str, pydantic_object: Type[BaseModel]) -> T:
        return OutputFixingParser.from_llm(
            parser=PydanticOutputParser(pydantic_object=pydantic_object),
            llm=get_chat_model("fix"),
        ).parse(output)

    def _retrieve_task(self, query_task_name: str) -> List[TaskMemorySearchResult]:
        return self._task_store.search(query_task_name)

    def _summary_task(self, task_info: TaskInfo) -> TaskMemorySummary:
        result = self.thought_model.invoke([
            SystemMessage(content=self._summary_prompt.format(task_info=task_info)),
            HumanMessage(content="请开始你的分析"),
        ])
        return self._output_fix(result.content, TaskMemorySummary)

    def _merge_task(
        self, new_task: TaskInfo, old_task: TaskInfo, prefer_new: bool = False
    ) -> TaskMemoryMergeResult:
        new_case = self._task_info_tmpl.format(
            task_name=new_task.task_name,
            task_description=new_task.task_description,
            history=new_task.history,
            plan=new_task.plan,
            thoughts=new_task.thoughts,
        )
        old_case = self._task_info_tmpl.format(
            task_name=old_task.task_name,
            task_description=old_task.task_description,
            history=old_task.history,
            plan=old_task.plan,
            thoughts=old_task.thoughts,
        )
        prefer_new = (
            "* 则重考虑 <新任务> 的计划与决策，其最高优先级" if prefer_new else ""
        )

        result = self.thought_model.invoke([
            SystemMessage(
                content=self._merge_prompt.format(
                    new_case=new_case, old_case=old_case, prefer_new=prefer_new
                )
            ),
            HumanMessage(content="请开始你的分析"),
        ])
        return self._output_fix(result.content, TaskMemoryMergeResult)

    def extract_keyword(self, task_name: str) -> str:
        try:
            logger.info(f"[任务记忆] 提取任务核心要素, 任务名称: <{task_name}>")
            result = self.simple_model.invoke([
                SystemMessage(
                    content=self._extract_keyword_prompt.format(task_name=task_name)
                ),
                HumanMessage(content="请开始你的任务概括"),
            ])
            logger.info(f"[任务记忆] 提取任务核心要素结果: <{result.content}>")
            return result.content
        except Exception as e:
            logger.error(
                f"提取任务核心要素失败, 任务名称: <{task_name}>, 错误信息: {e}"
            )
            return task_name

    def save(self, task: TaskInfo, is_prefer_new: bool = False):
        # 1. 查看是否是否存在类似的任务
        task_name = self.extract_keyword(task.task_name)
        search_results = self._retrieve_task(task_name)
        search_result = search_results[0] if search_results else None
        if search_result:
            logger.info(
                f"[任务记忆] 任务 <{task.task_name}> ({task_name}) 与历史任务 <{search_result.task_name}> 相似，进行合并优化"
            )
            # 2. 如果存在，进行合并优化
            merge_result = self._merge_task(task, search_result.task_info)
            # 3. 删除旧的任务
            self._task_store.delete(search_result.id)
            # 4. 存储新的任务
            logger.info(
                f"[任务记忆] 存储新的任务记忆, 任务名称: {task.task_name}, 通用任务名: {task_name}"
            )
            self._task_store.add(
                vector=self._embedding_client.embed_query(task_name),
                task_name=task_name,
                task_info=task,
                summary=merge_result,
                optimized_info=TaskMemoryOptimizedInfo(
                    new_task=task,
                    old_task=search_result.task_info,
                    prefer_new=is_prefer_new,
                ),
            )

        else:
            logger.info(
                f"[任务记忆] 任务 <{task.task_name}> 与历史任务不相似，进行存储"
            )
            # 1. 生成任务摘要
            logger.info(f"[任务记忆] 生成任务摘要, 任务名称: <{task.task_name}>")
            summary = self._summary_task(task)
            logger.debug(f"[任务记忆] 任务摘要: {summary}")
            # 3. 存储任务
            logger.info(f"[任务记忆] 存储任务, 任务名称: <{task.task_name}>")
            self._task_store.add(
                vector=self._embedding_client.embed_query(task_name),
                task_name=task_name,
                task_info=task,
                summary=summary,
                optimized_info=TaskMemoryOptimizedInfo(
                    new_task=task, old_task=None, prefer_new=is_prefer_new
                ),
            )
