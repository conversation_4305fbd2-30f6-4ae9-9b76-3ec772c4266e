from enum import Enum
from typing import Dict, List, TypedDict, Optional

from langchain_core.messages import BaseMessage
from pydantic import BaseModel, Field


class ShortTermMemoryType(str, Enum):

    task = "task"


class TaskMeta(BaseModel):

    task_name: str = Field(description="任务名称")
    task_description: str = Field(description="任务描述")
    plan: str = Field(description="执行计划")


class TaskInfo(TaskMeta):

    history: str = Field(description="对话历史")
    thoughts: str = Field(description="推理过程")

    @classmethod
    def build(
        cls,
        task_name: str,
        task_description: str,
        history: List[BaseMessage],
        plan: List[str],
        thoughts: str,
    ) -> "TaskInfo":
        history_str = "\n".join([f"{m.type}: {m.content}" for m in history])
        plan_str = "\n".join(plan)
        return cls(
            task_name=task_name,
            task_description=task_description,
            history=history_str,
            plan=plan_str,
            thoughts=thoughts,
        )


class TaskMemoryKnowledge(BaseModel):
    logic: str = Field(description="执行逻辑")
    decision: str = Field(description="关键决策")


class TaskMemorySummary(TaskMeta):
    knowledge: TaskMemoryKnowledge = Field(description="知识要点")


class TaskMemoryMergeResult(TaskMemorySummary):
    pass


class TaskMemoryOptimizedInfo(BaseModel):
    new_task: Optional[TaskInfo] = Field(default=None, description="新任务")
    old_task: Optional[TaskInfo] = Field(default=None, description="历史任务")
    prefer_new: bool = Field(default=False, description="是否更倾向于新任务")


class TaskMemorySearchResult(BaseModel):

    id: int = Field(description="任务ID")
    distance: float = Field(description="距离")
    task_name: str = Field(description="任务名称")
    task_info: TaskInfo = Field(description="任务元数据")
    summary: TaskMemorySummary = Field(description="任务总结")
    optimized_info: TaskMemoryOptimizedInfo = Field(default_factory=TaskMemoryOptimizedInfo, description="优化信息")
