import random
import time
from typing import Optional

from loguru import logger
from pydantic import BaseModel, Field

from src.core.spec.session import <PERSON><PERSON><PERSON><PERSON>atorSpec
from src.infra.app import app
from src.schema import consts

__all__ = ["MemoryIDGenerator"]

random.seed(time.time())


class MemoryIDGenerator(BaseModel, SessionIDGeneratorSpec):
    """
    Generate memory IDs for a given session.
    """

    chat_id: str = Field(description="chat id")
    expired_time: int = Field(description="expired time", default=600)
    session_id: str = Field(default="", description="session id")

    def __init__(self, **data):
        super().__init__(**data)
        self._random_id = self._gen_random_id()
        self._id_counter = 0
        self._init()

    def _gen_random_id(self) -> int:
        return random.randint(1, 1000000)

    def _init(self):
        session_id = self._get_from_cache()

        if session_id is not None:
            try:
                ran_id, id_counter = session_id.split("_")
                self._id_counter = int(id_counter)
                self.session_id = f"{ran_id}_{self._id_counter}"
                return
            except Exception:
                logger.warning(f"get session_id from cache error: {session_id}")

        self.generate_session_id()

    def _make_cache_key(self) -> str:
        return f"{consts.CACHE_PREFIX}:{app.current_env}:session_id:{self.chat_id}"

    def _get_from_cache(self) -> Optional[str]:
        return app.redis_cli.get(f"{self._make_cache_key()}")

    def _set_to_cache(self) -> None:
        app.redis_cli.set(
            f"{self._make_cache_key()}", self.session_id, ex=self.expired_time
        )

    def get_session_id(self) -> str:
        # print(f"session_id: {self.session_id}")
        return str(self.session_id)

    def generate_session_id(self) -> str:
        self._random_id = self._gen_random_id()
        self._id_counter += 1
        self.session_id = f"{self._random_id}_{self._id_counter}"
        self._set_to_cache()
        # print(f"generate session_id: {self.session_id}")
        return self.session_id
