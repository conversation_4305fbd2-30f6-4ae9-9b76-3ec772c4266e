from typing import Literal, Union, List, Dict, Any

import lark_oapi as lark
from loguru import logger

from src.core.perception.console.sender import ConsoleSender
from src.core.perception.lark.sender import LarkMessageSenderV2
from src.core.schema.chatbot import SenderSchema
from src.core.schema.messaage import SenderType
from src.core.schema.session import SessionContext
from src.core.spec.imessage import IMessageSenderSpec
from src.infra.app import app


class IMessageSender(IMessageSenderSpec):
    TERMINAL_TYPE_LARK = "lark"
    TERMINAL_TYPE_CONSOLE = "console"

    def __init__(self, client: lark.Client):
        self.lark_msg_sender: LarkMessageSenderV2 = LarkMessageSenderV2(client)
        self.console_msg_sender: ConsoleSender = ConsoleSender(client)
        self.config = app.config

    def _is_lark_sender(self) -> bool:
        return self.config.app.terminal_type == self.TERMINAL_TYPE_LARK

    def _is_console_sender(self) -> bool:
        return self.config.app.terminal_type == self.TERMINAL_TYPE_CONSOLE

    def send_msg_to_user(self, rsp_type: str, msg: dict, user_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.send_msg_to_user(rsp_type, msg, user_id)
            return
        if self._is_console_sender():
            if rsp_type == SenderType.Auth:
                self.console_msg_sender.send_auth_event(msg)
            else:
                self.console_msg_sender.send_message(msg)
            return
        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_nominator_auth_to_user(
        self, chat_id: str, user_id: str, msg: Dict[str, Any]
    ) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.send_auth_to_user(chat_id, user_id, msg)
            return
        if self._is_console_sender():
            self.console_msg_sender.send_auth_event(msg)
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_msg_to_group(self, rsp_type: str, msg: dict, group_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.send_msg_to_group(rsp_type, msg, group_id)
            return

        if self._is_console_sender():
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_stream_card_to_user(self, user_id: str, session_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.lark_stream_sender.send_immediately(
                user_id, session_id
            )
            return

        if self._is_console_sender():
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_stream_mission_to_user(self, msg: dict, user_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.lark_stream_sender.send_mission_content(msg, user_id)
            return

        if self._is_console_sender():
            self.console_msg_sender.send_mission_content(msg)
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_stream_plan_to_user(
        self, msg: dict, user_id: str, is_replan: bool = False
    ) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.lark_stream_sender.send_plan_content(
                msg, user_id, is_replan
            )
            return

        if self._is_console_sender():
            self.console_msg_sender.send_plan_content(msg)
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_stream_message_to_user(
        self,
        rsp_type: Union[Literal["final", "human", "auth"], str],
        msg: dict,
        user_id: str,
    ) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.lark_stream_sender.send_main_content(
                rsp_type, msg, user_id
            )
            return
        if self._is_console_sender():
            self.console_msg_sender.send_main_content(msg)
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def reply_msg_to_user(self, rsp_type: str, msg: dict, message_id: str) -> None:
        logger.info(f"reply_msg_to_user: {rsp_type}, {msg}, {message_id}")
        if self._is_lark_sender():
            self.lark_msg_sender.reply_msg_to_user(rsp_type, msg, message_id)
            return
        if self._is_console_sender():
            if rsp_type == SenderType.Auth:
                self.console_msg_sender.send_auth_event(msg)
            else:
                self.console_msg_sender.send_message(msg)
            return
        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def disable_event_action_with_stream(
        self, user_id: str, message_id: str, actions: List[str]
    ) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.lark_stream_sender.disable_card(
                user_id, message_id, actions
            )
            return

    def disable_event_action(self, action: str, message_id: str, content: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.disable_event_action(action, message_id)
            return

        if self._is_console_sender():
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def get_session_context(
        self, sender_info: SenderSchema, full_info: bool = False
    ) -> SessionContext:
        if self._is_lark_sender():
            return self.lark_msg_sender.get_session_context(sender_info, full_info)
        if self._is_console_sender():
            return self.console_msg_sender.get_session_context(sender_info, full_info)

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def reply_emoji(self, message_id: str, emoji_type: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.reply_emoji(message_id, emoji_type)
            return

        if self._is_console_sender():
            return

        raise ValueError(f"Invalid terminal type: {self.config.terminal_type}")

    def send_custom_json_card_to_user(self, json_card: dict, user_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.send_custom_json_card_to_user(json_card, user_id)
            return

    def update_custom_json_card_to_user(self, json_card: dict, message_id: str) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.update_custom_json_card_to_user(json_card, message_id)
            return

    def reply_custom_json_card_to_user(
        self, new_json_card: dict, message_id: str
    ) -> None:
        if self._is_lark_sender():
            self.lark_msg_sender.reply_custom_json_card_to_user(
                new_json_card, message_id
            )
            return
