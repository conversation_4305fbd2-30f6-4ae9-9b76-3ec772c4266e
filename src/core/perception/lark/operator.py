import json
from typing import Any, Optional, Literal, Dict, List

import lark_oapi as lark
from lark_oapi.api.cardkit.v1 import (
    CreateCardRequest,
    CreateCardRequestBody,
    CreateCardResponse,
    ContentCardElementRequest,
    ContentCardElementRequestBody,
    ContentCardElementResponse,
    SettingsCardRequest,
    SettingsCardRequestBody,
    SettingsCardResponse,
    BatchUpdateCardRequest,
    BatchUpdateCardRequestBody,
    BatchUpdateCardResponse,
)
from lark_oapi.api.contact.v3 import (
    GetUserRequest,
    GetUserResponse,
    BatchGetIdUserRequest,
    BatchGetIdUserRequestBody,
    BatchGetIdUserResponse,
)
from lark_oapi.api.contact.v3.model import User
from lark_oapi.api.im.v1 import (
    CreateMessageRequest,
    CreateMessageRequestBody,
    CreateMessageResponse,
    PatchMessageRequest,
    PatchMessageRequestBody,
    PatchMessageResponse,
    CreateMessageReactionRequest,
    CreateMessageReactionRequestBody,
    Emoji,
    CreateMessageReactionResponse,
    CreateMessageReactionResponseBody,
    ReplyMessageRequest,
    ReplyMessageRequestBody,
    ReplyMessageResponse,
    ReplyMessageResponseBody,
    CreateMessageResponseBody,
    GetMessageRequest,
    GetMessageResponse,
    GetMessageResponseBody,
    GetChatRequest,
    GetChatResponse,
    GetChatResponseBody,
)
from loguru import logger

from settings import get_or_creat_settings_ins
from src.core.perception.lark.card_tmpl import stream_answer_template
from src.core.schema.chatbot import RobotType
from src.infra import clients

cfg = get_or_creat_settings_ins()

__all__ = ["LarkOperator", "LarkMessageStreamOperator"]


class LarkOperator:

    def __init__(self, client: lark.Client) -> None:
        self.client = client

    def get_user_id(self, user_email: str) -> str:

        # 构造请求对象
        request: BatchGetIdUserRequest = (
            BatchGetIdUserRequest.builder()
            .user_id_type("user_id")
            .request_body(
                BatchGetIdUserRequestBody.builder().emails([user_email]).build()
            )
            .build()
        )

        chatbot_types: List = list(RobotType)
        for chatbot_type in chatbot_types:
            client = clients.get_lark_client(chatbot_type)
            # 发起请求
            response: BatchGetIdUserResponse = client.contact.v3.user.batch_get_id(
                request
            )

            if not response.success():
                logger.error(
                    f"user.batch_get_id failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                )
                continue
            if len(response.data.user_list) != 0:
                return response.data.user_list[0].user_id

        return ""

    def get_user_info(self, user_id: str) -> Optional[User]:
        request: GetUserRequest = (
            GetUserRequest.builder()
            .user_id_type("user_id")
            .user_id(user_id)  # type: ignore
            .build()
        )
        chatbot_types: List = list(RobotType)
        for chatbot_type in chatbot_types:
            client = clients.get_lark_client(chatbot_type)
            response: GetUserResponse = client.contact.v3.user.get(  # type: ignore
                request
            )

            if not response.success():
                logger.error(
                    f"user.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                )
                continue

            assert response.data is not None and response.data.user is not None
            return response.data.user

    def get_message_detail(
        self,
        message_id: str,
        user_id_type: Literal["open_id", "user_id", "union_id"] = "open_id",
    ) -> Optional[GetMessageResponseBody]:
        # 构造请求对象
        request: GetMessageRequest = (
            GetMessageRequest.builder()
            .message_id(message_id)
            .user_id_type(user_id_type)
            .build()
        )

        # 发起请求
        response: GetMessageResponse = self.client.im.v1.message.get(request)
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        return response.data

    def get_chat_detail(self, chat_id: str) -> Optional[GetChatResponseBody]:
        # 构造请求对象
        request: GetChatRequest = (
            GetChatRequest.builder().chat_id(chat_id=chat_id).build()
        )
        response: GetChatResponse = self.client.im.v1.chat.get(request)
        if not response.success():
            lark.logger.error(
                f"client.im.v1.chat.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return
        return response.data

    def send_msg(
        self,
        content: str,
        receive_id: str,
        msg_type: Literal[
            "text", "post", "interactive", "image", "file", "audio", "sticker"
        ] = "text",
        receive_id_type: Literal[
            "open_id", "user_id", "union_id", "chat_id"
        ] = "user_id",
    ) -> Optional[CreateMessageResponse]:
        if not hasattr(self.client, "im") or self.client.im is None:
            raise ValueError("Client does not have 'im' attribute or it is None")

        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type(receive_id_type)
            .request_body(
                CreateMessageRequestBody.builder()
                .content(content=content)
                .msg_type(msg_type=msg_type)
                .receive_id(receive_id)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)

        if response.success:
            return response
        logger.error(f"Failed to send message: {response.msg}")
        return None

    def send_nominator_message(
        self, chat_id: str, user_id: str, card_json: dict
    ) -> Optional[str]:
        body = {
            "chat_id": chat_id,
            "user_id": user_id,
            "msg_type": "interactive",
            "card": card_json,
        }
        # 构造请求对象
        request = (
            lark.BaseRequest.builder()
            .http_method(lark.HttpMethod.POST)
            .uri("/open-apis/ephemeral/v1/send")
            .token_types({lark.AccessTokenType.TENANT})
            .body(body)
            .build()
        )
        response = self.client.request(request)
        if not response.success():
            logger.error(
                f"Request failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            return None
        return json.loads(response.raw.content.decode("utf-8"))["data"]["message_id"]

    def send_card_with_tmpl(
        self,
        receive_id: str,
        template_id: str,
        template_variable: dict,
        receive_id_type: Literal[
            "open_id", "user_id", "union_id", "chat_id"
        ] = "user_id",
    ) -> None:
        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable,
            },
        }
        self.send_msg(
            json.dumps(content),
            receive_id,
            msg_type="interactive",
            receive_id_type=receive_id_type,
        )

    def update_card_with_tmpl(
        self, message_id: str, template_id: str, content: str
    ) -> Optional[PatchMessageResponse]:
        if not hasattr(self.client, "im") or self.client.im is None:
            raise ValueError("Client does not have 'im' attribute or it is None")

        body = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": {"disable": True, "content": content},
            },
        }
        return self.update_message(json.dumps(body), message_id)

    def update_message(self, content: str, message_id: str):
        request: PatchMessageRequest = (
            PatchMessageRequest.builder()
            .request_body(PatchMessageRequestBody.builder().content(content).build())
            .message_id(message_id)
            .build()
        )

        # 发起请求
        response: PatchMessageResponse = self.client.im.v1.message.patch(request)
        if response.success:
            return response
        logger.error(f"Failed to update message: {response.msg}")
        return None

    def reply_card_with_tmpl(
        self, message_id: str, template_id: str, template_variable: dict
    ) -> Optional[ReplyMessageResponseBody]:
        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable,
            },
        }
        return self.reply_message(
            content=json.dumps(content, ensure_ascii=False, indent=4),
            message_id=message_id,
            msg_type="interactive",
        )

    def reply_reaction(
        self, message_id: str, reaction_type: str
    ) -> Optional[CreateMessageReactionResponseBody]:
        request: CreateMessageReactionRequest = (
            CreateMessageReactionRequest.builder()
            .message_id(message_id)
            .request_body(
                CreateMessageReactionRequestBody.builder()
                .reaction_type(Emoji.builder().emoji_type(reaction_type).build())
                .build()
            )
            .build()
        )
        # 发起请求
        response: CreateMessageReactionResponse = (
            self.client.im.v1.message_reaction.create(request)
        )
        if not response.success():
            logger.error(
                f"client.im.v1.message_reaction.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        return response.data

    def reply_simple_message(
        self, message_id: str, content: str
    ) -> Optional[ReplyMessageResponseBody]:
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .message_id(message_id)
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(f'{{"text":"{content}"}}')
                .msg_type("text")
                .build()
            )
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        if not response.success():
            logger.error(
                f"client.im.v1.message_reaction.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return None

        return response.data

    def reply_message(
        self,
        content: str,
        message_id: str,
        msg_type: Literal["text", "post", "image", "interactive"],
        reply_in_thread=False,
    ) -> Optional[ReplyMessageResponseBody]:
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .message_id(message_id)
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type(msg_type)
                .reply_in_thread(reply_in_thread)
                .build()
            )
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message_reaction.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return None

        return response.data


class LarkMessageStreamOperator:

    def __init__(self, client: lark.Client):
        self.client: lark.Client = client

    def create_card(self, card_json: Dict[str, Any] = None) -> Optional[str]:
        if card_json is None:
            card_json = stream_answer_template

        request: CreateCardRequest = (
            CreateCardRequest.builder()
            .request_body(
                CreateCardRequestBody.builder()
                .type("card_json")
                .data(json.dumps(card_json))
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateCardResponse = self.client.cardkit.v1.card.create(request)
        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.cardkit.v1.card_element.patch failed, code: {response.code}, "
                f"msg: {response.msg}, "
                f"log_id: {response.get_log_id()}, "
                f"resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return None

        self._card_id = response.data.card_id
        return response.data.card_id

    def update_card(
        self, card_id: str, element_id: str, content: str, order_num: int
    ) -> ContentCardElementResponse:
        request: ContentCardElementRequest = (
            ContentCardElementRequest.builder()
            .card_id(card_id)
            .element_id(element_id)
            .request_body(
                ContentCardElementRequestBody.builder()
                .content(content)
                .sequence(order_num)
                .build()
            )
            .build()
        )
        # # 发起请求
        response: ContentCardElementResponse = (
            self.client.cardkit.v1.card_element.content(request)
        )
        if not response.success():
            lark.logger.error(
                f"client.cardkit.v1.card_element.content failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
        return response

    def disable_action(
        self, card_id: str, element_id: str, order_num: int, is_disable: bool = True
    ) -> None:

        content = [
            {
                "action": "partial_update_element",
                "params": {
                    "element_id": element_id,
                    "partial_element": {"disabled": is_disable},
                },
            }
        ]
        request: BatchUpdateCardRequest = (
            BatchUpdateCardRequest.builder()
            .card_id(card_id)
            .request_body(
                BatchUpdateCardRequestBody.builder()
                .sequence(order_num)
                .actions(json.dumps(content))
                .build()
            )
            .build()
        )

        # 发起请求
        response: BatchUpdateCardResponse = self.client.cardkit.v1.card.batch_update(
            request
        )

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.cardkit.v1.card.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

    def config_elements(
        self, card_id: str, actions: List[Dict[str, Any]], order_num: int
    ) -> BatchUpdateCardResponse:
        # 构造请求对象
        request: BatchUpdateCardRequest = (
            BatchUpdateCardRequest.builder()
            .card_id(card_id)
            .request_body(
                BatchUpdateCardRequestBody.builder()
                .sequence(order_num)
                .actions(json.dumps(actions))
                .build()
            )
            .build()
        )

        # 发起请求
        response: BatchUpdateCardResponse = self.client.cardkit.v1.card.batch_update(
            request
        )

        # 处理失败返回
        if not response.success():
            lark.logger.warning(
                f"client.cardkit.v1.card.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
        return response

    def setting_config(self, card_id: str, settings: str, order_num: int) -> None:
        request: SettingsCardRequest = (
            SettingsCardRequest.builder()
            .card_id(card_id)
            .request_body(
                SettingsCardRequestBody.builder()
                .settings(settings)
                .sequence(order_num)
                .build()
            )
            .build()
        )

        # 发起请求
        response: SettingsCardResponse = self.client.cardkit.v1.card.settings(request)
        if not response.success():
            lark.logger.error(
                f"client.cardkit.v1.card.settings failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

    def close(self, card_id: str, order_num: int) -> None:
        card_config = {
            "config": {
                "streaming_mode": False,
            }
        }
        self.setting_config(card_id, json.dumps(card_config), order_num)

    def send_msg(
        self,
        content: str,
        receive_id: str,
        msg_type: str = "interactive",
        receive_id_type: Literal[
            "open_id", "user_id", "union_id", "chat_id"
        ] = "user_id",
    ) -> Optional[CreateMessageResponseBody]:
        # https://open.feishu.cn/document/server-docs/im-v1/message/create?appId=cli_a3abf73e30b91013

        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type(receive_id_type)
            .request_body(
                CreateMessageRequestBody.builder()
                .content(content=content)
                .msg_type(msg_type=msg_type)
                .receive_id(receive_id)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)

        if response.success:
            return response.data
        logger.error(f"Failed to send message: {response.msg}")
        return None

    def reply_message(
        self,
        message_id: str,
        content: str,
        msg_type: str = "interactive",
        reply_in_thread=False,
    ) -> ReplyMessageResponseBody:
        """
        https://open.feishu.cn/document/server-docs/im-v1/message/reply
        """
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .message_id(message_id)
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type(msg_type)
                .reply_in_thread(reply_in_thread)
                .build()
            )
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message_reaction.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
        return response.data
