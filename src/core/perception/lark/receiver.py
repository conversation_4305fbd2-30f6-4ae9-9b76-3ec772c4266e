import signal
from typing import Dict, Optional, Union, List

import lark_oapi as lark
import redis
from lark_oapi import LogLevel
from lark_oapi.api.contact.v3.model import User
from lark_oapi.api.im.v1 import MentionEvent
from lark_oapi.api.im.v1.model import P2ImMessageReceiveV1Data
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerData,
    P2CardActionTriggerResponse,
)
from loguru import logger

from settings import Settings
from settings.settings import LarkClient
from src.core.parser.lark import LarkMessageParser
from src.core.perception.lark.operator import LarkOperator
from src.core.perception.lark.utils import get_user_info, get_lark_message_stream_name
from src.core.schema.chatbot import (
    EventSchema,
    InputSchema,
    InputUnsupportedSchema,
    MediaType,
    MessageSchema,
    SenderSchema,
    RobotType,
)
from src.core.spec.handler import MessageHandleSpec, MessageReceiverSpec
from src.infra import clients
from src.infra.app import app
from src.schema import consts


def mention_me(mentions, config: Settings) -> bool:
    if mentions is not None:
        for m in mentions:
            if m.name == config.lark.chat.name:
                return True
    return False


class LarkMessageHandler(MessageHandleSpec):
    STREAM_MAX_LENGTH = 256

    def __init__(
        self, message_stream_name: str, consume_group: str, chatbot_type: RobotType
    ):
        self.user_info_cache: Dict[str, User] = {}
        self.config = app.config
        self.chatbot_type = chatbot_type
        self.message_stream_name = message_stream_name
        self.consume_group = consume_group
        self._lark_operator = LarkOperator(clients.get_lark_client(chatbot_type))

        self._init_message_stream()

    def _init_message_stream(self):
        try:
            logger.info(
                f"create message stream: {self.message_stream_name}, consume group: {self.consume_group}"
            )
            app.redis_cli.xgroup_create(
                self.message_stream_name, self.consume_group, id="0-0", mkstream=True
            )
        except redis.exceptions.ResponseError:
            logger.warning(f"message stream {self.message_stream_name} already exists")

    def _send_message(
        self, input_data: Union[InputSchema, InputUnsupportedSchema]
    ) -> None:
        try:
            logger.info(
                f"send message to stream: {self.message_stream_name}; {input_data.model_dump_json()}"
            )
            # 可以考虑使用带超时的连接池或设置命令超时
            app.redis_cli.xadd(
                self.message_stream_name,
                {"data": input_data.model_dump_json()},
                maxlen=self.STREAM_MAX_LENGTH,
            )
        except redis.RedisError as e:
            logger.error(f"Failed to send message to Redis stream: {e}")
        except Exception as e:
            logger.error(f"Unexpected error when sending message: {e}")

    def _get_user_info(self, user_id: str) -> User:
        if user_id in self.user_info_cache:
            return self.user_info_cache[user_id]

        try:
            user = get_user_info(user_id)
        except Exception as e:
            logger.error(f"get user info error: {e}")
            return User({"name": None})

        self.user_info_cache[user_id] = user
        return user

    def _mention_me(self, mentions: Optional[List[MentionEvent]]) -> bool:
        if mentions is not None:
            for m in mentions:
                chatbot_types: List = list(RobotType)
                for chatbot_type in chatbot_types:
                    if self.chatbot_type == chatbot_type:
                        if m.name == getattr(self.config.lark, chatbot_type.value).name:
                            return True
        return False

    def on_message(self, *args, **kwargs) -> None:
        pass

    def on_event(self, *args, **kwargs) -> None:
        pass

    async def aon_event(self, event: P2CardActionTriggerData) -> None:
        assert event.operator is not None and event.operator.user_id is not None

        assert (
            event.action is not None
            and event.action.value is not None
            and event.context is not None
            and event.context.open_chat_id is not None
            and event.context.open_message_id is not None
        )

        chat_detail = self._lark_operator.get_chat_detail(
            chat_id=event.context.open_chat_id
        )

        # f"{self.sender.user_id}_{self.sender.chat_type}_{self.sender.chat_id}_{self.source}"
        format_input_data = InputSchema(
            sender=SenderSchema(
                chat_id=event.context.open_chat_id,
                chat_type=chat_detail.chat_mode if chat_detail.chat_mode else "",
                user_id=event.operator.user_id,
            ),
            payload=EventSchema(
                action=event.action.value.get("action", ""),
                message_id=event.context.open_message_id,
                session_id=event.action.value.get("session_id", ""),
                content=event.action.value.get("content", ""),
                payload={"option": event.action.option},
            ),
            chatbot_type=self.chatbot_type,
        )
        logger.info(
            f"on event: user id: {event.operator.user_id}, username: {self._get_user_info(event.operator.user_id)}"
        )
        logger.info(f"event input schema: \n{format_input_data.model_dump()}")
        self._send_message(format_input_data)

    async def aon_message(self, event: P2ImMessageReceiveV1Data) -> None:
        assert (
            event.sender is not None
            and event.sender.sender_id is not None
            and event.sender.sender_id.user_id is not None
        )
        assert (
            event.message is not None
            and event.message.chat_id is not None
            and event.message.chat_type is not None
            and event.message.message_id is not None
            and event.message.content is not None
        )
        if event.message.chat_type == "group" and not self._mention_me(
            event.message.mentions
        ):
            return

        content_type, content = LarkMessageParser(
            message_id=event.message.message_id, message=event.message
        ).parse()
        if content_type is None:
            logger.warning(f"Unsupported message type: {event.message.message_type}")
            self._send_message(
                InputUnsupportedSchema(
                    sender=SenderSchema(
                        chat_id=event.message.chat_id,
                        chat_type=event.message.chat_type,
                        user_id=event.sender.sender_id.user_id,
                    ),
                    chatbot_type=self.chatbot_type,
                )
            )
            return

        message_content = [MediaType(content=content, type=content_type)]

        format_input_data = InputSchema(
            sender=SenderSchema(
                chat_id=event.message.chat_id,
                chat_type=event.message.chat_type,
                user_id=event.sender.sender_id.user_id,
            ),
            payload=MessageSchema(
                message_id=event.message.message_id,
                content=message_content,
            ),
            chatbot_type=self.chatbot_type,
        )
        logger.info(
            f"on message: user id: {format_input_data.sender.user_id},"
            f" username: {self._get_user_info(format_input_data.sender.user_id).name}"
        )
        logger.debug(f"message input schema: \n{format_input_data.model_dump()}")
        self._send_message(format_input_data)


class LarkMessageReceiver(MessageReceiverSpec):

    def __init__(self, chatbot_type: RobotType):
        self._lark_client: Optional[lark.ws.client.Client] = None
        self._config = app.config
        self._chatbot_type = chatbot_type
        # 初始化消息处理组件
        self.handler = LarkMessageHandler(
            message_stream_name=get_lark_message_stream_name(chatbot_type),
            consume_group=consts.MSG_CONSUMER_GROUP,
            chatbot_type=chatbot_type,
        )

        self._init_lark_client()

    @property
    def chatbot_type(self) -> RobotType:
        return self._chatbot_type

    def _init_lark_client(self):
        chatbot_types: List = list(RobotType)
        for chatbot_type in chatbot_types:
            if self._chatbot_type == chatbot_type:
                logger.info(f"init {self._chatbot_type} lark client...")
                lark_client_conf: LarkClient = getattr(
                    self._config.lark, chatbot_type.value
                )
                app_id = lark_client_conf.app_id
                app_secret = lark_client_conf.app_secret
                break
        else:
            raise ValueError(f"Invalid chatbot type, {self._chatbot_type}")

        self._lark_client = lark.ws.Client(
            app_id,
            app_secret,
            event_handler=self._build_event_handler(),
            log_level=LogLevel.WARNING,
            auto_reconnect=True,
        )

    def _signal_handler(self, signum, frame):
        """处理接收到的信号"""
        sig_name = signal.Signals(signum).name
        logger.info(
            f"Signal {sig_name} received, stopping {self._chatbot_type} lark client..."
        )
        self.stop()

    def _release_redundant_resources(self):
        """
        清除子进程中不必要的服务

        在多进程环境中，子进程会继承父进程的数据库连接
        这可能导致连接泄漏或资源浪费，因此需要在子进程中关闭这些连接
        """
        try:
            # app.postgres_cli.close()
            # app.thread_pool.shutdown(wait=False)
            # SessionLocal.close_all()
            # app.milvus_cli.close()
            logger.info("Release redundant resource")
        except Exception as e:
            logger.error(f"Failed to release redundant resource: {e}")

    def _build_event_handler(self):
        """构建事件处理器"""

        def do_p2_im_message_receive_v1(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
            lark.ws.client.loop.create_task(self.handler.aon_message(data.event))

        def do_card_action_trigger(
            data: P2CardActionTrigger,
        ) -> P2CardActionTriggerResponse:
            lark.ws.client.loop.create_task(self.handler.aon_event(data.event))
            return P2CardActionTriggerResponse(None)

        return (
            lark.EventDispatcherHandler.builder("", "")
            .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)
            .register_p2_card_action_trigger(do_card_action_trigger)
            .build()
        )

    def start(self, *args, **kwargs) -> None:
        """启动接收器（兼容旧接口）"""
        try:
            logger.info(f"Starting {self._chatbot_type} lark client in process mode...")
            self._lark_client.start()
            logger.info(f"{self._chatbot_type} lark client started")
        except Exception as e:
            raise e

    def stop(self, *args, **kwargs) -> None:
        """停止接收器"""
        logger.info(f"Stopping {self._chatbot_type} lark client...")
        try:
            if lark.ws.client.loop and lark.ws.client.loop.is_running():
                lark.ws.client.loop.call_soon_threadsafe(
                    lambda: lark.ws.client.loop.stop()
                )
                logger.info("Lark client event loop stopped")
        except Exception as e:
            logger.error(f"Error stopping lark client: {e}")

    def run_forever(self) -> None:
        """在当前进程中运行接收器，直到被终止"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        try:
            logger.info(
                f"Starting {self._chatbot_type} lark client with signal handlers..."
            )
            self._release_redundant_resources()
            self.start()

        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received, stopping lark client...")
            self.stop()
        except Exception:
            pass
        finally:
            logger.info(f"{self._chatbot_type} lark client stopped")
