import json
from copy import deepcopy
from typing import Dict, Any, List, Optional, Literal, Union

import lark_oapi as lark
from lark_oapi.api.cardkit.v1 import ContentCardElementResponse
from lark_oapi.api.im.v1 import ReplyMessageResponseBody
from loguru import logger

from settings import get_or_creat_settings_ins
from src.core.perception.lark.card_tmpl import (
    like_button_tmpl,
    stop_button_tmpl,
    button_group_tmpl,
    mission_and_plan_tmpl,
    plan_tmpl,
    replan_tmpl,
)
from src.core.perception.lark.operator import LarkOperator, LarkMessageStreamOperator
from src.core.schema.chatbot import SenderSchema
from src.core.schema.messaage import SenderType, MessageEventAction
from src.core.schema.session import ChatInfo, SessionContext, UserInfo
from src.infra.app import app
from src.infra.utils import ThreadSafeDict
from src.schema import consts

cfg = get_or_creat_settings_ins()

__all__ = ["LarkMessageCard", "LarkMessageSenderV2", "LarkMessageStreamOperator"]


class LarkMessageCard:

    def __init__(self):
        self._card_info = None
        self._card_auth_container = {
            "config": {"update_multi": True, "wide_screen_mode": True},
            "i18n_elements": {
                "zh_cn": [
                    {
                        "tag": "markdown",
                        "content": "",
                        "text_align": "left",
                        "text_size": "normal",
                    },
                    {"tag": "hr"},
                    {
                        "tag": "action",
                        "layout": "bisected",
                        "actions": [
                            {
                                "tag": "button",
                                "text": {"tag": "plain_text", "content": "执行"},
                                "type": "danger",
                                "width": "default",
                                "disabled": False,
                                "behaviors": [
                                    {
                                        "type": "callback",
                                        "value": {
                                            "session_id": "${session_id}",
                                            "action": "yes",
                                            "content": "${content}",
                                        },
                                    }
                                ],
                            },
                            {
                                "tag": "button",
                                "text": {"tag": "plain_text", "content": "拒绝"},
                                "type": "primary",
                                "width": "default",
                                "disabled": False,
                                "behaviors": [
                                    {
                                        "type": "callback",
                                        "value": {
                                            "session_id": "${session_id}",
                                            "action": "no",
                                            "content": "${content}",
                                        },
                                    }
                                ],
                            },
                        ],
                    },
                ]
            },
            "i18n_header": {
                "zh_cn": {
                    "title": {"tag": "plain_text", "content": "确认执行"},
                    "subtitle": {"tag": "plain_text", "content": ""},
                    "template": "blue",
                }
            },
        }
        self._card_message_container = {
            "schema": "2.0",
            "config": {
                "update_multi": True,
                "style": {
                    "text_size": {
                        "normal_v2": {
                            "default": "normal",
                            "pc": "normal",
                            "mobile": "heading",
                        }
                    }
                },
            },
            "body": {
                "direction": "vertical",
                "horizontal_spacing": "8px",
                "vertical_spacing": "8px",
                "horizontal_align": "left",
                "vertical_align": "top",
                "padding": "12px 12px 12px 12px",
                "elements": [],
            },
        }
        self._stop_button = {
            "tag": "button",
            "text": {"tag": "plain_text", "content": "终止沟通"},
            "type": "default",
            "width": "default",
            "size": "medium",
            "hover_tips": {"tag": "plain_text", "content": "停止针对当前话题的沟通"},
            "behaviors": [
                {
                    "type": "callback",
                    "value": {
                        "session_id": "",
                        "action": "stop",
                        "content": "",
                    },
                }
            ],
            "margin": "0px 0px 0px 0px",
            "element_id": "stop",
            "disabled": False,
        }
        self._like_n_report_button = {
            "tag": "column_set",
            "horizontal_spacing": "8px",
            "horizontal_align": "left",
            "columns": [
                {
                    "tag": "column",
                    "width": "weighted",
                    "elements": [
                        {
                            "tag": "column_set",
                            "horizontal_spacing": "8px",
                            "horizontal_align": "left",
                            "columns": [
                                {
                                    "tag": "column",
                                    "width": "auto",
                                    "elements": [
                                        {
                                            "tag": "button",
                                            "text": {
                                                "tag": "plain_text",
                                                "content": "赞",
                                            },
                                            "type": "default",
                                            "width": "default",
                                            "size": "medium",
                                            "icon": {
                                                "tag": "standard_icon",
                                                "token": "thumbsup_outlined",
                                            },
                                            "behaviors": [
                                                {
                                                    "type": "callback",
                                                    "value": {
                                                        "session_id": "${session_id}",
                                                        "action": "like",
                                                        "content": "${content}",
                                                    },
                                                }
                                            ],
                                            "element_id": "like",
                                            "disabled": False,
                                        }
                                    ],
                                    "direction": "horizontal",
                                    "vertical_spacing": "8px",
                                    "horizontal_align": "left",
                                    "vertical_align": "top",
                                }
                            ],
                            "margin": "0px 0px 0px 0px",
                        }
                    ],
                    "vertical_spacing": "8px",
                    "horizontal_align": "left",
                    "vertical_align": "top",
                    "weight": 1,
                },
                {
                    "tag": "column",
                    "width": "weighted",
                    "elements": [
                        {
                            "tag": "select_static",
                            "placeholder": {"tag": "plain_text", "content": "错误原因"},
                            "options": [
                                {
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "回答错误",
                                    },
                                    "value": "回答错误",
                                },
                                {
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "内容过时",
                                    },
                                    "value": "内容过时",
                                },
                                {
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "理解错误",
                                    },
                                    "value": "理解错误",
                                },
                                {
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "参数错误",
                                    },
                                    "value": "参数错误",
                                },
                            ],
                            "type": "default",
                            "width": "default",
                            "behaviors": [
                                {
                                    "type": "callback",
                                    "value": {
                                        "session_id": "${session_id}",
                                        "action": "report",
                                        "content": "${content}",
                                    },
                                }
                            ],
                            "margin": "0px 0px 0px 0px",
                            "element_id": "report",
                            "disabled": False,
                        }
                    ],
                    "vertical_spacing": "8px",
                    "horizontal_align": "left",
                    "vertical_align": "top",
                    "weight": 1,
                },
            ],
            "margin": "0px 0px 0px 0px",
        }
        self._main_content = {
            "tag": "markdown",
            "content": "",
            "text_align": "left",
            "text_size": "normal_v2",
            "margin": "0px 0px 0px 0px",
            "element_id": "content",
        }
        self._chart_content = {
            "tag": "chart",
            "chart_spec": {},
            "preview": True,
            "color_theme": "brand",
            "height": "auto",
            "margin": "0px 0px 0px 0px",
        }
        self._empty_content = {
            "tag": "text",
            "content": "",
            "text_align": "left",
            "text_size": "normal_v2",
            "margin": "0px 0px 0px 0px",
            "element_id": "content",
        }
        self._cache_cli = app.redis_cli

    @property
    def data(self) -> Dict[str, Any]:
        return self._card_info

    @property
    def json(self) -> str:
        return json.dumps(self._card_info)

    def _hr(self) -> Dict[str, Any]:
        return {"tag": "hr", "margin": "0px 0px 0px 0px"}

    def _format_main_content(self, content: str) -> Dict[str, Any]:
        main_content = deepcopy(self._main_content)
        main_content["content"] = content
        return main_content

    def _format_footer_button(
        self, message: Dict[str, Any], resp_type: SenderType = None
    ) -> Dict[str, Any]:
        if resp_type == SenderType.Final:
            button = deepcopy(self._like_n_report_button)
            like_behavior = button["columns"][0]["elements"][0]["columns"][0][
                "elements"
            ][0]["behaviors"][0]["value"]
            like_behavior["session_id"] = message.get("session_id", "")
            like_behavior["content"] = message.get("content", "")

            report_behavior = button["columns"][1]["elements"][0]["behaviors"][0][
                "value"
            ]
            report_behavior["session_id"] = message.get("session_id", "")
            report_behavior["content"] = message.get("content", "")

            return button

        elif resp_type == SenderType.Human:
            button = deepcopy(self._stop_button)
            behavior: Any = button["behaviors"][0]["value"]  # type: ignore
            behavior["session_id"] = message.get("session_id", "")
            behavior["content"] = message.get("content", "")
            return button

        return self._empty_content

    def _format_chart(self, chart_spec: Dict[str, Any]) -> Dict[str, Any]:
        chart_content = deepcopy(self._chart_content)
        chart_content["chart_spec"] = chart_spec
        return chart_content

    def _add_message_element(self, element: Dict[str, Any]) -> "LarkMessageCard":
        if self._card_info is None:
            self._card_info = deepcopy(self._card_message_container)
        self._card_info["body"]["elements"].append(element)
        return self

    def load_card(self, message_id: str) -> "LarkMessageCard":
        item = self._cache_cli.get(f"{consts.CACHE_LARK_MESSAGE_ID}:{message_id}")
        if item is None:
            logger.error(f"message_id: {message_id}'s card not found in cache")
        logger.debug(f"load card from cache: \n{item}")
        self._card_info = json.loads(item)
        return self

    def save_card(
        self, message_id: str, card_info: Dict[str, Any] = None
    ) -> "LarkMessageCard":
        if card_info is not None:
            self._card_info = card_info
        if not message_id:
            logger.error("message_id is empty, can not save to cache")
            return self
        self._cache_cli.set(
            f"{consts.CACHE_LARK_MESSAGE_ID}:{message_id}",
            json.dumps(self._card_info),
            ex=86400,
        )
        return self

    def disable_action(
        self, action: Union[str, MessageEventAction], message_id: str
    ) -> Dict[str, Any]:
        self.load_card(message_id)

        if action == MessageEventAction.Report or action == MessageEventAction.Like:

            cols = self._card_info["body"]["elements"][-1]
            # disable like
            cols["columns"][0]["elements"][0]["columns"][0]["elements"][0][
                "disabled"
            ] = True
            # disable report
            cols["columns"][1]["elements"][0]["disabled"] = True

        elif action == MessageEventAction.No or action == MessageEventAction.Yes:
            actions = self._card_info["i18n_elements"]["zh_cn"][2]["actions"]
            for action in actions:
                action["disabled"] = True

        elif action == MessageEventAction.Stop:
            button = self._card_info["body"]["elements"][-1]
            button["disabled"] = True

        self.save_card(message_id)
        return self._card_info

    @classmethod
    def load_card_from_cache(cls, message_id: str) -> Optional["LarkMessageCard"]:
        card = cls()
        if not card._cache_cli.get(f"{consts.CACHE_LARK_MESSAGE_ID}:{message_id}"):
            return None
        card.load_card(message_id)
        return card

    @classmethod
    def build_auth_card(cls, message: Dict[str, Any]) -> "LarkMessageCard":
        """
        :params:
            message: {
                session_id: str,
                content: str,
            }
        """
        self = cls()
        content = message.get("content", "")
        self._card_info = deepcopy(self._card_auth_container)
        # content
        self._card_info["i18n_elements"]["zh_cn"][0]["content"] = content
        # behaviors
        actions = self._card_info["i18n_elements"]["zh_cn"][2]["actions"]
        for action in actions:
            action["behaviors"][0]["value"]["session_id"] = message.get(
                "session_id", ""
            )
            action["behaviors"][0]["value"]["content"] = message.get("content", "")
        return self

    @classmethod
    def build_message_card(
        cls, resp_type: SenderType, message: Dict[str, Any]
    ) -> "LarkMessageCard":
        """
        :params:
            message: {
                session_id: str,
                content: str,

            }
        """
        self = cls()
        if "images" in message:
            for image_data in message["images"]:
                self._add_message_element(
                    self._format_chart(image_data)
                )._add_message_element(self._hr())
        (
            self._add_message_element(
                self._format_main_content(message.get("content", ""))
            )
            ._add_message_element(self._hr())
            ._add_message_element(self._format_footer_button(message, resp_type))
        )
        return self

    @classmethod
    def build_custom_card(cls, card_data: Dict[str, Any]) -> "LarkMessageCard":
        self = cls()
        self._card_info = card_data
        return self


class LarkMessageSenderV2:

    def __init__(self, client: lark.Client) -> None:
        self.mo = LarkOperator(client)
        self.lark_stream_sender: LarkMessageSingleSingleStreamSender = (
            LarkMessageSingleSingleStreamSender(client)
        )

    def send_msg_to_user(
        self, rsp_type: Union[str, SenderType], msg: dict, user_id: str
    ):
        """
        :params:
        msg: {
            session_id: str
            content: str
        }
        """
        if rsp_type == SenderType.Final or rsp_type == SenderType.Human:
            message_card = LarkMessageCard.build_message_card(rsp_type, msg)
            resp = self.mo.send_msg(message_card.json, user_id, "interactive")
            if resp:
                message_card.save_card(resp.data.message_id)

        elif rsp_type == SenderType.Auth:
            message_card = LarkMessageCard.build_auth_card(msg)
            resp = self.mo.send_msg(message_card.json, user_id, "interactive")
            if resp:
                message_card.save_card(resp.data.message_id)
        else:
            self.mo.send_card_with_tmpl(user_id, app.config.template.system_tmp_id, msg)

    def send_auth_to_user(self, chat_id: str, user_id: str, message: Dict[str, Any]):
        message_card = LarkMessageCard.build_auth_card(message)
        message_id = self.mo.send_nominator_message(chat_id, user_id, message_card.data)
        if message_id:
            message_card.save_card(message_id)

    def send_msg_to_group(self, rsp_type: str, msg: dict, group_id: str) -> None:
        pass

    def reply_msg_to_user(
        self, rsp_type: Union[str, SenderType], msg: dict, message_id: str
    ):
        if rsp_type == SenderType.Final or rsp_type == SenderType.Human:
            message_card = LarkMessageCard.build_message_card(rsp_type, msg)
            resp = self.mo.reply_message(message_card.json, message_id, "interactive")
            if resp:
                message_card.save_card(resp.message_id)
        elif rsp_type == SenderType.Auth:
            message_card = LarkMessageCard.build_auth_card(msg)
            resp = self.mo.reply_message(message_card.json, message_id, "interactive")
            if resp:
                message_card.save_card(resp.message_id)
        else:
            self.mo.reply_card_with_tmpl(
                message_id, app.config.template.system_tmp_id, msg
            )

    def disable_event_action(
        self, action: Union[str, MessageEventAction], message_id: str
    ) -> None:
        message_card = LarkMessageCard.load_card_from_cache(message_id=message_id)
        message_card.disable_action(action, message_id)
        self.mo.update_message(message_card.json, message_id)

    def send_custom_json_card_to_user(
        self, json_card: dict, user_id: str
    ) -> Optional[ReplyMessageResponseBody]:
        message_card = LarkMessageCard.build_custom_card(json_card)
        resp = self.mo.send_msg(message_card.json, user_id, "interactive")
        if resp:
            message_card.save_card(resp.data.message_id)
        return resp

    def reply_custom_json_card_to_user(
        self, new_json_card: dict, message_id: str
    ) -> Optional[ReplyMessageResponseBody]:
        message_card = LarkMessageCard.build_custom_card(new_json_card)
        resp = self.mo.reply_message(message_card.json, message_id, "interactive")
        if resp:
            message_card.save_card(resp.message_id)
        return resp

    def update_custom_json_card_to_user(self, json_card: dict, message_id: str):
        self.mo.update_message(json.dumps(json_card), message_id)

    def get_session_context(
        self, sender_info: SenderSchema, full_info: bool = False
    ) -> SessionContext:
        # TODO full_info, 待规划
        user = self.mo.get_user_info(sender_info.user_id)
        if app.current_env == consts.NO_PROD_RUNTIME_ENV:
            import os
            from src.schema import env

            if not user.email:
                logger.warning(
                    f"user email is empty, use test email: {env.TEST_USER_EMAIL}"
                )
                test_email = os.getenv(env.TEST_USER_EMAIL, "")
                user.email = test_email
            if not user.name:
                logger.warning(
                    f"user name is empty, use test name: {env.TEST_USER_NAME}"
                )
                test_name = os.getenv(env.TEST_USER_NAME, "")
                user.name = test_name

        return SessionContext(
            user_info=UserInfo(email=user.email, id=user.user_id, name=user.name),  # type: ignore
            chat_info=ChatInfo(type=sender_info.chat_type, chat_id=sender_info.chat_id),
        )

    def reply_emoji(self, message_id: str, emoji_type: str) -> None:
        self.mo.reply_reaction(message_id, emoji_type)


class LarkMessageSingleSingleStreamSender(LarkMessageStreamOperator):
    ORDER_NUM_KEY = "order_num"
    SESSION_ID_KEY = "session_id"
    CARD_ID_KEY = "card_id"

    STATUS_THINKING = "thinking"
    STATUS_COMPLETED = "completed"

    def __init__(self, client: lark.Client):
        super().__init__(client)
        self._cache = ThreadSafeDict()
        self._init_order_num = 10000
        self._redis_cli = app.redis_cli

    def _save_card_info(
        self, user_id: str, card_id: str, session_id: str
    ) -> Dict[str, Any]:
        content_dict = {self.SESSION_ID_KEY: session_id, self.CARD_ID_KEY: card_id}
        self._cache[user_id] = content_dict
        self._cache[f"{user_id}_{self.ORDER_NUM_KEY}"] = self._init_order_num
        return content_dict

    def _get_card_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        content_dict = self._cache.get(user_id, None)
        return content_dict

    def _delete_card_info(self, user_id: str) -> None:
        del self._cache[user_id]

    def _get_order_num(self, user_id: str) -> int:
        order_key = f"{user_id}_{self.ORDER_NUM_KEY}"
        order_num = self._cache.get_or_set(order_key, self._init_order_num)
        self._cache[order_key] = order_num + 1
        return order_num

    def _disable_actions(
        self, user_id: str, message_id: str, actions: List[str]
    ) -> None:
        for action in actions:
            card_id = self._redis_cli.get(f"{consts.CACHE_LARK_CARD_ID}:{message_id}")
            logger.info(f"disable action: {action}, card_id: {card_id}")
            if not card_id:
                logger.warning(f"card_id not found for message_id: {message_id}")
                continue
            self.disable_action(card_id, action, self._get_order_num(user_id))

    def _set_callback_behavior(
        self, button_json: Dict[str, Any], msg: Dict[str, Any]
    ) -> Dict[str, Any]:
        callback_value: Dict[str, Any] = button_json["behaviors"][0]["value"]
        callback_value["session_id"] = msg.get("session_id", "")
        callback_value["content"] = msg.get("content", "")
        button_json["behaviors"][0]["value"] = callback_value
        return button_json

    def _switch_button_group(self, user_id: str, rsp_type: str, msg: Dict[str, Any]):
        card_id = self._get_card_info(user_id)[self.CARD_ID_KEY]
        button_elements = []
        if rsp_type == SenderType.Final:
            button_elements = [
                self._set_callback_behavior(like_button_tmpl.copy(), msg)
            ]
        elif rsp_type == SenderType.Human:
            button_elements = [
                self._set_callback_behavior(stop_button_tmpl.copy(), msg)
            ]
        # elif rsp_type == self.RESP_AUTH:
        #     button_elements = [
        #         self._set_callback_behavior(confirm_button_tmpl.copy(), msg),
        #         self._set_callback_behavior(reject_button_tmpl.copy(), msg)
        #     ]
        button_group_elements = button_group_tmpl.copy()
        button_group_elements["elements"] = button_elements
        # delete button_group
        actions = [
            {"action": "delete_elements", "params": {"element_ids": ["button_group"]}}
        ]
        logger.info(f"card_id: {card_id}, actions: {actions}")
        self.config_elements(card_id, actions, self._get_order_num(user_id))
        # insert button group before void
        actions = [
            {
                "action": "add_elements",
                "params": {
                    "type": "insert_before",
                    "target_element_id": "void",
                    "elements": [button_group_elements],
                },
            }
        ]
        logger.info(f"card_id: {card_id}, actions: {actions}")
        self.config_elements(card_id, actions, self._get_order_num(user_id))

        # turn on report element
        self.disable_action(
            card_id, "report", self._get_order_num(user_id), is_disable=False
        )

        # update report element's callback behaviors
        actions = [
            {
                "action": "partial_update_element",
                "params": {
                    "element_id": "report",
                    "partial_element": {
                        "behaviors": [
                            {
                                "type": "callback",
                                "value": {
                                    "session_id": msg["session_id"],
                                    "action": "report",
                                    "content": msg["content"],
                                },
                            }
                        ]
                    },
                },
            }
        ]
        logger.info(f"card_id: {card_id}, actions: {actions}")
        self.config_elements(card_id, actions, self._get_order_num(user_id))

    def _must_send_card_first(self, user_id: str, session_id: str = None) -> None:
        if self._get_card_info(user_id) is not None:
            return
        card_id = self.create_card()
        if card_id is None:
            logger.error("create stream card failed")
            return
        # 发送卡片
        content = {
            "type": "card",
            "data": {
                "card_id": card_id,
            },
        }
        self._save_card_info(user_id, card_id, session_id)
        resp = self.send_msg(json.dumps(content), user_id)
        # 缓存卡片id, 给 event action 使用
        logger.info(
            f"save card info: user: {user_id}, message_id: {resp.message_id}, card_id: {card_id}"
        )
        self._redis_cli.set(
            f"{consts.CACHE_LARK_CARD_ID}:{resp.message_id}", card_id, ex=3600
        )

    def send_immediately(self, user_id: str, session_id: str) -> None:
        self._must_send_card_first(user_id, session_id)

    def send_main_content(
        self,
        rsp_type: Union[Literal["final", "human", "auth"], str],
        msg: dict,
        user_id: str,
    ) -> None:
        """
        params:
        rsp_type: final, interaction, human, auth
        msg_dict: {"content": "", "session_id": ""}
        """

        self._must_send_card_first(user_id, msg["session_id"])
        self.send_status_content(user_id, msg["content"], status=self.STATUS_COMPLETED)

        card_json = json.loads(self._get_card_info(user_id)[self.CARD_ID_KEY])
        self.update_card(
            self._get_card_info(user_id)[self.CARD_ID_KEY],
            "content",
            msg["content"],
            self._get_order_num(user_id),
        )
        self._switch_button_group(user_id, rsp_type, msg)
        self.setting_config(
            card_id=self._get_card_info(user_id)[self.CARD_ID_KEY],
            settings=json.dumps({"config": {"summary": {"content": msg["content"]}}}),
            order_num=self._get_order_num(user_id),
        )
        self.close_card(user_id)

    def send_mission_content(self, msg: dict, user_id: str) -> None:
        """
        params:
            msg_dict: {"content": [{"id": 1, "name": "xxx", "description": "xx"}], "session_id": ""}

        """
        self._must_send_card_first(user_id, msg["session_id"])

        card_id = self._get_card_info(user_id)[self.CARD_ID_KEY]

        actions = [
            {
                "action": "add_elements",
                "params": {
                    "type": "insert_after",
                    "target_element_id": "status",
                    "elements": [mission_and_plan_tmpl],
                },
            }
        ]
        logger.info(f"card_id: {card_id}, actions: {actions}")
        self.config_elements(card_id, actions, self._get_order_num(user_id))

        mission_text = ""
        for mission in msg["content"]:
            mission_text += f"* {mission.get('description', 'none')}\n"

        self.update_card(card_id, "mission", mission_text, self._get_order_num(user_id))

    def send_plan_content(
        self, msg: dict, user_id: str, is_replan: bool = False
    ) -> None:
        """
        params:
            msg_dict: {"content": [{"id": 1, "plan": "xxx", "action": "xx"}], "session_id": ""}
        """
        if is_replan:
            actions = [
                {
                    "action": "add_elements",
                    "params": {
                        "type": "insert_after",
                        "target_element_id": "status",
                        "elements": [replan_tmpl],
                    },
                }
            ]
        else:
            actions = [
                {
                    "action": "add_elements",
                    "params": {
                        "type": "insert_after",
                        "target_element_id": "mission",
                        "elements": [plan_tmpl],
                    },
                }
            ]

        # 重新获取  card_id
        self._must_send_card_first(user_id, msg["session_id"])
        card_id = self._get_card_info(user_id)[self.CARD_ID_KEY]
        logger.info(f"card_id: {card_id}, actions: {actions}")
        _ = self.config_elements(card_id, actions, self._get_order_num(user_id))

        plan_text = ""
        for plan in msg["content"]:
            plan_text += f"* {plan.get('action', 'none')}\n"

        self.update_card(
            self._get_card_info(user_id)[self.CARD_ID_KEY],
            "plan",
            plan_text,
            self._get_order_num(user_id),
        )

    def send_status_content(
        self, user_id: str, session_id: str, status: str = "completed"
    ) -> ContentCardElementResponse:
        self._must_send_card_first(user_id, session_id)
        return self.update_card(
            self._get_card_info(user_id)[self.CARD_ID_KEY],
            "status",
            status,
            self._get_order_num(user_id),
        )

    def disable_card(self, user_id: str, message_id: str, actions: List[str]) -> None:
        try:
            self._disable_actions(user_id, message_id, actions=actions)
        except Exception as e:
            logger.warning(
                f"disable card failed, message_id: {message_id}, action: {actions}, error: {e}"
            )
            logger.exception(e)

    def close_card(self, user_id) -> None:
        card_info = self._get_card_info(user_id)
        if card_info is None:
            return
        logger.info(
            f"close card, message_id: {user_id}, card_id: {card_info[self.CARD_ID_KEY]}"
        )
        self.close(card_info[self.CARD_ID_KEY], self._get_order_num(user_id))
        self._delete_card_info(user_id)
