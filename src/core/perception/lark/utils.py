from functools import cache
from typing import List

import lark_oapi as lark
from lark_oapi.api.contact.v3 import GetUserRequest, GetUserResponse
from lark_oapi.api.contact.v3.model import User
from loguru import logger

from settings.settings import LarkClient
from src.core.schema.chatbot import RobotType
from src.infra.app import app
from src.infra.clients.client_lark import get_lark_client
from src.schema import consts


@cache
def get_user_info(user_id: str) -> User:
    client = get_lark_client()
    request: GetUserRequest = GetUserRequest.builder().user_id(user_id).user_id_type("user_id").build()
    response: GetUserResponse = client.contact.v3.user.get(request)

    if not response.success():
        logger.error(
            f"user.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )
        raise Exception

    assert response.data is not None and response.data.user is not None
    return response.data.user


def get_lark_message_stream_name(chatbot_type: RobotType) -> str:
    chatbot_types: List = list(RobotType)
    for ct in chatbot_types:
        chatbot_config: LarkClient = getattr(app.config.lark, ct.value)
        if chatbot_type == chatbot_config.type:
            app_id = chatbot_config.app_id
            break
    else:
        raise Exception(f"chatbot type {chatbot_type} not supported")

    return f"{consts.LARK_MSG_PRODUCE_STREAM_PREFIX}-{chatbot_type.value}-{app_id}"

