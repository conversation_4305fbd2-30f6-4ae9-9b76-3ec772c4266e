stream_answer_template = {
    "schema": "2.0",
    "config": {
        "update_multi": True,
        "streaming_mode": True,
        "summary": {
            "content": "",
        },
        "streaming_config": {
            "print_step": {
                "default": 1
            },
            "print_frequency_ms": {
                "default": 30
            },
            "print_strategy": "fast"
        },
        "style": {
            "text_size": {
                "normal_v2": {
                    "default": "normal",
                    "pc": "normal",
                    "mobile": "heading"
                }
            }
        }
    },
    "body": {
        "direction": "vertical",
        "horizontal_spacing": "8px",
        "vertical_spacing": "8px",
        "horizontal_align": "left",
        "vertical_align": "top",
        "padding": "12px 12px 12px 12px",
        "elements": [
            {
                "tag": "markdown",
                "content": "thinking...",
                "text_align": "left",
                "text_size": "notation",
                "margin": "0px 0px 0px 0px",
                "icon": {
                    "tag": "standard_icon",
                    "token": "robot_outlined",
                    "color": "blue"
                },
                "element_id": "status"
            },
            {
                "tag": "markdown",
                "content": "",
                "text_align": "left",
                "text_size": "normal_v2",
                "margin": "0px 0px 0px 4px",
                "element_id": "content"
            },
            {
                "tag": "hr",
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "column_set",
                "horizontal_spacing": "8px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "auto",
                        "elements": [

                        ],
                        "direction": "horizontal",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "element_id": "button_group"
                    },
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [],
                        "direction": "vertical",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1,
                        "element_id": "void"
                    },
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "select_static",
                                "placeholder": {
                                    "tag": "plain_text",
                                    "content": "错误原因"
                                },
                                "options": [
                                    {
                                        "text": {
                                            "tag": "plain_text",
                                            "content": "回答错误"
                                        },
                                        "value": "回答错误"
                                    },
                                    {
                                        "text": {
                                            "tag": "plain_text",
                                            "content": "内容过时"
                                        },
                                        "value": "内容过时"
                                    },
                                    {
                                        "text": {
                                            "tag": "plain_text",
                                            "content": "理解错误"
                                        },
                                        "value": "理解错误"
                                    },
                                    {
                                        "text": {
                                            "tag": "plain_text",
                                            "content": "参数错误"
                                        },
                                        "value": "参数错误"
                                    }
                                ],
                                "type": "default",
                                "width": "default",
                                "behaviors": [
                                    {
                                        "type": "callback",
                                        "value": {
                                            "session_id": "${session_id}",
                                            "action": "report",
                                            "content": "${content}",
                                        }
                                    }
                                ],
                                "margin": "0px 0px 0px 0px",
                                "element_id": "report",
                                "disabled": True
                            }
                        ],
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 0px 0px 0px"
            }
        ]
    }
}

mission_and_plan_tmpl = {
    "tag": "column_set",
    "horizontal_spacing": "8px",
    "horizontal_align": "left",
    "columns": [
        {
            "tag": "column",
            "width": "weighted",
            "background_style": "grey-100",
            "elements": [
                {
                    "tag": "markdown",
                    "content": "",
                    "text_align": "left",
                    "text_size": "notation",
                    "margin": "4px 4px 2px 8px",
                    "icon": {
                        "tag": "standard_icon",
                        "token": "multitask-5_outlined",
                        "color": "green"
                    },
                    "element_id": "mission"
                }
            ],
            "padding": "0px 0px 0px 0px",
            "direction": "vertical",
            "horizontal_spacing": "8px",
            "vertical_spacing": "8px",
            "horizontal_align": "left",
            "vertical_align": "top",
            "margin": "0px 0px 0px 0px",
            "weight": 1,
        }
    ],
    "margin": "0px 0px 0px 0px",
    "element_id": "mission_and_plan"
}

plan_tmpl = {
    "tag": "markdown",
    "content": "",
    "text_align": "left",
    "text_size": "notation",
    "margin": "2px 4px 4px 8px",
    "icon": {
        "tag": "standard_icon",
        "token": "meeting-plan_outlined",
        "color": "orange"
    },
    "element_id": "plan"
}

replan_tmpl = {
    "tag": "column_set",
    "horizontal_spacing": "8px",
    "horizontal_align": "left",
    "columns": [
        {
            "tag": "column",
            "width": "weighted",
            "background_style": "grey-100",
            "elements": [
                {
                    "tag": "markdown",
                    "content": "",
                    "text_align": "left",
                    "text_size": "notation",
                    "margin": "2px 4px 4px 8px",
                    "icon": {
                        "tag": "standard_icon",
                        "token": "meeting-plan_outlined",
                        "color": "orange"
                    },
                    "element_id": "plan"
                }
            ],
            "padding": "0px 0px 0px 0px",
            "direction": "vertical",
            "horizontal_spacing": "8px",
            "vertical_spacing": "8px",
            "horizontal_align": "left",
            "vertical_align": "top",
            "margin": "0px 0px 0px 0px",
            "weight": 1,
        }
    ],
    "margin": "0px 0px 0px 0px",
    "element_id": "mission_and_plan"
}

button_group_tmpl = {
    "tag": "column",
    "width": "auto",
    "elements": [

    ],
    "direction": "horizontal",
    "horizontal_spacing": "8px",
    "vertical_spacing": "8px",
    "horizontal_align": "left",
    "vertical_align": "top",
    "element_id": "button_group"
}

like_button_tmpl = {
    "tag": "button",
    "text": {
        "tag": "plain_text",
        "content": "赞"
    },
    "type": "default",
    "width": "default",
    "size": "medium",
    "icon": {
        "tag": "standard_icon",
        "token": "thumbsup_outlined"
    },
    "behaviors": [
        {
            "type": "callback",
            "value": {
                "session_id": "${session_id}",
                "action": "like",
                "content": "${content}",
                "card_id": "none",
            }
        }
    ],
    "element_id": "like",
    "disabled": False
}

stop_button_tmpl = {
    "tag": "button",
    "text": {
        "tag": "plain_text",
        "content": "终止"
    },
    "type": "default",
    "width": "default",
    "size": "medium",
    "icon": {
        "tag": "standard_icon",
        "token": "stop_outlined"
    },
    "behaviors": [
        {
            "type": "callback",
            "value": {
                "session_id": "${session_id}",
                "action": "stop",
                "content": "${content}",
                "card_id": "none",
            }
        }
    ],
    "element_id": "stop",
    "disabled": False

}

confirm_button_tmpl = {
    "tag": "button",
    "text": {
        "tag": "plain_text",
        "content": "确认"
    },
    "type": "danger",
    "width": "default",
    "size": "medium",
    "behaviors": [
        {
            "type": "callback",
            "value": {
                "session_id": "${session_id}",
                "action": "yes",
                "content": "${content}",
                "card_id": "none",
            }
        }
    ],
    "element_id": "yes",
    "disabled": False
}

reject_button_tmpl = {
    "tag": "button",
    "text": {
        "tag": "plain_text",
        "content": "拒绝"
    },
    "type": "primary",
    "width": "default",
    "size": "medium",
    "behaviors": [
        {
            "type": "callback",
            "value": {
                "session_id": "${session_id}",
                "action": "no",
                "content": "${content}",
                "card_id": "none",
            }
        }
    ],
    "element_id": "no",
    "disabled": False
}
