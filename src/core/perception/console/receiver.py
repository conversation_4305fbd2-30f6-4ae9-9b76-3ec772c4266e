import json
import threading
from functools import cache
from typing import List

import redis
from loguru import logger

from src.core.perception.console.schema import ConsoleMessage
from src.core.perception.lark.operator import LarkOperator
from src.core.schema.chatbot import (
    InputSchema,
    SenderSchema,
    MessageSchema,
    EventSchema,
    MediaType,
    RobotType,
)
from src.core.schema.messaage import ChatType
from src.core.spec.handler import MessageReceiverSpec, MessageHandleSpec
from src.infra import clients
from src.infra.app import app
from src.schema import consts

NOT_GIVEN = "not_given"


class ConsoleReceiver(MessageReceiverSpec, MessageHandleSpec):
    ON_MESSAGE_CHANNEL_PREFIX = consts.SERVER_CHANNEL_PREFIX
    MESSAGE_Q_CONSUMER_GROUP = consts.MSG_CONSUMER_GROUP
    STREAM_MAX_LENGTH = 128

    CHAT_ID = "local"

    def __init__(self):
        self.email = f"{app.config.console.login_name}@52tt.com"
        self._on_message_channel = f"{self.ON_MESSAGE_CHANNEL_PREFIX}:{self.email}"
        self._message_q_stream_name = get_console_message_stream_name()
        self._stop_event = threading.Event()
        self._lark_operator = LarkOperator(clients.get_lark_client())

    @property
    def _user_id(self) -> str:
        return self._lark_operator.get_user_id(self.email)

    @property
    def _chatbot_type(self) -> RobotType:
        chatbot_types: List = list(RobotType)
        for ct in chatbot_types:
            if app.config.console.chatbot_type == ct.value:
                return ct
        raise Exception("Invalid chatbot type")

    def _init_message_q(self):
        try:
            logger.info(f"init message q: {self._message_q_stream_name}")
            app.redis_cli.xgroup_create(
                self._message_q_stream_name,
                self.MESSAGE_Q_CONSUMER_GROUP,
                id="0-0",
                mkstream=True,
            )
        except redis.exceptions.ResponseError:
            logger.warning(
                f"message stream {self._message_q_stream_name} already exists"
            )

    def _send_message(self, input_data: InputSchema) -> None:
        logger.info(
            f"send message to stream: {self._message_q_stream_name}; {input_data.model_dump_json()}"
        )
        app.redis_cli.xadd(
            self._message_q_stream_name,
            {"data": input_data.model_dump_json()},
            maxlen=self.STREAM_MAX_LENGTH,
        )

    def _listening(self):
        pubsub = app.redis_cli.pubsub()
        pubsub.subscribe(self._on_message_channel)

        while not self._stop_event.is_set():  # 检查是否需要停止
            try:
                message = pubsub.get_message(timeout=3)  # 设置超时时间为 3 秒
                if message:
                    logger.debug(
                        f"console server receive message from {self._on_message_channel} - {message}"
                    )
                    if message["type"] != "message":
                        continue
                    message_data = ConsoleMessage(**json.loads(message["data"]))
                    if message_data.message_type == "message":
                        logger.info(
                            f"console server receive message: {message_data.content}"
                        )
                        self.on_message(message_data)
                    elif message_data.message_type == "event":
                        logger.info(
                            f"console server receive event: {message_data.content}"
                        )
                        self.on_event(message_data)
                    else:
                        logger.warning(
                            f"console server receive unknown message: {message_data}"
                        )

            except Exception as e:
                logger.warning(f"Error processing message: {e}")

    async def aon_event(self, message: ConsoleMessage) -> None:
        pass

    async def aon_message(self, message: ConsoleMessage) -> None:
        pass

    def on_event(self, message: ConsoleMessage) -> None:
        format_input_data = InputSchema(
            sender=SenderSchema(
                chat_id=self.CHAT_ID,
                chat_type=ChatType.P2P,
                user_id=self._user_id,
            ),
            payload=EventSchema(
                action=message.event.get("action", NOT_GIVEN),
                message_id=message.message_id,
                session_id=message.event.get("session_id", NOT_GIVEN),
                content="none",
                payload={},
            ),
            source="console",
            chatbot_type=self._chatbot_type,
        )
        self._send_message(format_input_data)

    def on_message(self, message: ConsoleMessage):
        message_content = [
            MediaType(content=message.content, type=message.content_type)
        ]
        format_input_data = InputSchema(
            sender=SenderSchema(
                chat_id=self.CHAT_ID,
                chat_type=ChatType.P2P,
                user_id=self._user_id,
            ),
            payload=MessageSchema(
                message_id=message.message_id,
                content=message_content,
            ),
            source="console",
            chatbot_type=self._chatbot_type,
        )
        self._send_message(format_input_data)

    def start(self, *args, **kwargs) -> None:
        logger.info("Console receiver start")
        if app.config.console.login_name == "none":
            raise Exception("console login name is not set")
        self._init_message_q()
        self._listening()

    def stop(self, *args, **kwargs) -> None:
        logger.info("Console receiver stop")
        self._stop_event.set()

    def run_forever(self, *args, **kwargs) -> None:
        pass


def get_console_message_stream_name() -> str:
    return f"{consts.SERVER_CHANNEL_PREFIX}-{app.config.console.chatbot_type}-{app.config.console.login_name}@52tt.com"
