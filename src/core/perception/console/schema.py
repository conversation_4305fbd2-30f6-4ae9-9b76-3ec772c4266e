from typing import Dict, Any, Literal, TypeVar

from pydantic import BaseModel, Field


class ConsoleMessage(BaseModel):
    content: str = Field(..., description="消息内容")
    content_type: Literal["text"] = Field(default="text", description="消息内容类型")
    message_type: Literal["message", "event"] = Field(default="message", description="消息类型")
    event: Dict[str, Any] = Field(default_factory=dict, description="事件内容")
    render_position: Literal["mission", "plan", "main"] = Field(default="main", description="消息位置")
    message_id: str = Field(..., description="消息id")
