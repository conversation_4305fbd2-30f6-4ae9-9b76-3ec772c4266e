import time
from typing import Literal

import lark_oapi as lark

from src.core.perception.console.schema import ConsoleMessage
from src.core.perception.lark.operator import LarkOperator
from src.core.schema.chatbot import SenderSchema
from src.core.schema.session import Session<PERSON>ontext, ChatInfo, UserInfo
from src.infra.app import app
from src.schema import consts


class ConsoleSender:
    SEND_MESSAGE_CHANNEL_PREFIX = consts.CLIENT_CHANNEL_PREFIX

    def __init__(self, lark_client: lark.Client):
        self.email = f"{app.config.console.login_name}@52tt.com"
        self._send_message_channel = f"{self.SEND_MESSAGE_CHANNEL_PREFIX}:{self.email}"
        self._lark_operator = LarkOperator(lark_client)

    def _get_message_id(self) -> str:
        return str(int(time.time()))

    def _format_msg(self, content: str,
                    message_type: Literal["message", "event"],
                    render_position: Literal["mission", "plan", "main"]) -> ConsoleMessage:
        return ConsoleMessage(
            content=content,
            message_type=message_type,
            message_id=self._get_message_id(),
            render_position=render_position,
        )

    def send_message(self, msg: dict) -> None:
        app.redis_cli.publish(self._send_message_channel,
                              self._format_msg(msg.get("content", "none"), "message", "main").json())

    def send_main_content(self, msg: dict) -> None:
        app.redis_cli.publish(self._send_message_channel,
                              self._format_msg(msg.get("content", "none"), "message", "main").json())

    def send_mission_content(self, msg: dict) -> None:
        content = ""
        for mission in msg["content"]:
            content += f"* {mission.get('description', 'none')}\n"
        app.redis_cli.publish(self._send_message_channel,
                              self._format_msg(content, "message", "mission").json())

    def send_plan_content(self, msg: dict) -> None:
        content = ""
        for plan in msg["content"]:
            content += f"* {plan.get('action', 'none')}\n"
        app.redis_cli.publish(self._send_message_channel,
                              self._format_msg(content, "message", "plan").json())

    def send_auth_event(self, msg: dict) -> None:
        app.redis_cli.publish(self._send_message_channel,
                              ConsoleMessage(
                                  content=msg.get("content", "none"),
                                  message_type="event",
                                  message_id=self._get_message_id(),
                                  render_position="main",
                                  event={
                                      "action": "auth",
                                      "session_id": msg.get("session_id", "none"),
                                  }
                              ).model_dump_json()
                              )

    def get_session_context(
            self, sender_info: SenderSchema, full_info: bool = False
    ) -> SessionContext:
        # TODO full_info, 待规划
        user = self._lark_operator.get_user_info(sender_info.user_id)
        return SessionContext(
            user_info=UserInfo(email=user.email, id=user.user_id, name=user.name),  # type: ignore
            chat_info=ChatInfo(type=sender_info.chat_type, chat_id=sender_info.chat_id),
        )
