import copy
import json
import queue
import threading
import time
from queue import Queue
from typing import Any, Dict, Optional

from langchain_core.messages import HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langfuse.callback import CallbackHandler
from langgraph.graph.graph import CompiledGraph
from langgraph.types import Command
from loguru import logger
from pydantic import BaseModel

from settings import Settings, get_or_creat_settings_ins
from src.core.cognition.memory import get_mem0_memory
from src.core.perception.session import SessionManager, get_task_memory
from src.core.schema.chatbot import (
    EventSchema,
    InputSchema,
    MessageSchema,
    RobotType,
    SenderSchema,
)
from src.helper.graph import AuthorizationSchema, InterruptSchema
from src.infra.app import app
from src.infra.atomic.bool import AtomicBool
from src.infra.feedback import UserTaskFeedBack
from src.infra.utils import safe_close_event_loop

from ...schema.messaage import ChatType, MessageEventAction, SenderType
from ...schema.session import SessionContext, SessionStatus
from ...spec.imessage import IMessageSenderSpec
from .utils import (
    _get_config,
    _get_mini_config,
    _get_thread_id_from_config,
    push_score_to_session,
)

get_or_creat_settings_ins()
settings = get_or_creat_settings_ins()


class _Robot(BaseModel):
    copilot: CompiledGraph
    msg_sender: IMessageSenderSpec
    session: SessionManager
    tracer: CallbackHandler = CallbackHandler()
    busy: AtomicBool = AtomicBool(False)

    _sc: Optional[SessionContext] = None

    class Config:
        arbitrary_types_allowed: bool = True

    def __init__(self, **data):
        super().__init__(**data)
        self._msg_queue: Queue[InputSchema] = Queue(maxsize=100)
        self._stop: threading.Event = threading.Event()

    @classmethod
    def chatbot_type(cls) -> RobotType:
        raise NotImplementedError("must set chabot type...")

    def _apologize_for_user(self, e: Exception, message_id: str):
        error_message = (
            f"抱歉，我出了点问题，请重试；{str(e)[:256]}"  # 限制异常信息长度
        )
        sanitized_error = "".join(
            c for c in error_message if c.isprintable()
        )  # 过滤不可打印字符

        base_msg = {
            "session_id": self.session.get_session(self._sc),
            "content": sanitized_error,
        }

        if self._sc.chat_info.type == ChatType.P2P:
            self.msg_sender.send_stream_message_to_user(
                rsp_type=SenderType.Final, msg=base_msg, user_id=self._sc.user_info.id
            )
        else:
            self.msg_sender.reply_msg_to_user(
                rsp_type=SenderType.Final, msg=base_msg, message_id=message_id
            )

    def is_stop(self) -> bool:
        return self._stop.is_set()

    def run(
        self,
        sender_info: SenderSchema,
    ) -> None:
        # 初始化 msg_sender 中定义的函数
        try:
            self._sc = self.msg_sender.get_session_context(sender_info)
            while not self._stop.is_set():
                message_id: Optional[str] = None
                try:
                    event = self._msg_queue.get(block=True, timeout=5)
                    if isinstance(event.payload, MessageSchema):
                        message_id = event.payload.message_id
                        self.busy.set(True)
                        self.on_message(event.payload)
                        logger.info(
                            f"{self._sc.user_info.id} {self._sc.user_info.name} chatbot is free."
                        )
                    else:
                        self.on_event(event.payload)
                        message_id = event.payload.message_id
                except queue.Empty:
                    continue
                finally:
                    self.busy.set(False)
                    if message_id is not None:
                        logger.info(
                            f"{self._sc.user_info.id} {self._sc.user_info.name} {message_id} close stream card."
                        )

        except Exception as e:
            logger.exception(e)
            self.stop()

    def stop(self) -> None:
        self._stop.set()
        safe_close_event_loop()

    def __save_long_memory(self, messages) -> None:
        start_time = time.time()
        msgs = []
        for msg in messages:
            if msg.type == "human":
                role = "user"
            else:
                role = "assistant"
            msgs.append({
                "role": role,
                "content": msg.content,
            })
        memory = get_mem0_memory()
        res = memory.add(messages=msgs, user_id=self._sc.user_info.email)
        cost_time = time.time() - start_time
        logger.info(f"save long memory: {res}, cost time: {cost_time:.2f}s")

    def call_copilot(self, input: dict[str, Any], config: RunnableConfig) -> None:
        if not self._sc:
            logger.error("SessionContext not initialized")
            return

        rsp = self.copilot.invoke(
            input["command"] if "command" in input else input, config
        )
        if self.is_interrupt(config):
            # self.interrupt_handler(config, self._sc.user_info.id)
            self.interrupt_handler(config, input["message_id"])
            return
        logger.info(f"copilot response: {rsp}")
        if rsp["status"] == SessionStatus.finish:
            state = self.copilot.get_state(config)
            # 保存任务记忆(程序性记忆)
            app.submit_concurrent_task(
                get_task_memory().save_by_session_id,
                self.session.get_session(self._sc),
            )
            # 保存长期记忆(语义记忆)
            if settings.mem0.enable:
                app.submit_concurrent_task(
                    self.__save_long_memory, state.values.get("messages", [])
                )
            else:
                logger.info("mem0 is not enabled, skip save long memory")

            self.session.close_session(messages=state.values.get("messages", []))

    def on_message(
        self,
        hmsg: MessageSchema,
    ):
        try:
            if not self._sc:
                logger.error("SessionContext not initialized")
                return

            session_id, history = self.session.get_session_and_history(self._sc)
            if self._sc.chat_info.type == ChatType.P2P:
                self.msg_sender.send_stream_card_to_user(
                    user_id=self._sc.user_info.id,
                    session_id=session_id,
                )
            config: RunnableConfig = _get_config(self._sc, session_id, [self.tracer])

            human_message = [HumanMessage(content=hmsg.content[0].content)]
            input = {
                "messages": human_message,
                "message_id": hmsg.message_id,
                "chat_type": self._sc.chat_info.type,
            }
            if history:
                logger.debug(f"get session history from session manager: {history}")
                input["history"] = history
                copy_history = copy.deepcopy(history)
                copy_history.extend(human_message)
                get_task_memory().record_history(session_id, copy_history)
            else:
                get_task_memory().record_history(session_id, human_message)

            self.call_copilot(input, config)
        except Exception as e:
            logger.error(
                f"{self._sc.user_info.name}({self._sc.user_info.id})'s chatbot on_message error: {e};"
            )  # type: ignore
            logger.exception(e)
            self.stop()
            self._apologize_for_user(e, hmsg.message_id)

    def new_event(self, event: InputSchema) -> None:
        self._msg_queue.put(event)

    def on_event(
        self,
        event: EventSchema,
    ) -> None:
        if not self._sc:
            logger.error("SessionContext not initialized")
            return
        try:
            logger.info(f"event: {event}")
            session_id = event.session_id

            config: RunnableConfig = _get_mini_config(session_id)
            if event.action == MessageEventAction.Like:
                state = self.copilot.get_state(config)
                logger.info(f"state: {state}")

                if self.tracer and self.tracer.langfuse:
                    push_score_to_session(
                        self.tracer.langfuse,
                        session_id,
                        1,
                        event.payload.get("option", ""),
                    )

                if self._sc.chat_info.type == ChatType.P2P:
                    self.msg_sender.disable_event_action_with_stream(
                        user_id=self._sc.user_info.id,
                        message_id=event.message_id,
                        actions=[event.action, MessageEventAction.Report.value],
                    )
                else:
                    self.msg_sender.disable_event_action(
                        event.action, event.message_id, event.content
                    )

                self.msg_sender.reply_msg_to_user(
                    SenderType.System,
                    {
                        "content": "感谢您的认可，我们会继续努力♥️♥️...",
                        "session_id": session_id,
                    },
                    event.message_id,
                )
                UserTaskFeedBack.update_feedback(session_id, event.action)
                # task memory
                app.submit_concurrent_task(
                    get_task_memory().save_by_session_id, session_id, True
                )
                return
            elif event.action == MessageEventAction.Stop:
                self.msg_sender.reply_msg_to_user(
                    SenderType.System,
                    {
                        "content": "对话已结束，您可以开始新的对话啦",
                        "session_id": session_id,
                    },
                    event.message_id,
                )
                state = self.copilot.get_state(config)
                self.session.close_session(messages=state.values.get("messages", []))
                UserTaskFeedBack.update_feedback(
                    session_id, event.payload.get("option", "")
                )
                if self._sc.chat_info.type == ChatType.P2P:
                    self.msg_sender.disable_event_action_with_stream(
                        user_id=self._sc.user_info.id,
                        message_id=event.message_id,
                        actions=[event.action],
                    )
                else:
                    self.msg_sender.disable_event_action(
                        event.action, event.message_id, event.content
                    )

                return
            elif event.action == MessageEventAction.Report:
                if self.tracer and self.tracer.langfuse:
                    push_score_to_session(
                        self.tracer.langfuse,
                        session_id,
                        -1,
                        event.payload.get("option", ""),
                    )

                    UserTaskFeedBack.update_feedback(
                        session_id, event.payload.get("option", "")
                    )

                    if self._sc.chat_info.type == ChatType.P2P:
                        self.msg_sender.disable_event_action_with_stream(
                            user_id=self._sc.user_info.id,
                            message_id=event.message_id,
                            actions=[event.action, MessageEventAction.Like.value],
                        )
                    else:
                        self.msg_sender.disable_event_action(
                            event.action, event.message_id, event.content
                        )
                return
            elif (
                event.action == MessageEventAction.Yes
                or event.action == MessageEventAction.No
            ):
                config: RunnableConfig = _get_config(
                    self._sc, session_id, [self.tracer]
                )
                logger.info(f"RunnableConfig: {config}")
                self.msg_sender.disable_event_action(
                    event.action, event.message_id, event.content
                )
                assert self.is_interrupt(config)
                self.call_copilot(
                    {
                        "command": Command(resume=event.action),
                        "message_id": event.message_id,
                    },
                    config,
                )

        except Exception as e:
            logger.error(
                f"{self._sc.user_info.name}({self._sc.user_info.id})'s chatbot on_event error: {e};"
            )  # type: ignore
            logger.exception(e)
            self.stop()
            self._apologize_for_user(e, event.message_id)

    def interrupt_handler(self, config: RunnableConfig, message_id: str) -> None:
        state = self.copilot.get_state(config)
        interrupt = state.tasks[0].interrupts[0]
        interrupt_schema = InterruptSchema.model_validate(interrupt.value)
        if isinstance(interrupt_schema.body, AuthorizationSchema):
            if self._sc.chat_info.type == ChatType.P2P:
                self.msg_sender.send_stream_message_to_user(
                    rsp_type=interrupt_schema.type,
                    msg={
                        "content": "请授权执行...",
                        "session_id": _get_thread_id_from_config(config),
                    },
                    user_id=self._sc.user_info.id,
                )
                self.msg_sender.reply_msg_to_user(
                    interrupt_schema.type,
                    {
                        "content": f"{interrupt_schema.body.action}\n将使用如下参数执行：\n{json.dumps(interrupt_schema.body.args, indent=2)}",
                        "session_id": _get_thread_id_from_config(config),
                    },
                    message_id,
                )
            else:
                self.msg_sender.send_nominator_auth_to_user(
                    self._sc.chat_info.chat_id,
                    self._sc.user_info.id,
                    {
                        "content": f"{interrupt_schema.body.action}\n将使用如下参数执行：\n{json.dumps(interrupt_schema.body.args, indent=2)}",
                        "session_id": _get_thread_id_from_config(config),
                    },
                )
        elif isinstance(interrupt_schema.body, InputSchema):
            if self._sc.chat_info.type == ChatType.P2P:
                self.msg_sender.send_stream_message_to_user(
                    rsp_type=interrupt_schema.type,
                    msg={
                        "content": interrupt_schema.body.prompt,
                        "session_id": _get_thread_id_from_config(config),
                    },
                    user_id=self._sc.user_info.id,
                )
            else:
                self.msg_sender.reply_msg_to_user(
                    interrupt_schema.type,
                    {
                        "content": interrupt_schema.body.prompt,
                        "session_id": _get_thread_id_from_config(config),
                    },
                    message_id,
                )

        return

    def is_interrupt(self, config: RunnableConfig | None):
        if not config:
            return False
        state = self.copilot.get_state(config)
        if state.next:
            return True
        return False

    @staticmethod
    def from_config(
        settings: Settings, msg_sender: IMessageSenderSpec, input_schema: InputSchema
    ) -> "_Robot":
        raise NotImplementedError()
