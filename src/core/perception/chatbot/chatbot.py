from langgraph.graph.graph import CompiledGraph
from qagent.agents import Agent
from qagent.action_executor import AgentActionExecutor, ToolNodeActionExecutor
from settings import Settings
from .robot import _Robot
from .utils import _get_checkpoint
from ..session import SessionManager
from ...cognition.agents.plan_and_execute import PlanAndExecute
from ...cognition.agents.plan_and_execute.function import FunctionPlan
from ...cognition.copilots import Chat<PERSON>opilot
from ...execution.toolkits import get_chat_mcp_toolkits
from ...schema.chatbot import RobotType, InputSchema
from ...spec.imessage import IMessageSenderSpec


class _ChatBot(_Robot):
    copilot: CompiledGraph
    msg_sender: IMessageSenderSpec
    session: SessionManager

    @classmethod
    def chatbot_type(cls) -> RobotType:
        return RobotType.Chat

    @staticmethod
    def from_config(
        settings: Settings, msg_sender: IMessageSenderSpec, input_schema: InputSchema
    ) -> "_ChatBot":
        checkpointer = _get_checkpoint(settings)

        _sc = msg_sender.get_session_context(input_schema.sender)
        mcp_toolkits = get_chat_mcp_toolkits(_sc.user_info)
        agent = Agent(
            workflowbuilder=FunctionPlan(name="function"),
            action_executor=ToolNodeActionExecutor(toolkit=mcp_toolkits),  # type: ignore
        )
        agent.init_env()
        planner = PlanAndExecute().setup(
            action_executor=AgentActionExecutor(agent, toolkits=mcp_toolkits),
            msg_sender=msg_sender,
        )

        copilot = ChatCopilot(msg_sender=msg_sender, planner=planner).build(
            checkpointer
        )
        session = SessionManager(
            chat_id=input_schema.sender.chat_id,
            expired_time=settings.chatbot.shortterm_memory_expired,
        )
        return _ChatBot(copilot=copilot, msg_sender=msg_sender, session=session)
