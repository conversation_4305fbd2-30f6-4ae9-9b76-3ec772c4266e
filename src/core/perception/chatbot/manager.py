import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Dict, Optional, List, Type

from loguru import logger

from settings import Settings
from src.core.schema.chatbot import InputSchema, RobotType
from .chatbot import _ChatBot
from .robot import _Robot
from ..session import TaskMemory
from ...spec.imessage import IMessageSenderSpec


class RoBotManager:
    """
    ChatBot 管理器，用于缓存 ChatBot 对象，根据路由 ID 获取对应的 ChatBot。
    具有定时回收闲置 ChatBot 的能力，可以配置最大缓存个数。

    线程安全：
    所有对共享数据的访问都通过线程锁进行保护，确保在多线程环境下安全运行。
    """

    def __init__(
        self,
        max_size: int = 1000,
        busy_check_interval: int = 900,  # 默认检查busy状态的间隔为 15 分钟
        thread_pool_size: int = 1001,  # 线程池大小, max_size+1, 对一个清理线程
    ):
        """
        初始化 ChatBotManager。

        Args:
            max_size: 最大缓存数量
            busy_check_interval: 检查busy状态的间隔（秒）
            thread_pool_size: 线程池大小
        """
        self._robots: Dict[str, _Robot] = {}  # 路由 ID -> ChatBot 映射
        self._max_size = max_size
        self._busy_check_interval = busy_check_interval
        self._last_busy_check = time.time()  # 上次检查busy状态的时间
        self._lock = threading.RLock()  # 可重入锁，保证线程安全

        # 创建线程池
        self._thread_pool = ThreadPoolExecutor(
            max_workers=thread_pool_size, thread_name_prefix="Robot-Worker"
        )

        # 启动定时清理任务（使用线程池）
        self._stop_event = threading.Event()
        self._thread_pool.submit(self._cleanup_loop)

        self.robot_type_list: List[Type[_Robot]] = []

        logger.info(
            f"RobotManager initialized with max_size={max_size}, "
            f"busy_check_interval={busy_check_interval}s, "
            f"thread_pool_size={thread_pool_size}"
        )

    def register(self, chatbot: Type[_Robot]) -> "RoBotManager":
        self.robot_type_list.append(chatbot)
        return self

    def get(
        self,
        input_schema: InputSchema,
        factory: Optional[Callable[[InputSchema], _Robot]] = None,
    ) -> Optional[_Robot]:
        """
        根据路由 ID 获取 ChatBot，如果不存在且提供了 factory 函数，则创建新的 ChatBot。

        Args:
            input_schema: 输入信息，包含路由ID
            factory: 创建 ChatBot 的工厂函数，接收 input_schema 参数

        Returns:
            ChatBot 对象，如果不存在且未提供 factory 则返回 None
        """
        route_id = input_schema.route_id

        with self._lock:
            # 如果已存在，直接返回
            if route_id in self._robots:

                chatbot = self._robots[route_id]
                if not chatbot.is_stop():
                    logger.debug(f"Reusing existing Robot for route {route_id}")
                    return chatbot

            # 如果不存在但提供了工厂函数，创建新的 ChatBot
            if factory is not None:
                # 检查是否达到最大缓存数量
                if len(self._robots) >= self._max_size:
                    self._remove_one_chatbot()

                # 创建新的 ChatBot
                chatbot = factory(input_schema)
                self._robots[route_id] = chatbot
                logger.debug(f"Created new Robot for route {route_id}")
                return chatbot

            return None

    def put(self, route_id: str, chatbot: _Robot) -> None:
        """
        将 ChatBot 添加到缓存中。

        Args:
            route_id: 路由 ID
            chatbot: ChatBot 对象
        """
        with self._lock:
            # 检查是否达到最大缓存数量
            if len(self._robots) >= self._max_size and route_id not in self._robots:
                self._remove_one_chatbot()

            self._robots[route_id] = chatbot
            logger.debug(f"Added Robot for route {route_id} to cache")

    def remove(self, route_id: str) -> Optional[_Robot]:
        """
        从缓存中移除指定路由的 ChatBot。

        Args:
            route_id: 路由 ID

        Returns:
            被移除的 ChatBot 对象，如果不存在则返回 None
        """
        with self._lock:
            if route_id in self._robots:
                robot = self._robots.pop(route_id)
                logger.debug(f"Removed Robot for route {route_id} from cache")
                return robot
            return None

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._robots.clear()
            logger.info("Cleared all Robots from cache")

    def size(self) -> int:
        """返回当前缓存的 ChatBot 数量"""
        with self._lock:
            return len(self._robots)

    def stop(self) -> None:
        """停止清理线程和线程池"""
        self._stop_event.set()

        # 关闭线程池
        self._thread_pool.shutdown(wait=False)
        logger.info("RobotsManager thread pool stopped")

    def _cleanup_loop(self) -> None:
        """定时检查 ChatBot 的 busy 状态"""
        while not self._stop_event.is_set():
            # 等待指定的检查间隔
            time.sleep(self._busy_check_interval)
            if self._stop_event.is_set():
                break

            try:
                self._check_busy_chatbots()
                self._last_busy_check = time.time()
            except Exception as e:
                logger.error(f"Error during Robots cleanup: {e}")

    def _check_busy_chatbots(self) -> None:
        """
        检查所有ChatBot的busy状态，如果为True则置为False，如果为False则释放实例

        线程安全：使用 self._lock 保护对共享数据的访问
        """
        release_count = 0
        reset_count = 0

        with self._lock:
            # 创建一个副本以避免在迭代过程中修改字典
            robots_copy = list(self._robots.items())

            for route_id, robot in robots_copy:
                if robot.busy.get():
                    # 如果busy为True，置为False
                    robot.busy.set(False)
                    reset_count += 1
                    logger.debug(
                        f"Reset busy state to False for Robots of route {route_id}"
                    )
                else:
                    # 如果busy为False，释放实例
                    robot.stop()  # 先调用stop函数
                    self._robots.pop(route_id, None)
                    release_count += 1
                    logger.debug(
                        f"Released Robot for route {route_id} due to inactive busy state"
                    )

            if reset_count > 0 or release_count > 0:
                logger.info(
                    f"Busy state check: reset {reset_count} Robots, released {release_count} Robots"
                )

    def _remove_one_chatbot(self) -> None:
        """
        移除一个 ChatBot 实例，当达到最大缓存数量时调用

        线程安全：此方法只在已获取 self._lock 的上下文中调用
        """
        if not self._robots:
            return

        # 简单地移除第一个 ChatBot
        route_id = next(iter(self._robots))
        chatbot = self._robots.pop(route_id)
        chatbot.stop()
        logger.debug(f"Removed Robot for route {route_id} due to cache limit")

    def __del__(self):
        """析构函数，确保线程池被正确停止"""
        self.stop()

    def dispatch(
        self,
        input_schema: InputSchema,
        settings: Settings,
        msg_sender: IMessageSenderSpec,
        chatbot_type: RobotType,
    ) -> None:
        """
        分发输入到对应的ChatBot实例，如果实例不存在则创建

        Args:
            input_schema: 输入信息，包含路由ID
            settings: 配置信息
            msg_sender: 消息发送器
            sc: 会话上下文
        """

        # 定义创建ChatBot的工厂函数
        def create_robot(_input_schema: InputSchema) -> _Robot:
            for robot in self.robot_type_list:
                if robot.chatbot_type() == _input_schema.chatbot_type:
                    bot = robot.from_config(settings, msg_sender, _input_schema)
                    break
            else:
                raise ValueError(f"Invalid robot type, {chatbot_type}")

            # 使用线程池启动ChatBot的消息处理线程，并传递会话上下文
            self._thread_pool.submit(bot.run, _input_schema.sender)
            return bot

        # 从缓存中获取ChatBot实例，如果不存在则创建
        robot = self.get(input_schema, create_robot)
        if robot:
            # 将输入传递给ChatBot处理
            robot.new_event(input_schema)
            logger.debug(f"Dispatched event to Robot for route {input_schema.route_id}")
        else:
            logger.error(
                f"Failed to get or create Robot for route {input_schema.route_id}"
            )
