from langchain_core.callbacks import Callbacks
from langchain_core.runnables.config import RunnableConfig
from langfuse import Langfuse
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.types import Checkpointer
from loguru import logger
from psycopg import Connection
from psycopg.rows import DictRow
from psycopg_pool import ConnectionPool

from settings import Settings
from src.core.schema.session import SessionContext
from src.infra.app import app
from src.schema import consts


def _get_config(
    sc: SessionContext, session_id: str, callbacks: Callbacks
) -> RunnableConfig:
    return {
        "configurable": {
            "thread_id": session_id,
            "session_context": sc,
        },
        "metadata": {
            "langfuse_session_id": session_id,
            "langfuse_user_id": sc.user_info.id,
        },
        "callbacks": callbacks,
        "recursion_limit": 100,
    }


def _get_mini_config(session_id: str) -> RunnableConfig:
    return {
        "configurable": {
            "thread_id": session_id,
        },
    }


def _get_thread_id_from_config(config: RunnableConfig) -> str:
    return config["configurable"]["thread_id"]  # type: ignore


def _get_checkpoint(settings: Settings) -> Checkpointer:
    if app.current_env == consts.NO_PROD_RUNTIME_ENV:
        logger.info("Using InMemorySaver")
        return InMemorySaver()

    logger.info("Using PostgresSaver")
    conn = ConnectionPool[Connection[DictRow]](
        f"postgresql://{settings.postgres.user}:{settings.postgres.password}@{settings.postgres.host}:{settings.postgres.port}/{settings.postgres.database}",
        min_size=settings.postgres.min_size,
        max_size=settings.postgres.max_size,
    )

    checkpointer = PostgresSaver(conn=conn)

    return checkpointer


def push_score_to_session(
    langfuse_client: Langfuse, session_id: str, score: int, comment: str = ""
) -> bool:
    """
    将用户反馈分数推送到Langfuse会话中

    Args:
        langfuse_client: Langfuse客户端实例
        session_id: 会话ID
        score: 用户反馈分数 (1表示点赞,-1表示差评)
        comment: 用户反馈评论,默认为空字符串

    Returns:
        bool: 是否成功推送所有评分

    Raises:
        Exception: 当API调用失败时抛出异常
    """
    try:
        # 获取会话相关的所有traces
        traces = langfuse_client.get_traces(session_id=session_id)
        if not traces.data:
            logger.warning(f"No traces found for session {session_id}")
            return False

        # 批量处理所有traces的评分
        success = True
        for trace in traces.data:
            try:
                langfuse_client.score(
                    trace_id=trace.id,
                    observation_id="",
                    name="user-feedback",
                    data_type="NUMERIC",
                    value=score,
                    comment=comment,
                )
            except Exception as e:
                logger.error(f"Failed to score trace {trace.id}: {str(e)}")
                success = False

        return success

    except Exception as e:
        logger.error(f"Failed to push score to session {session_id}: {str(e)}")
        raise
