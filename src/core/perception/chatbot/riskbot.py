from langgraph.graph.graph import CompiledGraph
from loguru import logger
from qagent.action_executor import AgentActionExecutor, ToolNodeActionExecutor
from qagent.agents import Agent

from settings import Settings
from src.core.schema.chatbot import (
    MessageSchema,
    RobotType,
    EventSchema,
    MediaType,
    InputSchema,
)
from src.schedule import get_risk_card
from .robot import _Robot
from .utils import _get_checkpoint
from ..session import SessionManager
from ...cognition.agents.plan_and_execute import PlanAndExecute
from ...cognition.agents.plan_and_execute.function import FunctionPlan
from ...cognition.copilots.risk import RiskCopilot
from ...execution.toolkits import get_risk_mcp_toolkits
from ...schema.messaage import ChatType, MessageEventAction
from ...spec.imessage import IMessageSenderSpec


class _RiskBot(_Robot):

    copilot: CompiledGraph
    msg_sender: IMessageSenderSpec
    session: SessionManager
    @classmethod
    def chatbot_type(cls) -> RobotType:
        return RobotType.Risk

    def on_event(
        self,
        event: EventSchema,
    ):
        try:
            if event.action == MessageEventAction.DealRisk:
                hmsg = MessageSchema(
                    message_id=event.message_id,
                    content=[MediaType(type="text", content=event.content)],
                )
                self._sc.chat_info.type = ChatType.P2P
                self.on_message(hmsg)
                return
            super().on_event(event)
        except Exception as e:
            logger.error(
                f"{self._sc.user_info.name}({self._sc.user_info.id})'s chatbot on event error: {e};"
            )
            logger.exception(e)
            self.stop()
            self._apologize_for_user(e, event.message_id)

    def on_message(
        self,
        hmsg: MessageSchema,
    ):
        try:
            if hmsg.content[0].content == "风险列表":
                get_risk_card(self._sc.user_info)
                return
            super().on_message(hmsg)
        except Exception as e:
            logger.error(
                f"{self._sc.user_info.name}({self._sc.user_info.id})'s chatbot on_message error: {e};"
            )  # type: ignore
            logger.exception(e)
            self.stop()
            self._apologize_for_user(e, hmsg.message_id)

    @staticmethod
    def from_config(
        settings: Settings, msg_sender: IMessageSenderSpec, input_schema: InputSchema
    ) -> "_RiskBot":
        checkpointer = _get_checkpoint(settings)

        _sc = msg_sender.get_session_context(input_schema.sender)
        mcp_toolkits = get_risk_mcp_toolkits(_sc.user_info)
        agent = Agent(
            workflowbuilder=FunctionPlan(name="function"),
            action_executor=ToolNodeActionExecutor(toolkit=mcp_toolkits),  # type: ignore
        )
        agent.init_env()
        planner = PlanAndExecute().setup(
            action_executor=AgentActionExecutor(agent, toolkits=mcp_toolkits),
            msg_sender=msg_sender,
        )

        copilot = RiskCopilot(msg_sender=msg_sender, planner=planner).build(
            checkpointer
        )
        session = SessionManager(
            chat_id=input_schema.sender.chat_id,
            expired_time=settings.chatbot.shortterm_memory_expired,
        )
        return _RiskBot(copilot=copilot, msg_sender=msg_sender, session=session)
