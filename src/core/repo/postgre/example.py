import typing

from sqlalchemy import Column, Integer, String, Table, ForeignKey
from sqlalchemy.orm import relationship

from src.infra.clients.postgres.orm import BaseModel

__all__ = ['Parent', 'Child', 'Friend']


class Child(BaseModel):
    __tablename__ = 'child'
    __allow_unmapped__ = True
    id = Column(Integer, primary_key=True)
    parent_id = Column(Integer, index=True, nullable=True)  # 依然不加ForeignKey，但用于关联
    name = Column(String(32))

    parent: "Parent" = relationship(
        'Parent',
        back_populates='child',
        primaryjoin='Parent.id==foreign(Child.parent_id)',
        uselist=False,
        lazy="joined"
    )


parent_friend_association = Table(
    "parent_friend_association",
    BaseModel.metadata,
    Column("id", Integer, primary_key=True),
    Column("parent_id", Integer, ForeignKey('parent.id'), index=True),
    <PERSON>umn("friend_id", Integer, Foreign<PERSON>ey('friend.id'), index=True),
)


class Parent(BaseModel):
    __tablename__ = 'parent'
    __allow_unmapped__ = True
    id = Column(Integer, primary_key=True)
    name = Column(String(32), unique=True, nullable=True)
    age = Column(Integer)
    child: typing.List["Child"] = relationship(
        'Child',
        back_populates='parent',
        primaryjoin='Parent.id==foreign(Child.parent_id)',
        uselist=True,
        lazy="joined"

    )

    friends: typing.List["Friend"] = relationship(
        secondary=parent_friend_association,
        back_populates="parents",
        uselist=True,
        lazy="joined"
    )


class Friend(BaseModel):
    __tablename__ = 'friend'
    __allow_unmapped__ = True
    id = Column(Integer, primary_key=True)
    name = Column(String(32), unique=True, nullable=True)
    age = Column(Integer)
    parents: typing.List["Parent"] = relationship(
        secondary=parent_friend_association,
        back_populates="friends",
        uselist=True,
        # lazy="joined"
    )
