from sqlalchemy import Column, Integer, String, Text, DateTime, func

from src.infra.clients.postgres.orm import BaseModel


class IndicatorCollection(BaseModel):
    __tablename__ = 'indicator_collection'
    __allow_unmapped__ = True
    id = Column(Integer, primary_key=True)
    session_id = Column(String(128), nullable=False)
    user_id = Column(String(28), nullable=False)
    end_type = Column(String(28), nullable=True)
    feedback = Column(String(128), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    feedback_time = Column(DateTime(timezone=True), nullable=True)
