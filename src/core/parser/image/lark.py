import base64
import json
from typing import Optional, Literal
from lark_oapi.api.im.v1 import GetImageRequest, GetImageResponse, GetMessageResourceRequest, GetMessageResourceResponse
from loguru import logger

from src.core.parser.image.utils import convert_image_to_text
from src.infra import clients


class LarkImageConvertor:

    def __init__(self):
        self.client = clients.get_lark_client()

    def get_image_base64(self, message_id: str, img_key: str) -> Optional[str]:
        # 构造请求对象
        # 构造请求对象
        request: GetMessageResourceRequest = GetMessageResourceRequest.builder() \
            .message_id(message_id) \
            .file_key(img_key) \
            .type("image") \
            .build()

        # 发起请求
        response: GetMessageResourceResponse = self.client.im.v1.message_resource.get(request)
        # 处理失败返回
        if not response.success():
            logger.error(
                f"client.im.v1.image.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, "
                f"resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return None

        return base64.b64encode(response.file.read()).decode('utf-8')

    def convert_to_text(self, img_base64_text: str, detail_level: Literal["low", "high"] = "high") -> Optional[str]:
        try:
            return convert_image_to_text(img_base64_text, detail_level)
        except IndexError:
            logger.error("convert_image_to_text failed")
            return None
