import base64
import io
from typing import Dict, List, Literal

from PIL import Image
from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from loguru import logger

from src.helper.utils import get_chat_model

IMAGE_MAX_SIZE = 10 * 1024 * 1024
MAX_IMAGE_DIMENSION = 7990


def compress_n_convert2png(image_b64_data: str) -> str:
    # Extract bytes and media type from base64 data URL
    image_bytes = base64.b64decode(image_b64_data)

    img = Image.open(io.BytesIO(image_bytes))

    # Check if image is under max dimensions and size
    is_under_dimension_limit = (
        img.width < MAX_IMAGE_DIMENSION and img.height < MAX_IMAGE_DIMENSION
    )

    # Check if either dimension exceeds 7900px (Claude disallows >= 8000px)
    # Resize image if needed
    if not is_under_dimension_limit:
        # Calculate the new dimensions while maintaining aspect ratio
        if img.width > img.height:
            new_width = MAX_IMAGE_DIMENSION
            new_height = int((MAX_IMAGE_DIMENSION / img.width) * img.height)
        else:
            new_height = MAX_IMAGE_DIMENSION
            new_width = int((MAX_IMAGE_DIMENSION / img.height) * img.width)

        # Resize the image
        img = img.resize((new_width, new_height), Image.DEFAULT_STRATEGY)

    quality = 100
    output = io.BytesIO()
    img = img.convert("RGB")  # Ensure image is in RGB mode for JPEG conversion
    img.save(output, format="PNG", quality=quality)

    # Reduce quality until image is under max size
    while len(base64.b64encode(output.getvalue())) > IMAGE_MAX_SIZE and quality > 10:
        logger.warning(f"Reducing quality to {quality}")
        output = io.BytesIO()
        img.save(output, format="PNG", quality=quality)
        quality -= 5

    return base64.b64encode(output.getvalue()).decode("utf-8")


def convert_image_to_text(
    image_b64_data: str, detail_level: Literal["low", "high"] = "high"
) -> str:
    model = get_chat_model("image")

    b64_str = compress_n_convert2png(image_b64_data)

    description = "详细描述图片的内容"
    if detail_level == "low":
        description = "简单描述图片的内容"

    content_list: List[Dict[str, str]] = [
        {"type": "text", "text": f"{description}，不需要给出总结及任何多余内容"},
        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{b64_str}"}},
    ]

    msg = HumanMessage(content=content_list)
    chain = ChatPromptTemplate.from_messages([msg]) | model
    return chain.invoke({}).content
