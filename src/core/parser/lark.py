import json
from typing import Optional, List, Dict, Any

from lark_oapi.api.im.v1 import EventMessage
from loguru import logger
from pydantic import BaseModel, Field

from src.core.parser.image.lark import LarkImageConvertor
from src.core.spec.imessage import MessageParserSpec, ConvertToStringSpec


class Text(ConvertToStringSpec, BaseModel):
    type: str = Field(default="text", description="消息类型")
    content: Dict[str, Any] = Field(..., description="lark message")

    class Config:
        arbitrary_types_allowed = True

    def convert_to_string(self) -> str:
        return self.content.get("text", "")


class Post(ConvertToStringSpec, BaseModel):
    type: str = Field(default="post", description="消息类型")
    content: Dict[str, Any] = Field(..., description="lark message")
    can_convert_type: List[str] = Field(
        default=["text", "a", "code_block", "img"],
        description="支持的转换类型"
    )

    class Config:
        arbitrary_types_allowed = True

    def _text(self, item: Dict[str, Any]):
        text = item.get("text", None)
        if not text:
            return "" + "\n"

        styles = item.get("style", [])

        if "bold" in styles:
            text = f"**{text}**"

        elif "underline" in styles:
            return f"*{text}*"

        return text + "\n"

    def _a(self, item: Dict[str, Any]):
        return f"[link]({item.get('href', '')})" + "\n"

    def _code_block(self, item: Dict[str, Any]):
        return f'''
```{item.get('language', '')}
{item.get("text", "")}
```
'''

    def _image(self, item: Dict[str, Any]) -> str:
        image_key = item.get("image_key", None)
        if image_key is None:
            return "\n"
        image_obj = Image(
            type="image",
            content=item,
            message_id=self.message_id
        )
        return image_obj.convert_to_string() + "\n"

    def convert_to_string(self) -> str:
        result = ""
        # title
        title = self.content.get("title", None)
        if title:
            result += f"# {title}\n"

        for line in self.content.get("content", []):
            for item in line:
                if item["tag"] in self.can_convert_type:
                    if item["tag"] == "text":
                        result += self._text(item)
                    elif item["tag"] == "a":
                        result += self._a(item)
                    elif item["tag"] == "code_block":
                        result += self._code_block(item)
                    elif item["tag"] == "img":
                        result += self._image(item)
        return result


class Image(ConvertToStringSpec, BaseModel):
    type: str = Field(default="image", description="消息类型")
    content: Dict[str, Any] = Field(..., description=" image content")
    image_convertor: LarkImageConvertor = Field(default_factory=lambda _: LarkImageConvertor())

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, **data):
        super().__init__(**data)

    def _format_image_message(self, content: str) -> str:
        return f"<图片内容>{content}</图片内容>"

    def convert_to_string(self) -> str:
        image_key: Optional[str] = self.content.get("image_key", None)
        if image_key is None:
            logger.warning("image key is empty")
            return self._format_image_message("")

        base64_str = self.image_convertor.get_image_base64(self.message_id, image_key)
        return self._format_image_message(self.image_convertor.convert_to_text(base64_str))


support_message_type = {
    "text": Text,
    "post": Post,
    "image": Image,
}


class LarkMessageParser(BaseModel, MessageParserSpec):
    message_id: str = Field(..., description="message id")
    message: EventMessage = Field(..., description="lark message")

    class Config:
        arbitrary_types_allowed = True

    def parse(self) -> (Optional[str], str):
        if self.message.message_type not in support_message_type:
            return None, ""

        content = json.loads(self.message.content)
        logger.debug(f"lark message:\n {content}")
        return self.message.message_type, support_message_type[self.message.message_type](
            message_id=self.message_id,
            content=content).convert_to_string()
