from enum import Enum
from typing import Any, Dict, List, Optional, Union, Literal

from pydantic import BaseModel, Field


class RobotType(str, Enum):
    Chat = "chat"
    Risk = "risk"


class SenderSchema(BaseModel):
    user_id: str = Field(..., description="用户id")
    chat_type: str = Field(..., description="会话类型, p2p or group")
    chat_id: str = Field(..., description="群id")


class EventSchema(BaseModel):
    action: Union[Literal["stop", "like", "report", "yes", "no", "deal_risk"], str] = (
        Field(..., description="事件动作")
    )
    message_id: str = Field(..., description="消息id")
    session_id: str = Field(..., description="会话id")
    content: str = Field(..., description="事件内容")
    payload: Optional[Dict[str, Any]] = Field(default=None, description="事件负载")


class MediaType(BaseModel):
    type: str = Field(..., description="媒体类型")
    content: Any = Field(..., description="媒体内容")


class MessageSchema(BaseModel):
    message_id: str = Field(..., description="消息id")
    content: List[MediaType]


class InputSchema(BaseModel):
    sender: SenderSchema = Field(..., description="发送者信息")
    payload: Union[MessageSchema, EventSchema] = Field(..., description="输入内容")
    source: Literal["lark", "console"] = Field(default="lark", description="消息来源")
    chatbot_type: RobotType = Field(..., description="聊天机器人类型")

    @property
    def route_id(self) -> str:
        """
        生成路由ID，用于唯一标识一个会话

        Returns:
            由user_id、chat_type、chat_id和source组成的唯一字符串
        """
        return f"{self.sender.chat_id}_{self.sender.user_id}_{self.source}_{self.chatbot_type}"


class InputUnsupportedSchema(BaseModel):
    """
    输入不支持的格式
    """

    content: str = Field(
        default="Sorry，我暂时看不懂该类型的信息，我会继续努力的～～～💪",
        description="输入不支持的格式",
    )
    sender: SenderSchema = Field(..., description="发送者信息")
    chatbot_type: RobotType = Field(default="chat", description="聊天机器人类型")
