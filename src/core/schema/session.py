from enum import Enum
from typing import Dict, Optional

from pydantic import BaseModel, Field


class SessionStatus(str, Enum):
    # 调用caller
    agent = "agent" 
    # 缺参数找人类
    human = "human"
    # 任务搞定了, 告诉人类
    finish = "finish"


class UserInfo(BaseModel):
    id: str = Field(..., description="用户id")
    email: Optional[str] = Field(default="", description="用户邮箱")
    name: Optional[str] = Field(default="尚未登录", description="用户名")

    def __repr__(self) -> str:
        return f"用户:{self.name}, email:{self.email}"

    def __str__(self) -> str:
        return super().__repr__()


class ChatInfo(BaseModel):
    type: str = Field(..., description="会话类型, p2p or group")
    chat_id: str = Field(..., description="会话id")
    slots: Optional[Dict] = Field(default=None, description="会话上下文")


class SessionContext(BaseModel):
    """Session context for qianxing"""

    user_info: UserInfo = Field(..., description="用户信息")
    chat_info: ChatInfo = Field(..., description="")
