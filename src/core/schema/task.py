from enum import Enum
from typing import Annotated, List, Optional, TypedDict, Union

from pydantic import BaseModel, Field
from pydantic.json_schema import SkipJsonSchema


class TaskStatus(Enum):
    finish = "finish"
    missing_context = "missing_context"
    failed = "failed"
    givenup = "givenup"


class TaskResult(BaseModel):
    status: TaskStatus = Field(description="任务状态")
    result: str = Field(description="返回结果")


class TaskMeta(BaseModel):
    id: int = Field(description="任务唯一标识符，新任务自增，更新任务保持原id")
    name: str = Field(description="简明的任务名称, 例如：用户xxx想要做什么")
    description: str = Field(description="详细的任务描述，仅包含所有用户提供的关键信息，不包含评价性内容，不用改变用户意图")
    result: SkipJsonSchema[Optional[TaskResult]] = Field(
        default=None, description="任务完成情况"
    )


class DeleteTask(BaseModel):
    id: int = Field(description="任务id")
    name: str = Field(description="简明的任务名称")
    reason: str = Field(description="放弃任务的原因")

class UpdateTaskResult(TaskMeta):
    name: str = "update_result"
    description: str = "update task result"


class UpdateTask(TaskMeta):
    id: int = Field(description="任务id")
    name: str = "update"
    description: str = Field(description="任务描述")


IntentsLike = Union[List[TaskMeta], TaskMeta]


def add_task(left: IntentsLike, right: IntentsLike) -> IntentsLike:
    if not isinstance(left, list):
        left = [left]
    if isinstance(right, UpdateTaskResult):
        for i in left:
            if i.id == right.id:
                i.result = right.result
                return left
    if not isinstance(right, list):
        right = [right]
    return right
    # for item in right:
    #     if item.id <= len(left):
    #         for i in left:
    #             if i.id == item.id:
    #                 i.description = item.description
    #                 i.result = None
    #     else:
    #         left.append(item)
    # return left


class TaskList(TypedDict):
    tasks: Annotated[List[TaskMeta], add_task]
    doing_task: int
