import json
import queue
import threading
import time
from queue import Queue
from typing import Union, List, Dict

from loguru import logger
from pydantic import ValidationError

from settings import get_or_creat_settings_ins
from src.core.perception.chatbot import RoBotManager
from src.core.perception.console.receiver import get_console_message_stream_name
from src.core.perception.lark.utils import get_lark_message_stream_name
from src.core.perception.sender import IMessageSender
from src.core.schema.chatbot import (
    InputSchema,
    InputUnsupportedSchema,
    MessageSchema,
    RobotType,
)
from src.core.spec.imessage import IMessageSenderSpec
from src.infra import clients
from src.infra.app import app
from src.schema import consts


class MessageDispatchServer:
    """
    消息分发服务器类

    负责接收、处理和分发消息到相应的聊天机器人实例，并管理消息队列与响应机制。
    该服务器在单独的线程中运行，能够持续监听消息队列并进行处理。
    """

    CONSUMER_GROUP = consts.MSG_CONSUMER_GROUP
    CONSUMER_NAME = "MessageDispatchServer"

    def __init__(
        self,
        chatbot_manager: RoBotManager,  # 聊天机器人管理器
        message_stream_name: List[str],
    ):
        """
        初始化消息分发服务器

        Args:
            chatbot_manager: 管理多个聊天机器人实例的管理器
            message_sender: 用于向用户发送响应消息的接口实现
            message_stream_name: Redis消息流名称列表
        """
        self.chatbot_manager = chatbot_manager  # 聊天机器人管理器实例
        self._msg_q: Queue[Union[InputSchema, InputUnsupportedSchema]] = Queue(
            maxsize=1024
        )
        self._settings = get_or_creat_settings_ins()  # 获取或创建全局设置实例
        self._stop_event = threading.Event()  # 停止事件，用于控制服务器的启动与停止
        self._message_stream_name = message_stream_name  # Redis消息流名称列表
        self._consumer_threads = []  # 消息流消费者线程列表
        self._message_sender_set: Dict[str, IMessageSenderSpec] = (
            self._new_message_sender_set()
        )

    def _new_message_sender_set(self) -> Dict[str, IMessageSenderSpec]:
        return {
            RobotType.Chat.value: IMessageSender(
                client=clients.get_lark_client(RobotType.Chat)
            ),
            RobotType.Risk.value: IMessageSender(
                client=clients.get_lark_client(RobotType.Risk)
            ),
        }

    def _message_sender(self, chatbot_type: RobotType) -> IMessageSenderSpec:
        return self._message_sender_set[chatbot_type.value]

    def _consume_message_stream(self, message_stream_name: str):
        logger.info(f"Consuming message stream: {message_stream_name}")
        try:
            app.redis_cli.xgroup_setid(message_stream_name, self.CONSUMER_GROUP, id="$")
        except Exception as e:
            logger.warning(f"set consumer group message stream offset failed: {e}")
        while not self._stop_event.is_set():
            try:
                # 从Redis流中读取消息
                messages = app.redis_cli.xreadgroup(
                    groupname=self.CONSUMER_GROUP,
                    consumername=self.CONSUMER_NAME,
                    # 读取未被确认的信息
                    streams={message_stream_name: ">"},
                    count=10,
                    block=100,
                )

                # 处理获取到的消息
                if messages and len(messages) > 0:
                    # 获取指定流的消息
                    stream_messages = messages[0][1]  # [0]是流名称，[1]是消息列表

                    for message_id, message_data in stream_messages:
                        #
                        try:
                            logger.info(
                                f"Message id: {message_id}, data: {message_data}"
                            )
                            # msg_data = {k.decode('utf-8'): v.decode('utf-8') for k, v in message_data.items()}
                            # 从消息中提取数据并解析为对象
                            if "data" in message_data:
                                message_json = message_data["data"]
                                # 尝试解析为InputSchema或InputUnsupportedSchema
                                try:
                                    # 先尝试解析为InputSchema
                                    message_obj = json.loads(message_json)
                                    if "payload" in message_obj:
                                        input_message = InputSchema.model_validate_json(
                                            message_json
                                        )
                                    else:
                                        input_message = (
                                            InputUnsupportedSchema.model_validate_json(
                                                message_json
                                            )
                                        )

                                    # 将消息放入队列
                                    self._msg_q.put(input_message)

                                    # 确认消息已处理
                                    app.redis_cli.xack(
                                        message_stream_name,
                                        self.CONSUMER_GROUP,
                                        message_id,
                                    )
                                except ValidationError as ve:
                                    logger.error(f"Failed to parse message: {ve}")
                                    # 确认消息已处理，即使解析失败
                                    app.redis_cli.xack(
                                        message_stream_name,
                                        self.CONSUMER_GROUP,
                                        message_id,
                                    )
                        except Exception as e:
                            logger.exception(
                                f"Error processing message {message_id}: {e}"
                            )
                            # 确认消息已处理，即使处理失败
                            app.redis_cli.xack(
                                message_stream_name, self.CONSUMER_GROUP, message_id
                            )
            except Exception as e:
                logger.error(f"Error consuming message stream: {e}")
                time.sleep(5)

    def _run(self) -> None:
        """
        消息处理的主循环方法

        持续监听消息队列，获取消息并进行相应处理：
        1. 对不支持的消息类型进行特殊处理
        2. 检查聊天机器人是否忙碌
        3. 发送思考中的提示
        4. 将消息分发给合适的聊天机器人处理
        """
        while not self._stop_event.is_set():  # 当停止事件未被触发时，持续执行
            try:
                # 从队列中获取消息，设置超时以避免无限阻塞
                input_message: Union[InputSchema, InputUnsupportedSchema] = (
                    self._msg_q.get(block=True, timeout=1)
                )
                logger.info(f"Received message: {input_message}")  # 记录收到的消息

                # 处理不支持的消息类型
                if isinstance(input_message, InputUnsupportedSchema):
                    # 向用户回复不支持的消息内容
                    self._message_sender(input_message.chatbot_type).reply_msg_to_user(
                        rsp_type="system",  # 系统消息类型
                        msg={"content": input_message.content},  # 消息内容
                        message_id=input_message.payload.message_id,  # 消息ID，用于关联回复
                    )
                    continue  # 跳过后续处理

                # 处理常规消息
                if isinstance(input_message.payload, MessageSchema):
                    # 获取处理该消息的聊天机器人实例
                    chatbot = self.chatbot_manager.get(
                        input_message,
                    )
                    if chatbot:
                        # 记录聊天机器人的忙碌状态
                        logger.info(
                            f"ChatBot <{input_message.route_id}> is busy: {chatbot.busy}"
                        )
                        if chatbot.busy:
                            # 如果聊天机器人正忙，向用户发送等待提示
                            self._message_sender(
                                input_message.chatbot_type
                            ).reply_msg_to_user(
                                "system",
                                {
                                    "content": "请等待当前回答结束后，再发送信息。",
                                    "session_id": "",
                                },
                                input_message.payload.message_id,
                            )
                            continue  # 跳过后续处理

                    # 先对用户进行友好回复，表示正在思考
                    self._message_sender(input_message.chatbot_type).reply_emoji(
                        message_id=input_message.payload.message_id,
                        emoji_type="THINKING",  # 发送思考中的表情
                    )

                # 将消息分发给聊天机器人管理器处理
                self.chatbot_manager.dispatch(
                    input_message,
                    self._settings,
                    self._message_sender(input_message.chatbot_type),
                    input_message.chatbot_type,
                )

            except queue.Empty:
                # 队列为空时（超时）继续循环
                continue
            except Exception as e:
                # 捕获并记录处理消息过程中的异常
                logger.exception(f"Error processing message: {e}")

    def start(self, max_retries: int = 5) -> None:
        """
        启动消息分发服务器

        在遇到异常时会自动重试，直到达到最大重试次数

        Args:
            max_retries: 遇到异常时的最大重试次数，默认为5次
        """
        logger.info("MessageDispatchServer started.")  # 记录服务器启动日志

        # 启动消息流消费者线程
        for stream_name in self._message_stream_name:
            logger.info(f"Starting consumer thread for stream: {stream_name}")
            consumer_thread = threading.Thread(
                target=self._consume_message_stream, args=(stream_name,), daemon=True
            )
            consumer_thread.start()
            self._consumer_threads.append(consumer_thread)

        retry_count = 0  # 重试计数器

        # 当停止事件未被触发时，持续运行
        while not self._stop_event.is_set():
            try:
                self._run()  # 执行消息处理的主循环
            except Exception as e:
                # 捕获并记录异常
                logger.error(f"MessageDispatchServer Error: {e}")
                retry_count += 1

                # 检查是否达到最大重试次数
                if retry_count >= max_retries:
                    logger.error("Max retries reached, stopping MessageDispatchServer.")
                    return  # 达到最大重试次数，停止服务

                time.sleep(1)  # 重试前等待1秒
                logger.info("MessageDispatchServer restarting...")  # 记录重启日志

        logger.info("MessageDispatchServer stopped.")  # 记录服务停止日志

    def stop(self) -> None:
        """
        停止消息分发服务器

        设置停止事件，使服务器主循环退出
        """
        logger.info("Stopping MessageDispatchServer...")
        self._stop_event.set()  # 触发停止事件

        # 等待所有消费者线程结束
        for i, thread in enumerate(self._consumer_threads):
            logger.info(f"Waiting for consumer thread {i} to stop...")
            if thread.is_alive():
                thread.join(timeout=2)  # 等待线程结束，最多等待2秒

        logger.info("All consumer threads stopped.")


def get_message_stream_list() -> List[str]:
    stream_names = []
    if app.config.app.terminal_type == "lark":
        # lark 消息流
        chatbot_types: List = list(RobotType)
        for chatbot_type in chatbot_types:
            if getattr(app.config.lark, chatbot_type.value).is_on:
                stream_names.append(get_lark_message_stream_name(chatbot_type))
    elif app.config.app.terminal_type == "console":
        # console 消息流
        stream_names.append(get_console_message_stream_name())
    else:
        raise ValueError(f"Invalid terminal type, {app.config.app.terminal_type}")
    return stream_names
