from abc import ABC, abstractmethod
from typing import List

from langchain_core.messages import AnyMessage
from langchain_core.runnables import RunnableConfig

__all__ = [
    "SessionIDGeneratorSpec",
    "WorkingMemorySpec",
]


class SessionIDGeneratorSpec(ABC):

    @abstractmethod
    def get_session_id(self) -> str:
        raise NotImplementedError

    @abstractmethod
    def generate_session_id(self) -> str:
        raise NotImplementedError


class WorkingMemorySpec(ABC):

    @abstractmethod
    def get_conversation_summary(self) -> str:
        """获取会话摘要"""
        raise NotImplementedError

    @abstractmethod
    def save_conversation_messages(
            self, messages: List[AnyMessage], config: RunnableConfig | None = None
    ) -> None:
        """保存会话消息"""
        raise NotImplementedError