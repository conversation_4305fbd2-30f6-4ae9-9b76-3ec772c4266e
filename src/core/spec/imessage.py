from abc import ABC, abstractmethod
from typing import Any, Optional, Literal, Union, List

from pydantic import BaseModel, Field

from src.core.schema.chatbot import SenderSchema
from src.core.schema.session import SessionContext

__all__ = ["IMessageSenderSpec", "MessageParserSpec", "ConvertToStringSpec"]


class IMessageSenderSpec(ABC):

    @abstractmethod
    def send_msg_to_user(self, rsp_type: str, msg: dict, user_id: str) -> None:
        """
        :rsp_tpe:  final | human | auth
        :msg:    {
                "content": str,
                "session_id": str,
            }
        :user_id: str
        """
        raise NotImplementedError()
    
    @abstractmethod
    def send_nominator_auth_to_user(self, chat_id: str, user_id: str, msg: dict) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_msg_to_group(self, rsp_type: str, msg: dict, group_id: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_stream_card_to_user(self, user_id: str, session_id: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_stream_mission_to_user(self, msg: dict, user_id: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_stream_plan_to_user(self, msg: dict, user_id: str, is_replan: bool = False) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_stream_message_to_user(self, rsp_type: Union[Literal["final", "human", "auth"], str], msg: dict,
                                    user_id: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def reply_msg_to_user(self, rsp_type: str, msg: dict, message_id: str) -> None:
        """
        rsp_tpe:  final | human | auth
        msg:    {
                "content": str,
                "session_id": str,
            }
        message_id: str
        """
        raise NotImplementedError()

    @abstractmethod
    def disable_event_action(self, action: str, message_id: str, content: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def disable_event_action_with_stream(self, user_id: str, message_id: str, actions: List[str]) -> None:
        raise NotImplementedError()

    @abstractmethod
    def get_session_context(
            self, sender_info: SenderSchema, full_info: bool = False
    ) -> SessionContext:
        raise NotImplementedError()

    @abstractmethod
    def reply_emoji(self, message_id: str, emoji_type: str) -> None:
        raise NotImplementedError()

    @abstractmethod
    def send_custom_json_card_to_user(self, json_card: dict, user_id: str):
        raise NotImplementedError()

    @abstractmethod
    def reply_custom_json_card_to_user(self, new_json_card: dict, message_id: str):
        raise NotImplementedError()

    @abstractmethod
    def update_custom_json_card_to_user(self, json_card: dict, message_id: str):
        raise NotImplementedError()


class MessageParserSpec(ABC):

    @abstractmethod
    def parse(self, *args, **kwargs) -> (Optional[str], str):
        """
        return: message_type, content
        """
        raise NotImplementedError()


class ConvertToStringSpec(BaseModel, ABC):
    type: str = Field(..., description="消息类型")
    message_id: str = Field(..., description="消息id")

    @abstractmethod
    def convert_to_string(self, *args, **kwargs) -> str:
        raise NotImplementedError()
