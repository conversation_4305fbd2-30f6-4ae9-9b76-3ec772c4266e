from abc import ABCMeta, abstractmethod


class MessageHandleSpec(metaclass=ABCMeta):
    @abstractmethod
    async def aon_message(self, *args, **kwargs) -> None:
        pass

    @abstractmethod
    async def aon_event(self, *args, **kwargs) -> None:
        pass

    @abstractmethod
    def on_event(self, *args, **kwargs) -> None:
        pass

    @abstractmethod
    def on_message(self, *args, **kwargs) -> None:
        pass


class MessageReceiverSpec(metaclass=ABCMeta):

    @abstractmethod
    def start(self, *args, **kwargs) -> None:
        pass

    @abstractmethod
    def stop(self, *args, **kwargs) -> None:
        pass

    @abstractmethod
    def run_forever(self, *args, **kwargs) -> None:
        pass
