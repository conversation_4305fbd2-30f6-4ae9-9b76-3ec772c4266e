LARK__CHAT__APP_ID=
LARK__CHAT__APP_SECRET=
LARK__CHAT__ENCRYPT_KEY=
LARK__CHAT__VERIFICATION_TOKEN=

LARK__RISK__APP_ID=
LARK__RISK__APP_SECRET=
LARK__RISK__ENCRYPT_KEY=
LARK__RISK__VERIFICATION_TOKEN=

AZURE_OPENAI_API_KEY=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

DASHSCOPE_API_KEY=

DEEPSEEK_API_KEY=

POSTGRES__PASSWORD=
MILVUS__PASSWORD=

LANGFUSE_TRACING_ENVIRONMENT=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=

CONSOLE__LOGIN_NAME=

TEST_USER_EMAIL=
TEST_USER_NAME=