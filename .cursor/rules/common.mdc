---
alwaysApply: true
---
# ops-brain 团队 Cursor Rules
# 智能运维机器人项目开发规范

## 项目概述
你正在开发一个基于 LangGraph 的智能运维机器人系统，集成了多种 AI 模型和外部服务。

## 核心技术栈
- Python 3.11+
- LangGraph (AI Agent 框架)
- FastAPI (Web 框架)
- PostgreSQL + Redis (数据存储)
- Lark/飞书 (集成平台)
- MCP (Model Context Protocol)
- Milvus (向量数据库)

## 开发环境要求
- 使用 uv 进行包管理和环境管理
- 使用 ruff 进行代码格式化和 linting
- 使用 pyright 进行类型检查
- 遵循 DDD (领域驱动设计) 架构原则

## 代码编写规范

### Python 基础规范
- 严格遵循 Python 3.11+ 语法特性
- 使用类型注解，确保 pyright 类型检查通过
- 遵循 PEP 8 代码风格，使用 ruff 进行格式化
- 行长度限制为 88 字符
- 使用双引号作为字符串引用风格
- 使用 4 空格缩进

### 项目架构规范
- 严格遵循 DDD 领域驱动设计原则
- 代码组织按照以下结构：
  - `src/core/` - 核心业务逻辑
  - `src/infra/` - 基础设施层
  - `src/api/` - API 接口层
  - `src/schedule/` - 定时任务
  - `src/helper/` - 辅助工具
- 每个模块都要有清晰的职责边界
- 依赖注入优于硬编码依赖

### LangGraph Agent 开发规范
- 所有 Agent 都应该继承或使用 LangGraph 的标准模式
- 状态管理使用 LangGraph 的状态图模式
- 工具调用遵循 MCP 标准
- 记忆管理使用 mem0ai 或项目内的记忆系统
- 所有 Agent 都要有适当的错误处理和重试机制

### API 开发规范
- 使用 FastAPI 进行 API 开发
- 所有 API 都要有适当的类型注解和文档字符串
- 使用 Pydantic 模型进行数据验证
- 遵循 RESTful API 设计原则
- 所有端点都要有适当的错误处理

### 数据库操作规范
- 使用 SQLAlchemy 进行 ORM 操作
- 所有数据库操作都要使用异步方式
- 使用 Alembic 进行数据库迁移
- Redis 操作使用 aioredis
- 向量数据库操作使用 langchain-milvus

### 错误处理和日志
- 使用 loguru 进行日志记录
- 所有异常都要有适当的处理和日志记录
- 关键操作要有详细的日志跟踪
- 使用结构化日志格式

### 测试要求
- 为所有核心功能编写单元测试
- 使用 pytest 作为测试框架
- 测试覆盖率要求 > 80%
- 集成测试要覆盖主要业务流程

## 代码质量检查
在提交代码前，确保运行以下检查：
```bash
# 类型检查
uv run pyright src

# 代码格式化
uv run ruff format src

# 代码检查和自动修复
uv run ruff check src --fix

# 运行测试
uv run pytest
```

## 特定编码约定

### 导入顺序
1. Python 标准库
2. 第三方库
3. 项目内部模块
4. 使用相对导入进行项目内部引用

### 命名约定
- 类名使用 PascalCase
- 函数和变量使用 snake_case
- 常量使用 UPPER_SNAKE_CASE
- 私有成员以单下划线开头
- 特殊方法以双下划线包围

### 文档字符串
- 所有公共类和函数都要有文档字符串
- 使用 Google 风格的文档字符串
- 包含参数类型、返回值类型和异常说明

### 异步编程
- 优先使用异步函数和异步库
- 正确使用 await 关键字
- 避免阻塞操作在异步函数中

## 安全要求
- 所有外部输入都要进行验证
- 敏感信息使用环境变量管理
- API 端点要有适当的认证和授权
- 数据库连接使用连接池

## 性能要求
- 数据库查询要考虑性能优化
- 使用缓存减少重复计算
- 异步操作要正确使用并发
- 避免 N+1 查询问题

## 团队协作
- 提交信息要清晰描述变更内容
- PR 要有详细的描述和测试说明
- 代码审查要关注架构设计和业务逻辑
- 重大变更要先讨论再实施

## 禁止事项
- 不要重新造轮子，优先使用现有的成熟解决方案
- 不要在代码中硬编码敏感信息
- 不要提交调试代码和临时文件
- 不要忽略类型检查警告
- 不要跳过代码格式化步骤

## 特殊说明
- 本项目集成了多个 AI 服务，注意 API 调用的错误处理
- Lark/飞书集成要考虑消息的异步处理
- Agent 的决策过程要有可追踪性
- 记忆系统的使用要考虑隐私和安全

当你编写代码时，始终考虑：
1. 这个代码是否符合 DDD 原则？
2. 这个实现是否已经有现成的解决方案？
3. 异常处理是否完善？
4. 类型注解是否正确？
5. 是否遵循了项目的架构模式？

遵循这些规则将帮助团队保持代码质量和一致性。 