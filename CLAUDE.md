# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**ops-brain** is an intelligent operations robot backend service built with Python 3.11+. It's a multi-agent system that processes messages from various sources (Lark/chat, console) and executes tasks using LangGraph workflows.

## Architecture

The system follows a layered architecture with these key components:

- **Perception Layer**: Message receivers for Lark, console, and other interfaces
- **Cognition Layer**: LangGraph-based agents (plan-and-execute, chat copilots, risk copilots)
- **Execution Layer**: Toolkits and action executors for task automation
- **Gateway Layer**: Message dispatcher and session management
- **Infrastructure**: Database (PostgreSQL), vector store (Milvus), cache (Redis), and web server

## Key Directories

- `src/core/` - Core business logic
  - `cognition/` - LangGraph agents and workflows
  - `perception/` - Message receivers and chatbots
  - `execution/` - Task execution and toolkits
  - `gateway/` - Message dispatching
- `src/infra/` - Infrastructure layer (clients, web, scheduler)
- `src/schedule/` - Scheduled tasks and evaluations
- `settings/` - Configuration management

## Development Commands

### Environment Setup
```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create virtual environment
uv venv --python 3.11

# Install all dependencies
uv sync

# Install only production dependencies
uv sync --group production
```

### Code Quality
```bash
# Type checking
uv run pyright src

# Format code
uv run ruff format src

# Lint and auto-fix
uv run ruff check src --fix

# Run tests
uv run pytest
```

### Running the Application
```bash
# Run main application
uv run python main.py

# Run with specific configuration
OPSBRAIN_APP__TERMINAL_TYPE=console uv run python main.py

# Run specific test
uv run pytest tests/public/test_settings.py -v
```

## Configuration

Configuration is managed through environment variables with Pydantic Settings. Key settings:

- `OPSBRAIN_APP__TERMINAL_TYPE`: "lark" or "console"
- `OPSBRAIN_LARK__CHAT__APP_ID`: Lark chat bot app ID
- `OPSBRAIN_LARK__RISK__APP_ID`: Lark risk bot app ID
- Database, Redis, and Milvus connection strings

## Key Technologies

- **LangGraph**: Workflow orchestration and agent management
- **FastAPI**: Web server for API endpoints
- **PostgreSQL**: Primary database with SQLAlchemy ORM
- **Milvus**: Vector database for embeddings
- **Redis**: Caching and session management
- **Lark API**: Integration with Feishu/Lark messaging platform
- **MCP**: Model Context Protocol for tool integration

## Testing

Tests are organized in `tests/`:
- `public/`: Public tests
- `private/`: Internal tests

Run specific test modules:
```bash
uv run pytest tests/public/lark_stream/main.py
```

## Database Migrations

The project uses Alembic for database migrations:
```bash
# Create new migration
uv run alembic revision --autogenerate -m "description"

# Apply migrations
uv run alembic upgrade head
```

## Important Files

- `main.py`: Application entry point
- `src/infra/app/__init__.py`: Application lifecycle management
- `settings/settings.py`: Configuration schema
- `src/core/cognition/agents/plan_and_execute/workflow.py`: Main task execution workflow