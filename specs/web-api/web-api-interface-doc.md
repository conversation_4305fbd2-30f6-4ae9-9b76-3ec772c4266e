# ops-brain Web API 接口文档

## 概述

ops-brain Web API 提供了完整的智能运维机器人Web服务接口，支持SSO单点登录、会话管理、流式聊天、Agent交互等功能。

**Base URL**: `https://your-domain.com/api/v2`

## 认证方式

### SSO单点登录
系统采用公司SSO单点登录，支持Cookie和Header两种认证方式：

- **Cookie认证**: `access_token` (推荐)
- **Header认证**: `Authorization: Bearer <access_token>`

### 认证流程
1. 访问需要认证的页面时，系统自动重定向到SSO登录
2. SSO登录成功后，系统设置认证Cookie
3. 后续API调用自动携带Cookie进行认证

## 1. 认证接口

### 1.1 SSO Ticket认证
**接口**: `GET /auth/sso-ticket-auth/`

**描述**: SSO系统回调接口，用于处理SSO登录票据

**参数**:
```json
{
  "ticket": "string",     // SSO登录票据
  "target": "string"      // 登录成功后跳转地址，默认为 "/"
}
```

**响应**: 重定向到目标页面，并设置认证Cookie

---

### 1.2 获取当前用户信息
**接口**: `GET /auth/user`

**描述**: 获取当前登录用户信息

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "user_id": "string",
    "username": "string",
    "email": "string",
    "role": "user|admin",
    "last_login": "2024-01-01T00:00:00Z",
    "settings": {
      "language": "zh-CN",
      "theme": "light"
    }
  }
}
```

---

### 1.3 刷新访问令牌
**接口**: `POST /auth/refresh`

**描述**: 刷新访问令牌

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "access_token": "string",
    "expires_in": 900
  }
}
```

---

### 1.4 用户登出
**接口**: `POST /auth/logout`

**描述**: 用户登出，清除会话

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "Logged out successfully"
  }
}
```

---

### 1.5 SSO单点登出
**接口**: `GET /auth/sso-logout/`

**描述**: SSO单点登出，同时登出本系统和SSO系统

**响应**: 重定向到SSO登出页面

## 2. 会话管理接口

### 2.1 创建会话
**接口**: `POST /session/create`

**描述**: 创建新的聊天会话

**请求体**:
```json
{
  "title": "string",           // 会话标题，可选，默认为"新对话"
  "agent_id": "string"       // Agent ID，可选，默认为"default_chat_agent", 这一期只走这个默认的
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success", 
  "data": {
    "session_id": "user123_chat456_chat_20240101120000",
    "title": "新对话",
    "agent_id": "default_chat_agent",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

---

### 2.2 获取会话列表
**接口**: `GET /session/list`

**描述**: 获取当前用户的会话列表

**参数**:
```json
{
  "page": 1,              // 页码，默认为1
  "limit": 20             // 每页数量，默认为20
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "sessions": [
      {
        "session_id": "user123_chat456_chat_20240101120000",
        "title": "Kubernetes故障排查",
        "agent_id": "default_chat_agent", 
        "status": "active",
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:30:00Z",
        "last_activity": "2024-01-01T12:30:00Z",
        "message_count": 15
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

---

### 2.3 获取会话详情
**接口**: `GET /session/{session_id}`

**描述**: 获取指定会话的详细信息

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "session_id": "user123_chat456_chat_20240101120000",
    "title": "Kubernetes故障排查",
    "agent_id": "default_chat_agent",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:30:00Z",
    "last_activity": "2024-01-01T12:30:00Z",
    "message_count": 15,
    "metadata": {}
  }
}
```

---

### 2.4 删除会话
**接口**: `DELETE /session/{session_id}`

**描述**: 删除指定的会话

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "Session deleted successfully"
  }
}
```

---

### 2.5 处理人工干预
**接口**: `POST /session/{session_id}/interrupt`

**描述**: 响应会话中Agent的人工干预请求

**请求体**:
```json
{
  "interrupt_id": "string",
  "decision": "approve|reject|modify",
  "modified_context": {},      // 当decision为modify时必填
  "additional_info": "string"  // 额外信息，可选
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "Decision processed successfully",
    "continue": true
  }
}
```

---

### 2.6 停止会话执行
**接口**: `POST /session/{session_id}/stop`

**描述**: 停止指定会话的Agent执行

**请求体**:
```json
{
  "reason": "string"  // 停止原因，可选
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "Session stopped successfully"
  }
}
```

---

## 3. 聊天管理接口

### 3.1 发送消息
**接口**: `POST /chat/send`

**描述**: 向指定会话发送消息

**请求体**:
```json
{
  "session_id": "string",
  "message": "string",
  "attachments": [           // 可选，附件列表
    {
      "type": "image|file",
      "url": "string",
      "name": "string"
    }
  ]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message_id": "string",
    "session_id": "string",
    "content": "string",
    "role": "user",
    "timestamp": "2024-01-01T00:00:00Z",
    "status": "sent"
  }
}
```

---

### 3.2 建立流式连接
**接口**: `GET /chat/stream?session_id={session_id}`

**描述**: 建立SSE连接，接收流式聊天响应

**参数**:
```json
{
  "session_id": "string"
}
```

**响应**: Server-Sent Events 流

**事件格式**:
```
data: {"type": "message", "data": {"content": "Hello", "timestamp": "2024-01-01T00:00:00Z"}}

data: {"type": "tool", "data": {"tool_name": "kubernetes", "status": "running"}}

data: {"type": "complete", "data": {"message_id": "string"}}
```

**事件类型**:
- `message`: 聊天消息内容
- `tool`: 工具调用状态
- `status`: 状态更新
- `error`: 错误信息
- `interrupt`: 人工干预请求
- `complete`: 响应完成

---

### 3.3 获取聊天记录
**接口**: `GET /chat/history`

**描述**: 获取指定会话的聊天记录

**参数**:
```json
{
  "page": 1,              // 页码，默认为1
  "limit": 50             // 每页数量，默认为50
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "messages": [
      {
        "message_id": "string",
        "session_id": "string",
        "role": "user|assistant",
        "content": "string",
        "attachments": [],
        "timestamp": "2024-01-01T00:00:00Z",
        "metadata": {}
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 50,
    "has_more": true
  }
}
```



## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
| 1001 | 会话不存在 |
| 1002 | 会话已过期 |
| 1003 | Agent执行错误 |
| 1004 | 工具调用失败 |

## 5. 流式事件详细说明

### 5.1 消息事件
```json
{
  "type": "message",
  "data": {
    "content": "string",
    "delta": "string",        // 增量内容
    "message_id": "string",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### 5.2 工具调用事件
```json
{
  "type": "tool",
  "data": {
    "tool_name": "string",
    "action": "start|progress|complete|error",
    "input": {},
    "output": {},
    "progress": 0.5,
    "duration": 1000,
    "error": "string"
  }
}
```

### 5.3 人工干预事件
```json
{
  "type": "interrupt",
  "data": {
    "interrupt_id": "string",
    "reason": "string",
    "context": {},
    "options": ["approve", "reject", "modify"],
    "required": true,
    "timeout": 300
  }
}
```

### 5.4 状态更新事件
```json
{
  "type": "status",
  "data": {
    "status": "thinking|planning|executing|waiting",
    "step": "string",
    "progress": 0.5,
    "estimated_time": 30
  }
}
```

## 6. 注意事项

1. **认证**: 所有API接口都需要认证，除了SSO回调接口
2. **CORS**: 支持跨域请求，需要在请求中包含 `credentials: 'include'`
3. **限流**: API有频率限制，建议合理控制请求频率
4. **会话管理**: 会话有过期时间，长时间不活跃的会话会被自动清理
5. **流式连接**: SSE连接需要正确处理断线重连
6. **错误处理**: 建议对所有API调用进行错误处理
