# ops-brain Web API 技术方案

## 项目概述

本文档详细说明了将现有的ops-brain智能运维机器人系统扩展为Web版本的完整技术方案。目标是提供一套完整的RESTful API，支持现代Agent能力，包括SSO单点登录、流式输出、人工干预、会话管理、上下文清理等功能。

## 1. 现有架构分析

### 1.1 已有技术栈
- **LangGraph**: 工作流编排和Agent管理
- **FastAPI**: Web服务器框架
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话管理
- **Milvus**: 向量数据库
- **MCP**: Model Context Protocol工具集成
- **UV**: 现代Python包管理器
- **Pyright + Ruff**: 代码质量工具

### 1.2 现有Agent能力
- **PlanAndExecuteAgent**: 规划和执行工作流
- **ChatCopilot**: 聊天助手，支持多轮对话
- **RiskCopilot**: 风险控制Agent
- **Human-in-the-loop**: 人工干预节点
- **会话管理**: SessionManager支持会话状态管理
- **任务调度**: 基于APScheduler的定时任务系统

### 1.3 现有限制
- 主要支持Lark和Console终端
- 缺乏Web API接口
- 需要集成公司SSO认证体系
- 缺乏流式输出支持
- 缺乏上下文清理机制

## 2. Web API 架构设计

### 2.1 技术栈选择
- **认证**: 公司SSO + Cookie Session
- **API**: FastAPI (已有)
- **流式**: Server-Sent Events (SSE)
- **数据库**: PostgreSQL (已有)
- **缓存**: Redis (已有)
- **序列化**: Pydantic DTOs
- **包管理**: UV (已有)

### 2.2 API结构设计

```
src/api/v2/
├── auth/           # 认证模块
│   ├── sso.py      # SSO认证接口
│   ├── logout.py   # 登出接口
│   └── user.py     # 用户信息接口
├── session/        # 会话管理
│   ├── create.py   # 创建会话
│   ├── list.py     # 会话列表
│   ├── get.py      # 获取会话详情
│   ├── delete.py   # 删除会话
│   ├── interrupt.py # 人工干预处理
│   └── stop.py     # 停止会话执行
├── chat/           # 聊天接口
│   ├── send.py     # 发送消息
│   ├── stream.py   # 流式响应
│   └── history.py  # 历史消息
└── user/           # 用户管理
    └── profile.py  # 用户信息
```

## 3. SSO认证与会话管理方案

### 3.1 SSO认证体系

#### 认证流程
1. 用户访问需要认证的页面 -> 重定向到SSO登录
2. SSO登录成功 -> 回调 `/auth/sso-ticket-auth/` 接口
3. 验证ticket -> 解密用户信息 -> 设置认证Cookie
4. 后续API调用自动携带Cookie进行认证

#### SSO集成实现
```python
@app.get("/auth/sso-ticket-auth/")
async def sso_ticket_auth(
    ticket: str,
    target: str = "/"
):
    # 1. 验证ticket
    sso_config = load_sso_config()
    validation_result = await validate_ticket(sso_config.validate_api, ticket)
    
    # 2. 解密用户信息
    user_info = decrypt_user_info(validation_result.ciphertext, sso_config.private_key)
    
    # 3. 创建或更新用户
    user = await get_or_create_user(user_info.username)
    
    # 4. 生成访问令牌
    access_token = create_access_token(user)
    
    # 5. 设置Cookie并重定向
    response = RedirectResponse(url=target, status_code=302)
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=True,
        samesite="lax"
    )
    return response
```

### 3.2 会话管理

#### 会话ID格式（保持现有格式）
```
{user_id}_{chat_id}_{chat_type}_{generated_id}
```

#### 会话状态存储
- **活跃会话**: Redis存储，支持快速访问
- **持久化会话**: PostgreSQL存储，支持历史查询
- **会话上下文**: 支持灵活的清理策略

#### 并发控制
- 每个用户支持多个并发会话
- 会话之间相互隔离
- 支持会话状态查询和管理
- 支持上下文大小限制和自动清理

## 4. 上下文管理方案

### 4.1 上下文清理策略

#### 清理类型
- **history**: 聊天历史消息
- **memory**: 工作记忆和短期记忆
- **tasks**: 任务状态和执行历史
- **all**: 全部上下文信息

#### 清理接口设计
```python
@app.post("/api/v2/session/{session_id}/clear-context")
async def clear_context(
    session_id: str,
    request: ClearContextRequest,
    current_user: User = Depends(get_current_user)
):
    session_manager = get_session_manager(session_id, current_user)
    
    result = await session_manager.clear_context(
        clear_type=request.clear_type,
        keep_recent=request.keep_recent,
        clear_before=request.clear_before
    )
    
    return {"code": 0, "data": result}
```

### 4.2 智能清理建议
- 基于上下文大小自动建议清理
- 支持保留重要消息和任务
- 提供清理效果预估

## 5. 流式输出方案

### 5.1 Server-Sent Events (SSE)

#### 流式接口设计
```python
@app.get("/api/v2/chat/{session_id}/stream")
async def stream_chat(
    session_id: str,
    message: str,
    current_user: User = Depends(get_current_user)
):
    async def event_generator():
        session_manager = get_session_manager(session_id, current_user)
        
        async for chunk in session_manager.astream_chat(message):
            yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        
        yield "data: [DONE]\n\n"
    
    return EventSourceResponse(event_generator())
```

#### 流式数据格式
```json
{
  "type": "message|tool|status|error|complete|interrupt",
  "data": {
    "content": "string",
    "timestamp": "datetime",
    "session_id": "string",
    "message_id": "string",
    "metadata": {}
  }
}
```

### 5.2 消息类型定义

#### 基础消息类型
- `message`: AI助手回复消息
- `tool`: 工具调用和结果
- `status`: Agent状态更新
- `error`: 错误信息
- `complete`: 对话完成
- `interrupt`: 人工干预请求

#### 人工干预消息格式
```json
{
  "type": "interrupt",
  "data": {
    "interrupt_id": "string",
    "reason": "需要用户确认风险操作",
    "context": {
      "operation": "restart_pod",
      "target": "production-app-pod-123"
    },
    "options": ["approve", "reject", "modify"],
    "timeout": 300
  }
}
```

## 6. Human-in-the-Loop 方案

### 6.1 人工干预流程

#### 干预触发场景
1. 高风险操作需要确认（如生产环境重启）
2. 缺少必要参数需要用户补充
3. 多个选择需要用户决策
4. 异常情况需要人工判断

#### 干预处理接口
```python
@app.post("/api/v2/session/{session_id}/interrupt")
async def handle_interrupt(
    session_id: str,
    interrupt_id: str,
    decision: InterruptDecision,
    current_user: User = Depends(get_current_user)
):
    session_manager = get_session_manager(session_id, current_user)
    
    result = await session_manager.handle_interrupt(
        interrupt_id=interrupt_id,
        decision=decision.decision,
        context=decision.modified_context,
        additional_info=decision.additional_info
    )
    
    return {"code": 0, "data": result}
```

#### 停止会话接口
```python
@app.post("/api/v2/session/{session_id}/stop")
async def stop_session(
    session_id: str,
    request: StopSessionRequest,
    current_user: User = Depends(get_current_user)
):
    session_manager = get_session_manager(session_id, current_user)
    
    result = await session_manager.stop_execution(
        reason=request.reason
    )
    
    return {"code": 0, "data": result}
```

#### 停止会话接口
```python
@app.post("/api/v2/session/{session_id}/stop")
async def stop_session(
    session_id: str,
    request: StopSessionRequest,
    current_user: User = Depends(get_current_user)
):
    session_manager = get_session_manager(session_id, current_user)
    
    result = await session_manager.stop_execution(
        reason=request.reason
    )
    
    return {"code": 0, "data": result}
```

### 6.2 干预状态管理
- 干预请求的持久化存储
- 超时处理机制
- 干预历史记录

## 7. 数据模型设计

### 7.1 用户模型
```python
class User(BaseModel):
    id: UUID
    username: str
    email: str
    role: UserRole = UserRole.USER
    created_at: datetime
    last_login: Optional[datetime]
    is_active: bool = True
```

### 7.2 会话模型
```python
class WebSession(BaseModel):
    id: str  # 使用现有的session_id格式
    user_id: UUID
    title: str
    agent_id: str = "default_chat_agent"  # 第一期固定为默认chat agent
    status: SessionStatus
    created_at: datetime
    updated_at: datetime
    last_activity: datetime
    metadata: Dict[str, Any] = {}
```

### 7.3 消息模型
```python
class ChatMessage(BaseModel):
    id: UUID
    session_id: str
    role: MessageRole  # human, assistant, system
    content: str
    attachments: List[Attachment] = []
    timestamp: datetime
    metadata: Dict[str, Any] = {}
```

### 7.4 干预模型
```python
class InterruptRequest(BaseModel):
    id: UUID
    session_id: str
    reason: str
    context: Dict[str, Any]
    options: List[str]
    status: InterruptStatus  # pending, resolved, timeout
    created_at: datetime
    resolved_at: Optional[datetime]
    decision: Optional[str]
    timeout_seconds: int = 300
```

## 8. 安全性设计

### 8.1 认证安全
- 基于公司SSO的统一认证
- Cookie安全配置（HttpOnly, Secure, SameSite）
- 访问令牌定期刷新
- 登录状态超时控制

### 8.2 数据安全
- 敏感信息脱敏处理
- SQL注入防护
- XSS防护
- CSRF防护
- 输入验证和清理

### 8.3 访问控制
- 基于角色的访问控制（RBAC）
- 会话权限验证
- 操作审计日志
- 敏感操作二次确认

## 9. 性能优化

### 9.1 缓存策略
- Redis缓存用户会话信息
- 缓存Agent状态和工具列表
- 缓存常用查询结果
- 上下文大小监控和自动清理

### 9.2 数据库优化
- 索引优化（用户ID、会话ID、时间戳）
- 查询优化和分页
- 连接池管理
- 读写分离支持

### 9.3 并发控制
- 异步处理和协程优化
- 请求限流和熔断
- 超时控制和重试机制
- 资源池管理

## 10. 监控和日志

### 10.1 监控指标
- API响应时间和吞吐量
- 错误率和成功率
- 并发用户数和会话数
- Agent执行状态和工具调用
- 上下文大小和清理频率

### 10.2 日志系统
- 结构化日志记录
- 请求链路追踪
- 错误报警和通知
- 性能分析和优化建议

## 11. 实施计划

### 第一阶段：基础架构（2周）
**目标**: 建立基础的Web API框架和SSO认证

#### 任务列表
1. **SSO认证集成**（5天）
   - [ ] 实现SSO ticket验证接口
   - [ ] 集成用户信息解密逻辑
   - [ ] 实现Cookie认证中间件
   - [ ] 添加用户权限验证
   - [ ] 测试SSO登录流程

2. **基础API框架**（3天）
   - [ ] 设计API路由结构
   - [ ] 实现统一响应格式
   - [ ] 添加错误处理中间件
   - [ ] 实现请求验证和清理
   - [ ] 添加API文档生成

3. **用户管理API**（2天）
   - [ ] 实现用户信息查询接口
   - [ ] 实现用户设置管理
   - [ ] 添加用户权限检查
   - [ ] 实现登出接口

#### 验收标准
- [ ] SSO登录流程完整可用
- [ ] 用户认证和权限验证正常
- [ ] API文档自动生成
- [ ] 基础错误处理完善

### 第二阶段：会话管理（2周）
**目标**: 实现完整的会话管理功能

#### 任务列表
1. **会话CRUD接口**（4天）
   - [ ] 实现会话创建接口
   - [ ] 实现会话列表查询
   - [ ] 实现会话详情获取
   - [ ] 实现会话删除接口
   - [ ] 添加会话权限控制

2. **会话控制功能**（3天）
   - [ ] 实现人工干预处理接口
   - [ ] 实现会话停止接口
   - [ ] 集成现有SessionManager
   - [ ] 添加会话状态管理

3. **会话状态管理**（1天）
   - [ ] 实现会话活跃度监控
   - [ ] 添加会话超时处理
   - [ ] 实现状态持久化

#### 验收标准
- [ ] 会话CRUD功能完整
- [ ] 人工干预流程正常
- [ ] 会话控制功能有效
- [ ] 支持多会话并发

### 第三阶段：聊天和流式输出（2周）
**目标**: 实现聊天功能和实时流式响应

#### 任务列表
1. **基础聊天接口**（3天）
   - [ ] 实现消息发送接口
   - [ ] 实现历史消息查询
   - [ ] 集成现有Agent系统
   - [ ] 添加消息持久化

2. **流式输出实现**（5天）
   - [ ] 实现SSE流式接口
   - [ ] 设计流式数据格式
   - [ ] 集成LangGraph流式输出
   - [ ] 实现连接管理和错误处理
   - [ ] 添加流式状态监控

3. **消息类型处理**（2天）
   - [ ] 实现不同消息类型处理
   - [ ] 添加工具调用消息格式
   - [ ] 实现状态更新消息
   - [ ] 添加错误消息处理

#### 验收标准
- [ ] 聊天功能正常工作
- [ ] 流式输出稳定可靠
- [ ] 消息类型处理完整
- [ ] 连接异常处理完善

### 第四阶段：人机协同（1周）
**目标**: 完整集成Agent能力和人工干预机制

#### 任务列表

3. **人工干预实现**（4天）
   - [ ] 实现干预请求处理
   - [ ] 实现干预响应接口
   - [ ] 添加干预超时机制
   - [ ] 实现干预历史记录
   - [ ] 集成流式干预消息

#### 验收标准
- [ ] Agent集成完整可用
- [ ] 工具调用正常工作
- [ ] 人工干预流程完善
- [ ] 干预超时处理正确

### 第五阶段：调度管理和优化（1周）
**目标**: 集成调度系统和性能优化

#### 任务列表
1. **调度管理接口**（2天）
   - [ ] 实现任务列表查询
   - [ ] 实现任务删除接口
   - [ ] 添加任务执行统计
   - [ ] 实现任务状态监控

2. **性能优化**（3天）
   - [ ] 实现缓存策略
   - [ ] 优化数据库查询
   - [ ] 添加请求限流
   - [ ] 实现连接池优化
   - [ ] 添加性能监控

#### 验收标准
- [ ] 调度管理功能完整
- [ ] 性能指标达标
- [ ] 缓存策略有效
- [ ] 监控告警正常

### 第六阶段：测试和部署（1周）
**目标**: 完整测试和生产部署

#### 任务列表
1. **测试完善**（3天）
   - [ ] 单元测试覆盖率>80%
   - [ ] 集成测试完整
   - [ ] 性能测试通过
   - [ ] 安全测试验证

2. **部署准备**（2天）
   - [ ] Docker镜像构建
   - [ ] 部署脚本编写
   - [ ] 环境配置管理
   - [ ] 监控告警配置

#### 验收标准
- [ ] 所有测试通过
- [ ] 部署流程自动化
- [ ] 监控告警完善
- [ ] 文档完整更新

## 12. 风险评估和应对

### 12.1 技术风险
- **SSO集成复杂性**: 提前进行SSO对接测试
- **流式输出稳定性**: 实现完善的错误处理和重连机制
- **性能瓶颈**: 进行压力测试和性能调优

### 12.2 业务风险
- **用户体验**: 进行用户测试和反馈收集
- **功能完整性**: 分阶段验收和迭代改进
- **安全合规**: 进行安全审计和漏洞扫描

### 12.3 进度风险
- **依赖阻塞**: 并行开发和提前准备
- **技术难点**: 技术预研和原型验证
- **资源不足**: 合理分配和外部支持

## 13. 总结

本技术方案基于现有的ops-brain系统架构，充分利用已有的LangGraph Agent能力和调度系统，通过Web API的形式提供现代化的用户界面。方案特别注重：

1. **SSO集成**: 与公司现有认证体系无缝集成
2. **上下文管理**: 提供灵活的上下文清理和管理策略
3. **流式体验**: 实现实时的Agent交互体验
4. **人工干预**: 支持复杂场景下的人机协作
5. **性能优化**: 确保系统在高并发下的稳定性

通过6个阶段的实施计划，预计8周内完成完整的Web API系统开发和部署。
