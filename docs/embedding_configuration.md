# Embedding 配置指南

本文档介绍如何配置和使用不同类型的embedding服务。

## 概述

系统支持多种embedding提供者，包括：
- **Qwen3**: 自部署的Qwen3 embedding服务（默认）
- **OpenAI**: OpenAI的embedding API
- **HuggingFace**: HuggingFace的embedding模型

## 配置方式

### 1. Qwen3 Embedding（默认）

```toml
[embedding]
provider = "qwen3"
model_name = "Qwen/Qwen3-Embedding-4B"
base_url = "http://ray.ttyuyin.com:10001/ray-qwen3-emb"
dimensions = 2560
timeout = 30
batch_size = 32
```

### 2. OpenAI Embedding

```toml
[embedding]
provider = "openai"
model_name = "text-embedding-3-large"
api_key = "your-openai-api-key"
base_url = "https://api.openai.com/v1"  # 可选
dimensions = 3072
timeout = 30
batch_size = 32

# 可选：额外参数
[embedding.extra_params]
user = "your-user-id"
```

### 3. HuggingFace Embedding

```toml
[embedding]
provider = "huggingface"
model_name = "sentence-transformers/all-MiniLM-L6-v2"
dimensions = 384
timeout = 30
batch_size = 32

# 可选：额外参数
[embedding.extra_params]
device = "cuda"
normalize_embeddings = true
```

## 配置参数说明

| 参数 | 说明 | 必填 | 默认值 |
|------|------|------|--------|
| `provider` | embedding提供者 | 是 | "qwen3" |
| `model_name` | 模型名称 | 是 | - |
| `base_url` | 服务地址 | 是* | - |
| `api_key` | API密钥 | 否** | None |
| `dimensions` | embedding维度 | 是 | - |
| `timeout` | 请求超时时间（秒） | 否 | 30 |
| `batch_size` | 批处理大小 | 否 | 32 |
| `extra_params` | 额外参数 | 否 | None |

\* 对于OpenAI，`base_url`可选；对于Qwen3，必填  
\** 对于OpenAI，`api_key`必填；对于其他提供者，可选

## 代码使用

### 获取embedding客户端

```python
from src.infra.clients.embedding_factory import get_embedding_client

# 获取文档embedding客户端
doc_client = get_embedding_client(is_query=False)

# 获取查询embedding客户端（某些模型对查询和文档有不同处理）
query_client = get_embedding_client(is_query=True)

# 使用
embeddings = doc_client.embed_documents(["文档1", "文档2"])
query_embedding = query_client.embed_query("查询文本")
```

### 自定义配置

```python
from src.infra.clients.embedding_factory import EmbeddingFactory
from settings.settings import EmbeddingSettings

# 创建自定义配置
custom_config = EmbeddingSettings(
    provider="openai",
    model_name="text-embedding-3-small",
    api_key="your-api-key",
    dimensions=1536
)

# 使用自定义配置创建客户端
client = EmbeddingFactory.create_embedding(custom_config)
```

## 向量维度说明

不同的embedding模型有不同的向量维度，配置时需要正确设置：

- **Qwen3-Embedding-4B**: 2560维
- **text-embedding-3-large**: 3072维
- **text-embedding-3-small**: 1536维
- **all-MiniLM-L6-v2**: 384维

## 注意事项

1. **向量维度一致性**: 确保配置的`dimensions`与模型实际输出维度一致
2. **API密钥安全**: 对于需要API密钥的服务，请妥善保管密钥
3. **网络连接**: 确保服务地址可访问
4. **模型兼容性**: 不同模型的embedding可能不兼容，切换模型时需要重新构建向量索引

## 故障排除

### 1. 维度不匹配错误

```
MilvusException: vector dimension mismatch
```

**解决方案**: 检查配置中的`dimensions`是否与模型实际输出维度一致

### 2. 连接超时

```
requests.exceptions.Timeout
```

**解决方案**: 
- 检查`base_url`是否正确
- 增加`timeout`值
- 检查网络连接

### 3. API密钥错误

```
openai.AuthenticationError
```

**解决方案**: 检查`api_key`是否正确设置

## 示例配置文件

更多配置示例请参考：`settings/embedding_examples.toml` 