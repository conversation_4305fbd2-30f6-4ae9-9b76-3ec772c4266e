# SSO 配置指南

## 概述

本文档说明了 ops-brain 项目的 SSO（单点登录）配置文件`sso_conf.yaml`的配置方法和注意事项。

## 配置文件说明

## 配置文件结构

### 基本信息配置

```yaml
data:
  sc: "ops-brain" # 应用代码，需要向运维平台申请
  name: "ops-brain智能运维机器人" # 应用名称
  environment: "development" # 当前环境
```

### SSO 服务端点配置

根据不同环境配置相应的 SSO 服务地址：

- **测试环境**: `http://testing-yw.ttyuyin.com`
- **生产环境**: `https://yw-sso.ttyuyin.com`

### 关键配置项说明

#### 1. validate_api

- **用途**: 验证 SSO 票据的 API 地址
- **格式**: `{sso_domain}/api/sso/validate/`
- **调用方式**: POST 请求，参数为`{"ticket": "票据值"}`

#### 2. logout_api

- **用途**: 单点登出的重定向地址
- **格式**: `{sso_domain}/accounts/logout/`
- **调用方式**: GET 请求，直接重定向

#### 3. private_key

- **用途**: RSA 私钥，用于解密 SSO 返回的用户信息
- **格式**: PEM 格式的 RSA 私钥
- **获取方式**: 从运维平台获取

#### 4. salt

- **用途**: 签名验证的盐值
- **获取方式**: 从运维平台获取

## 环境配置

### 开发环境配置

```yaml
development:
  sso_domain: "http://**************"
  app_url: "http://localhost:8000"
```

### 测试环境配置

```yaml
testing:
  sso_domain: "http://testing-yw.ttyuyin.com"
  app_url: "http://testing-ops-brain.ttyuyin.com"
```

### 生产环境配置

```yaml
production:
  sso_domain: "https://yw-sso.ttyuyin.com"
  app_url: "https://ops-brain.ttyuyin.com"
```

## 安全配置

### Cookie 安全设置

```yaml
cookie:
  name: "Authorization"
  secure: true # 生产环境必须为true
  httponly: true # 防止XSS攻击
  samesite: "lax" # CSRF保护
  max_age: 86400 # 24小时过期
```

### 安全策略

```yaml
security:
  ticket_timeout: 300 # 票据5分钟超时
  replay_protection: true # 防重放攻击
  user_cache_timeout: 3600 # 用户信息缓存1小时
```

## 配置步骤

### 1. 向运维平台申请接入

需要提供以下信息：

- 应用名称：ops-brain 智能运维机器人
- 应用地址：前端访问地址
- 应用代码：ops-brain
- 认证接口：/auth/sso-ticket-auth/
- 权限管理人员：运维团队联系人
- 系统简介：基于 LangGraph 的智能运维机器人系统

### 2. 获取配置信息

从运维平台获取：

- RSA 私钥
- 签名盐值
- 应用代码(sc)

### 3. 更新配置文件

将获取的信息更新到`sso_conf.yaml`文件中：

```yaml
data:
  sc: "实际申请的应用代码"
  private_key: |
    -----BEGIN RSA PRIVATE KEY-----
    实际的RSA私钥内容
    -----END RSA PRIVATE KEY-----
  salt: "实际的盐值"
```

### 4. 环境变量配置

可以通过环境变量覆盖配置：

```bash
export SSO_ENVIRONMENT=production
export SSO_APP_URL=https://ops-brain.ttyuyin.com
```

## 使用方法

### 在代码中使用

```python
from border_secure_core.utils import YamlParser

# 加载配置
sso_config = YamlParser('./sso_conf.yaml')

# 验证票据
result = sso_config.request_validate_api(ticket=ticket)

# 解密用户信息
user_info = sso_config.decrypt(result['data']['ciphertext'])
```

## 安全注意事项

### RSA 私钥安全

- 私钥文件应设置严格的文件权限（600）
- 不要将私钥提交到版本控制系统
- 定期轮换私钥

### 配置文件安全

- 配置文件应包含敏感信息，需要妥善保管
- 生产环境配置应与开发环境分离
- 使用环境变量或密钥管理系统存储敏感配置

### 网络安全

- 确保 SSO API 使用 HTTPS 协议
- 配置适当的防火墙规则
- 定期检查 API 端点的安全性

## 注意事项

1. **私钥安全**: 私钥文件不应提交到版本控制系统
2. **环境隔离**: 不同环境使用不同的配置
3. **HTTPS**: 生产环境必须使用 HTTPS
4. **Cookie 安全**: 生产环境 Cookie 必须设置 secure=true
5. **定期更新**: 定期更新私钥和盐值

## 故障排除

### 常见问题

1. **票据验证失败**

   - 检查 validate_api 地址是否正确
   - 确认网络连通性
   - 验证私钥是否正确

2. **解密失败**

   - 检查私钥格式是否正确
   - 确认私钥与 SSO 系统匹配

3. **登出失败**
   - 检查 logout_api 地址是否正确
   - 确认重定向逻辑是否正确

### 调试方法

1. 启用详细日志：

```yaml
logging:
  level: "DEBUG"
```

2. 检查网络连接：

```bash
curl -X POST {validate_api} -d '{"ticket":"test"}'
```

3. 验证配置加载：

```python
from border_secure_core.utils import YamlParser
config = YamlParser('./sso_conf.yaml')
print(config.loaded)
```
