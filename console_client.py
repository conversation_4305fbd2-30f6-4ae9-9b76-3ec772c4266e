import asyncio
import json
import os
import signal
import time
from enum import Enum
from typing import TypeVar

import redis.asyncio as aioredis  # ← 异步 redis
from loguru import logger
from prompt_toolkit import PromptSession
from prompt_toolkit.application import run_in_terminal
from prompt_toolkit.validation import ValidationError
from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from settings import get_or_creat_settings_ins
from src.core.perception.console.schema import ConsoleMessage
from src.infra.clients.redis import get_async_redis_cli
from src.schema import consts
from prompt_toolkit.key_binding import KeyBindings

kb = KeyBindings()


@kb.add('c-c')  # Ctrl+C
def _(event):
    """
    ① 退出当前 Prompt
    ② 返回空字符串，让主循环知道要放弃本次输入
    """
    event.app.exit(result="")


# ------------------ 公共常量 ------------------
class Action(str, Enum):
    Yes = "yes"
    No = "no"


not_given = "not_given"


# ------------------ 终端打印封装 ------------------
class WindowPrompt:

    def __init__(self):
        self.console = Console()
        self.is_auth = False
        self.can_input = True

    def print_welcome(self):
        welcome_content = (
            "### 使用说明\n"
            "1. Ctrl+C 放弃输入\n"
            "2. Ctrl+D 或 exit 退出程序\n"
            "3. 授权执行请输入 yes / no"
        )
        self.console.print(Panel(Markdown(welcome_content)))

    def _print_mission(self, content: str):
        content = f"{content}"
        self.console.print(Markdown(content, style="yellow3"))

    def _print_plan(self, content: str):
        content = f"{content}"
        self.console.print(Markdown(content, style="yellow3"))

    def _print_event(self, content: str):
        content = f"## 确认执行 (yes / no)\n {content}"
        self.console.print(Panel(Markdown(content, style="bright_blue")))

    def _print_message(self, message: ConsoleMessage):
        if message.render_position == "mission":
            self._print_mission(content=message.content)
            return
        if message.render_position == "plan":
            self._print_plan(content=message.content)
            return

        if message.message_type == "event":
            self._print_event(content=message.content)
            return

        self.console.print(Panel(Markdown(f"{message.content}", style="yellow1")))

    def print_exit(self):
        self.console.print(Panel(Markdown("**See you again, bye ~~~ **"), style="bold red"))

    # 供外部立即打印
    def display(self, msg: ConsoleMessage):
        self.can_input = False
        self._print_message(msg)
        if msg.message_type == "event":
            self.is_auth = msg.event.get("action") == "auth"
            self.can_input = True
        if msg.message_type == "message":
            if msg.render_position == "main":
                self.can_input = True


class AsyncConsoleClient:
    ON_MESSAGE_CHANNEL = consts.CLIENT_CHANNEL_PREFIX
    SEND_MESSAGE_CHANNEL = consts.SERVER_CHANNEL_PREFIX

    def __init__(self):
        self.cfg = get_or_creat_settings_ins()
        self.email = f"{self.cfg.console.login_name}@52tt.com"
        self.send_ch = f"{self.SEND_MESSAGE_CHANNEL}:{self.email}"
        self.recv_ch = f"{self.ON_MESSAGE_CHANNEL}:{self.email}"

        self.redis: aioredis.Redis | None = None
        self.pubsub = None

        self.window = WindowPrompt()
        self.session = PromptSession(key_bindings=kb)
        self.new_msg_evt = asyncio.Event()

        self.current_session_id = "none"

    # ----------- Redis 连接 -----------
    async def init_redis(self):
        self.redis = await get_async_redis_cli()
        self.pubsub = self.redis.pubsub()
        await self.pubsub.subscribe(self.recv_ch)

    # ----------- 发送消息 -----------
    async def send_message(self, text: str, is_event=False):
        msg_id = str(int(time.time()))
        if is_event:
            payload = ConsoleMessage(
                content=text,
                content_type="text",
                message_type="event",
                message_id=msg_id,
                event={"action": text, "session_id": self.current_session_id},
            )
        else:
            payload = ConsoleMessage(
                content=text,
                content_type="text",
                message_type="message",
                message_id=msg_id,
            )
        await self.redis.publish(self.send_ch, payload.model_dump_json())

    # ----------- 异步监听 pubsub -----------
    async def _on_message(self):
        async for raw in self.pubsub.listen():
            if raw["type"] != "message":
                continue
            msg = ConsoleMessage(**json.loads(raw["data"]))

            if msg.message_type == "event":
                self.current_session_id = msg.event.get("session_id", not_given)

            # 即刻打印，run_in_terminal 保证不破坏输入行
            def _render():
                self.window.display(msg)

            run_in_terminal(_render)
            self.new_msg_evt.set()  # 通知主循环

    # ----------- 主交互循环 -----------
    async def interact(self):
        """
        - Ctrl+C 只放弃本次输入，绝不退出主程序
        - Ctrl+D / exit 命令正常退出
        """
        self.window.print_welcome()

        while True:
            try:  # ←★ 新增最外层 try
                # ---------- 1. 根据状态创建 prompt ----------
                if not self.window.can_input:
                    prompt_task = asyncio.create_task(self.session.prompt_async(""))
                else:
                    prompt_task = asyncio.create_task(self.session.prompt_async(">> "))

                event_task = asyncio.create_task(self.new_msg_evt.wait())

                done, _ = await asyncio.wait({prompt_task, event_task},
                                             return_when=asyncio.FIRST_COMPLETED)

                # ---------- 2. 收到新消息 ----------
                if event_task in done:
                    self.new_msg_evt.clear()
                    prompt_task.cancel()
                    await asyncio.gather(prompt_task, return_exceptions=True)
                    continue

                # ---------- 3. 用户输入完成 ----------
                event_task.cancel()
                await asyncio.gather(event_task, return_exceptions=True)

                #   3‑a 捕获 Ctrl+C / Ctrl+D 发生在 prompt_task 内部的情况
                exc = prompt_task.exception()
                if exc:
                    if isinstance(exc, KeyboardInterrupt):  # Ctrl+C
                        self.window.console.print(
                            Markdown("**检测到 Ctrl+C，已放弃本次输入。**"),
                            style="bold red"
                        )
                        continue
                    if isinstance(exc, EOFError):  # Ctrl+D
                        self.window.print_exit()
                        os._exit(0)
                    raise exc

                #   3‑b 正常获取输入
                user_input = ""
                try:
                    user_input = prompt_task.result().strip()  # ← 现在在 try 里
                except KeyboardInterrupt:  # Ctrl+C
                    self.window.console.print(
                        Markdown("**检测到 Ctrl+C，已放弃本次输入。**"), style="bold red"
                    )
                    continue  # 重新开始下一轮
                except EOFError:  # Ctrl+D
                    self.window.print_exit()
                    os._exit(0)

                if not user_input:
                    continue
                self.window.can_input = False

                if self.window.is_auth:  # 授权模式
                    if user_input not in [Action.Yes, Action.No]:
                        raise ValidationError(message="只能输入 yes 或 no")

                    await self.send_message(user_input, is_event=True)
                    self.window.is_auth = False
                    continue

                if user_input.lower() in ["exit", "bye"]:  # exit 命令
                    self.window.print_exit()
                    os._exit(0)

                await self.send_message(user_input)

            except EOFError:  # 保险处理 Ctrl+D
                self.window.print_exit()
                os._exit(0)

    # ----------- 外部启动 -----------
    async def run(self):
        await self.init_redis()
        # 并发监听 pubsub + 交互
        await asyncio.gather(self._on_message(), self.interact())


# ------------------ 启动脚本 ------------------
async def main():
    client = AsyncConsoleClient()

    # 优雅退出
    loop = asyncio.get_running_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, lambda: asyncio.create_task(client.redis.close()))

    await client.run()


if __name__ == "__main__":
    asyncio.run(main())
