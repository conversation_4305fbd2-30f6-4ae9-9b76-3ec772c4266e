from multiprocessing import get_context
from multiprocessing.context import SpawnProcess
from typing import List

from loguru import logger

from src.api import API_V2
from src.core.gateway.dispatcher import MessageDispatchServer, get_message_stream_list
from src.core.perception.chatbot import chatbot_manager
from src.core.perception.console.receiver import <PERSON>sol<PERSON><PERSON><PERSON><PERSON><PERSON>
from src.core.perception.lark.receiver import (
    LarkMessageReceiver,
)
from src.core.schema.chatbot import RobotType
from src.infra.app import app
from src.infra.clients import get_redis_cli
from src.infra.utils import get_current_env
from src.schedule import keep_scheduler

_ = keep_scheduler
_ = API_V2

lark_processes: List[SpawnProcess] = []
dispatch_server = MessageDispatchServer(
    chatbot_manager=chatbot_manager,
    message_stream_name=get_message_stream_list(),
)
console_receiver = ConsoleReceiver()


def start_lark_receiver(chatbot_type: RobotType):
    # app.redis_cli = get_redis_cli()
    LarkMessageReceiver(chatbot_type).run_forever()


if __name__ == "__main__":
    app.init()
    # 记录当前运行环境
    logger.info(f"Runtime environment: {get_current_env()}")

    # 启动时的回调
    app.add_on_start(app.scheduler.start)
    app.add_on_start(dispatch_server.start)
    # 启动 Console
    if app.config.app.terminal_type == "console":
        app.add_on_start(console_receiver.start)
    # 启动飞书消息接收器进程
    if app.config.app.terminal_type == "lark":
        chatbot_types: List = list(RobotType)
        for chatbot_type in chatbot_types:
            if getattr(app.config.lark, chatbot_type.value).is_on:
                ctx = get_context("spawn")
                p = ctx.Process(
                    target=start_lark_receiver, args=(chatbot_type,), daemon=False
                )
                p.start()
                lark_processes.append(p)

    #
    # 停止时的回调
    app.add_on_stop(app.scheduler.stop)
    app.add_on_stop(dispatch_server.stop)
    # 停止飞书
    if app.config.app.terminal_type == "lark":
        app.add_on_stop(lambda: [p.terminate() for p in lark_processes])
    # 停止Console
    if app.config.app.terminal_type == "console":
        app.add_on_stop(console_receiver.stop)

    # 启动应用
    app.launch()
