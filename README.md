# ops-brain
jarvis 机器人后端服务

# 环境
## 开发环境
### 依赖
- uv (现代Python包管理器)
- Python 3.11 及以上

### uv安装
```
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 初始化
```shell
# 安装 uv
uv venv --python 3.11

# 同步所有依赖到本地环境
uv sync

# 仅同步生产依赖
uv sync --group production

# 安装新包并自动更新 pyproject.toml
uv add package_name

# 安装开发依赖
uv add --dev package_name

# 安装特定版本的包
uv add "package_name>=1.0.0,<2.0.0"
```

### 代码质量检查
```shell
# 运行类型检查
uv run pyright src

# 运行代码格式化
uv run ruff format src

# 运行代码检查和自动修复
uv run ruff check src --fix
```

### 运行应用
```shell
# 运行主程序
uv run python main.py

# 运行特定脚本
uv run python script.py
```


## 技术栈
- **包管理**: UV (替代Poetry，更快的依赖解析和安装)
- **类型检查**: Pyright
- **代码格式化**: Ruff
- **Python版本**: 3.11+
- **镜像源**: 腾讯云PyPI镜像 (国内加速)

## 迁移说明
项目已从Poetry迁移到UV包管理器，详细的迁移文档请参考 `docs/poetry_to_uv_migration.md`