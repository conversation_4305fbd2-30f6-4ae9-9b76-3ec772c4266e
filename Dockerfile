FROM cr.ttyuyin.com/public/python:3.11

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

WORKDIR /usr/app

# 安装 uv
RUN pip install --upgrade pip -i https://mirrors.cloud.tencent.com/pypi/simple  \
    && pip install uv -i https://mirrors.cloud.tencent.com/pypi/simple

# 复制项目文件
COPY pyproject.toml ./
COPY src ./src
COPY main.py ./
COPY settings ./settings
COPY alembic ./alembic
COPY alembic.ini ./

# 使用uv安装依赖
RUN uv sync --index-url https://mirrors.cloud.tencent.com/pypi/simple

CMD ["uv", "run", "python", "-u", "main.py"]