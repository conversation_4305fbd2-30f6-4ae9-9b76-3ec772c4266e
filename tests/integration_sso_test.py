#!/usr/bin/env python3
"""
SSO集成测试脚本

测试SSO模块与主应用的集成情况
"""

import asyncio
import sys
from pathlib import Path

import httpx

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_sso_endpoints_structure():
    """测试SSO端点结构"""
    print("🧪 测试SSO端点结构...")

    try:
        # 检查SSO模块文件是否存在
        sso_files = [
            "src/api/v2/auth/__init__.py",
            "src/api/v2/auth/sso.py",
            "src/api/v2/auth/models.py",
            "src/api/v2/auth/utils.py",
            "src/api/v2/auth/middleware.py",
            "src/api/v2/auth/user.py",
        ]

        missing_files = []
        for file_path in sso_files:
            full_path = project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)

        if missing_files:
            print(f"❌ 缺少SSO模块文件: {missing_files}")
            return False

        print("✅ SSO模块文件结构完整")

        # 检查SSO端点定义
        sso_file = project_root / "src/api/v2/auth/sso.py"
        with open(sso_file, encoding="utf-8") as f:
            content = f.read()

        required_endpoints = [
            "/sso-ticket-auth/",
            "/sso-logout/",
            "/login-redirect/",
            "/health",
        ]

        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing_endpoints.append(endpoint)

        if missing_endpoints:
            print(f"❌ 缺少SSO端点: {missing_endpoints}")
            return False

        print("✅ SSO端点定义完整")
        return True

    except Exception as e:
        print(f"❌ SSO端点结构测试失败: {e}")
        return False


def test_sso_configuration():
    """测试SSO配置"""
    print("\n🧪 测试SSO配置...")

    try:
        import yaml

        config_file = project_root / "sso_conf.yaml"
        if not config_file.exists():
            print("❌ SSO配置文件不存在")
            return False

        with open(config_file, encoding="utf-8") as f:
            config = yaml.safe_load(f)

        # 检查配置结构
        if "data" not in config:
            print("❌ 配置文件缺少data节点")
            return False

        data = config["data"]
        required_fields = [
            "sc",
            "name",
            "validate_api",
            "logout_api",
            "private_key",
            "salt",
        ]

        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)

        if missing_fields:
            print(f"❌ 配置文件缺少必要字段: {missing_fields}")
            return False

        print("✅ SSO配置文件结构正确")

        # 检查API端点格式
        validate_api = data.get("validate_api", "")
        logout_api = data.get("logout_api", "")

        if not validate_api.startswith("http"):
            print("❌ validate_api格式不正确")
            return False

        if not logout_api.startswith("http"):
            print("❌ logout_api格式不正确")
            return False

        print("✅ API端点格式正确")
        return True

    except Exception as e:
        print(f"❌ SSO配置测试失败: {e}")
        return False


def test_border_secure_core_integration():
    """测试border_secure_core集成"""
    print("\n🧪 测试border_secure_core集成...")

    try:
        from border_secure_core.utils import YamlParser

        config_file = project_root / "sso_conf.yaml"
        if not config_file.exists():
            print("⚠️  SSO配置文件不存在，跳过测试")
            return True

        # 测试YamlParser初始化
        yaml_parser = YamlParser(str(config_file))

        if not yaml_parser.loaded:
            print("❌ YamlParser加载配置失败")
            return False

        print("✅ YamlParser初始化成功")

        # 检查必要的方法
        required_methods = ["request_validate_api", "decrypt"]
        for method in required_methods:
            if not hasattr(yaml_parser, method):
                print(f"❌ YamlParser缺少方法: {method}")
                return False

        print("✅ YamlParser方法完整")
        return True

    except ImportError:
        print("❌ border_secure_core库未安装")
        return False
    except Exception as e:
        print(f"❌ border_secure_core集成测试失败: {e}")
        return False


async def test_sso_api_connectivity():
    """测试SSO API连通性"""
    print("\n🧪 测试SSO API连通性...")

    try:
        import yaml

        config_file = project_root / "sso_conf.yaml"
        if not config_file.exists():
            print("⚠️  SSO配置文件不存在，跳过测试")
            return True

        with open(config_file, encoding="utf-8") as f:
            config = yaml.safe_load(f)

        validate_api = config["data"]["validate_api"]
        logout_api = config["data"]["logout_api"]

        async with httpx.AsyncClient(timeout=5.0) as client:
            # 测试validate_api连通性
            try:
                response = await client.post(validate_api, json={"ticket": "test"})
                print(f"✅ validate_api连通性正常 (状态码: {response.status_code})")
            except httpx.TimeoutException:
                print("⚠️  validate_api连接超时")
            except Exception as e:
                print(f"⚠️  validate_api连接异常: {e}")

            # 测试logout_api连通性
            try:
                response = await client.get(logout_api)
                print(f"✅ logout_api连通性正常 (状态码: {response.status_code})")
            except httpx.TimeoutException:
                print("⚠️  logout_api连接超时")
            except Exception as e:
                print(f"⚠️  logout_api连接异常: {e}")

        return True

    except Exception as e:
        print(f"❌ SSO API连通性测试失败: {e}")
        return False


def test_documentation():
    """测试文档完整性"""
    print("\n🧪 测试文档完整性...")

    try:
        doc_files = ["external/SSO接入.md", "docs/sso_configuration.md"]

        missing_docs = []
        for doc_file in doc_files:
            full_path = project_root / doc_file
            if not full_path.exists():
                missing_docs.append(doc_file)

        if missing_docs:
            print(f"❌ 缺少文档文件: {missing_docs}")
            return False

        print("✅ 文档文件完整")

        # 检查SSO配置文档内容
        config_doc = project_root / "docs/sso_configuration.md"
        with open(config_doc, encoding="utf-8") as f:
            content = f.read()

        required_sections = [
            "# SSO",  # 匹配 "# SSO 配置指南" 或 "# SSO配置指南"
            "## 配置文件说明",
            "## 环境配置",
            "## 安全注意事项",
        ]

        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)

        if missing_sections:
            print(f"❌ 配置文档缺少章节: {missing_sections}")
            return False

        print("✅ 配置文档内容完整")
        return True

    except Exception as e:
        print(f"❌ 文档完整性测试失败: {e}")
        return False


def test_dependencies():
    """测试依赖项"""
    print("\n🧪 测试依赖项...")

    try:
        # 检查必要的Python包
        required_packages = [
            "fastapi",
            "pydantic",
            "PyJWT",
            "border_secure_core",
            "httpx",
            "yaml",
        ]

        missing_packages = []
        for package in required_packages:
            try:
                if package == "yaml":
                    import yaml
                elif package == "PyJWT":
                    import jwt
                else:
                    __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            print(f"❌ 缺少必要的Python包: {missing_packages}")
            return False

        print("✅ 所有必要的Python包已安装")
        return True

    except Exception as e:
        print(f"❌ 依赖项测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始SSO集成测试...\n")

    tests = [
        ("SSO端点结构", test_sso_endpoints_structure),
        ("SSO配置", test_sso_configuration),
        ("border_secure_core集成", test_border_secure_core_integration),
        ("SSO API连通性", test_sso_api_connectivity),
        ("文档完整性", test_documentation),
        ("依赖项", test_dependencies),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 集成测试结果摘要")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1

    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 SSO集成测试全部通过！模块已准备就绪")
        return 0
    else:
        print("⚠️  部分集成测试失败，请检查配置和实现")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
