#!/usr/bin/env python3
"""
SSO配置和连通性测试脚本
用于验证SSO配置是否正确，以及与SSO服务的连通性
"""

import os
import sys
import yaml
import requests
import json
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from border_secure_core.utils import YamlParser
    BORDER_SECURE_AVAILABLE = True
except ImportError:
    BORDER_SECURE_AVAILABLE = False
    print("警告: border_secure_core库未安装，将跳过相关测试")


class SSOTester:
    """SSO测试类"""
    
    def __init__(self, config_path: str = "sso_conf.yaml"):
        self.config_path = config_path
        self.config = None
        self.yaml_parser = None
        
    def load_config(self) -> bool:
        """加载SSO配置文件"""
        try:
            if not os.path.exists(self.config_path):
                print(f"❌ 配置文件不存在: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
                
            print(f"✅ 配置文件加载成功: {self.config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def load_yaml_parser(self) -> bool:
        """加载YamlParser"""
        if not BORDER_SECURE_AVAILABLE:
            print("⚠️  跳过YamlParser测试 (border_secure_core未安装)")
            return False
            
        try:
            self.yaml_parser = YamlParser(self.config_path)
            print("✅ YamlParser加载成功")
            return True
        except Exception as e:
            print(f"❌ YamlParser加载失败: {e}")
            return False
    
    def test_config_structure(self) -> bool:
        """测试配置文件结构"""
        if not self.config:
            return False
            
        required_keys = [
            'data.sc',
            'data.name', 
            'data.validate_api',
            'data.logout_api',
            'data.private_key',
            'data.salt'
        ]
        
        missing_keys = []
        for key in required_keys:
            keys = key.split('.')
            current = self.config
            try:
                for k in keys:
                    current = current[k]
            except (KeyError, TypeError):
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 配置文件缺少必要字段: {missing_keys}")
            return False
        else:
            print("✅ 配置文件结构检查通过")
            return True
    
    def test_network_connectivity(self) -> bool:
        """测试网络连通性"""
        if not self.config:
            return False
            
        # 获取当前环境的配置
        environment = self.config.get('data', {}).get('environment', 'development')
        env_config = self.config.get('environments', {}).get(environment, {})
        
        # 测试SSO域名连通性
        sso_domain = env_config.get('sso_domain') or self.config.get('data', {}).get('sso_domain')
        if sso_domain:
            try:
                response = requests.get(sso_domain, timeout=5)
                print(f"✅ SSO域名连通性测试通过: {sso_domain} (状态码: {response.status_code})")
                return True
            except requests.exceptions.RequestException as e:
                print(f"❌ SSO域名连通性测试失败: {sso_domain} - {e}")
                return False
        else:
            print("❌ 未找到SSO域名配置")
            return False
    
    def test_validate_api_endpoint(self) -> bool:
        """测试验证API端点"""
        if not self.config:
            return False
            
        validate_api = self.config.get('data', {}).get('validate_api')
        if not validate_api:
            print("❌ 未找到validate_api配置")
            return False
        
        # 使用无效票据测试API端点是否可达
        test_data = {"ticket": "test_invalid_ticket"}
        
        try:
            response = requests.post(
                validate_api,
                json=test_data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            # 即使票据无效，API应该返回错误响应而不是连接失败
            print(f"✅ 验证API端点可达: {validate_api} (状态码: {response.status_code})")
            
            # 尝试解析响应
            try:
                response_data = response.json()
                if 'errors' in response_data:
                    print(f"   响应包含错误信息: {response_data['errors']}")
                elif 'code' in response_data:
                    print(f"   响应错误码: {response_data['code']}")
            except:
                print(f"   响应内容: {response.text[:100]}...")
                
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 验证API端点测试失败: {validate_api} - {e}")
            return False
    
    def test_private_key_format(self) -> bool:
        """测试私钥格式"""
        if not self.config:
            return False
            
        private_key = self.config.get('data', {}).get('private_key', '')
        
        if not private_key:
            print("❌ 私钥配置为空")
            return False
        
        # 检查私钥格式
        if '-----BEGIN RSA PRIVATE KEY-----' in private_key and '-----END RSA PRIVATE KEY-----' in private_key:
            print("✅ 私钥格式检查通过")
            return True
        else:
            print("❌ 私钥格式不正确，应该是PEM格式的RSA私钥")
            return False
    
    def test_yaml_parser_methods(self) -> bool:
        """测试YamlParser方法"""
        if not self.yaml_parser:
            return False
            
        try:
            # 测试配置加载
            loaded_config = self.yaml_parser.loaded
            if loaded_config:
                print("✅ YamlParser配置加载成功")
            else:
                print("❌ YamlParser配置加载失败")
                return False
            
            # 测试是否有必要的方法
            required_methods = ['request_validate_api', 'decrypt']
            for method in required_methods:
                if hasattr(self.yaml_parser, method):
                    print(f"✅ YamlParser方法存在: {method}")
                else:
                    print(f"❌ YamlParser方法缺失: {method}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ YamlParser方法测试失败: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始SSO配置测试...\n")
        
        results = {}
        
        # 1. 加载配置文件
        results['config_load'] = self.load_config()
        
        # 2. 测试配置文件结构
        if results['config_load']:
            results['config_structure'] = self.test_config_structure()
        else:
            results['config_structure'] = False
        
        # 3. 测试网络连通性
        if results['config_load']:
            results['network_connectivity'] = self.test_network_connectivity()
        else:
            results['network_connectivity'] = False
        
        # 4. 测试验证API端点
        if results['config_load']:
            results['validate_api'] = self.test_validate_api_endpoint()
        else:
            results['validate_api'] = False
        
        # 5. 测试私钥格式
        if results['config_load']:
            results['private_key_format'] = self.test_private_key_format()
        else:
            results['private_key_format'] = False
        
        # 6. 加载YamlParser
        if results['config_load']:
            results['yaml_parser_load'] = self.load_yaml_parser()
        else:
            results['yaml_parser_load'] = False
        
        # 7. 测试YamlParser方法
        if results['yaml_parser_load']:
            results['yaml_parser_methods'] = self.test_yaml_parser_methods()
        else:
            results['yaml_parser_methods'] = False
        
        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """打印测试结果摘要"""
        print("\n" + "="*50)
        print("📊 测试结果摘要")
        print("="*50)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:20} : {status}")
        
        print("-"*50)
        print(f"总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！SSO配置就绪")
        else:
            print("⚠️  部分测试失败，请检查配置")


def main():
    """主函数"""
    tester = SSOTester()
    results = tester.run_all_tests()
    tester.print_summary(results)
    
    # 返回退出码
    if all(results.values()):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
