#!/usr/bin/env python3
"""
简单的SSO功能测试脚本

独立测试SSO相关功能，不依赖项目配置系统
"""

import sys
import time
from pathlib import Path

import jwt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


# 测试数据模型
def test_sso_models():
    """测试SSO数据模型"""
    print("🧪 测试SSO数据模型...")

    try:
        # 直接测试Pydantic模型，避免导入会触发配置加载的模块
        from datetime import datetime

        from pydantic import BaseModel, Field

        # 重新定义模型进行测试
        class SSOUser(BaseModel):
            username: str = Field(..., description="用户名")
            uid: str = Field(..., description="用户ID")
            sid: str = Field(..., description="会话ID")
            timestamp: int = Field(..., description="票据生成时间戳")
            feature: str = Field(..., description="特征码")

        class UserToken(BaseModel):
            username: str = Field(..., description="用户名")
            uid: str = Field(..., description="用户ID")
            issued_at: int = Field(..., description="令牌签发时间戳")
            expires_at: int = Field(..., description="令牌过期时间戳")

        class AuthUser(BaseModel):
            username: str = Field(..., description="用户名")
            uid: str = Field(..., description="用户ID")
            is_authenticated: bool = Field(True, description="是否已认证")
            login_time: datetime | None = Field(None, description="登录时间")
            last_activity: datetime | None = Field(None, description="最后活动时间")

        # 测试SSOUser模型
        sso_user = SSOUser(
            username="testuser",
            uid="123",
            sid="456",
            timestamp=1234567890,
            feature="ab",
        )

        assert sso_user.username == "testuser"
        assert sso_user.uid == "123"
        print("✅ SSOUser模型测试通过")

        # 测试UserToken模型
        user_token = UserToken(
            username="testuser",
            uid="123",
            issued_at=1234567890,
            expires_at=1234567890 + 3600,
        )

        assert user_token.username == "testuser"
        assert user_token.uid == "123"
        print("✅ UserToken模型测试通过")

        # 测试AuthUser模型
        auth_user = AuthUser(username="testuser", uid="123", is_authenticated=True)

        assert auth_user.is_authenticated is True
        print("✅ AuthUser模型测试通过")

        return True

    except Exception as e:
        print(f"❌ SSO模型测试失败: {e}")
        return False


def test_jwt_operations():
    """测试JWT令牌操作"""
    print("\n🧪 测试JWT令牌操作...")

    try:
        # JWT配置
        JWT_SECRET_KEY = "test-secret-key"
        JWT_ALGORITHM = "HS256"

        # 创建令牌
        current_time = int(time.time())
        payload = {
            "username": "testuser",
            "uid": "123",
            "iat": current_time,
            "exp": current_time + 3600,
            "type": "access_token",
        }

        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        print(f"✅ JWT令牌创建成功: {token[:20]}...")

        # 验证令牌
        decoded_payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

        assert decoded_payload["username"] == "testuser"
        assert decoded_payload["uid"] == "123"
        assert decoded_payload["type"] == "access_token"
        print("✅ JWT令牌验证通过")

        return True

    except Exception as e:
        print(f"❌ JWT令牌操作测试失败: {e}")
        return False


def test_utility_functions():
    """测试工具函数"""
    print("\n🧪 测试工具函数...")

    try:
        # 测试状态令牌生成
        import hashlib

        def generate_state_token(user_id: str, timestamp: int, secret: str) -> str:
            data = f"{user_id}:{timestamp}:{secret}"
            return hashlib.sha256(data.encode()).hexdigest()[:16]

        user_id = "123"
        timestamp = int(time.time())
        secret = "test-secret"

        token1 = generate_state_token(user_id, timestamp, secret)
        token2 = generate_state_token(user_id, timestamp, secret)

        assert token1 == token2
        assert len(token1) == 16
        print("✅ 状态令牌生成测试通过")

        # 测试URL安全检查
        def is_safe_redirect_url(url: str, allowed_hosts: list = None) -> bool:
            if not url:
                return False

            # 相对路径被认为是安全的
            if url.startswith("/") and not url.startswith("//"):
                return True

            if not allowed_hosts:
                return False

            from urllib.parse import urlparse

            try:
                parsed = urlparse(url)
                return parsed.hostname in allowed_hosts
            except:
                return False

        assert is_safe_redirect_url("/dashboard") is True
        assert is_safe_redirect_url("//evil.com") is False
        assert is_safe_redirect_url("https://example.com", ["example.com"]) is True
        assert is_safe_redirect_url("https://evil.com", ["example.com"]) is False
        print("✅ URL安全检查测试通过")

        # 测试敏感数据遮蔽
        def mask_sensitive_data(
            data: str, mask_char: str = "*", visible_chars: int = 4
        ) -> str:
            if not data or len(data) <= visible_chars:
                return mask_char * len(data) if data else ""

            visible_part = data[:visible_chars]
            masked_part = mask_char * (len(data) - visible_chars)
            return visible_part + masked_part

        result = mask_sensitive_data("1234567890", visible_chars=4)
        assert result == "1234******"

        result = mask_sensitive_data("123", visible_chars=4)
        assert result == "***"
        print("✅ 敏感数据遮蔽测试通过")

        return True

    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False


def test_sso_config_structure():
    """测试SSO配置文件结构"""
    print("\n🧪 测试SSO配置文件结构...")

    try:
        import yaml

        config_path = project_root / "sso_conf.yaml"
        if not config_path.exists():
            print("⚠️  SSO配置文件不存在，跳过测试")
            return True

        with open(config_path, encoding="utf-8") as f:
            config = yaml.safe_load(f)

        # 检查必要的配置项
        required_keys = [
            "data.sc",
            "data.name",
            "data.validate_api",
            "data.logout_api",
            "data.private_key",
            "data.salt",
        ]

        missing_keys = []
        for key in required_keys:
            keys = key.split(".")
            current = config
            try:
                for k in keys:
                    current = current[k]
            except (KeyError, TypeError):
                missing_keys.append(key)

        if missing_keys:
            print(f"❌ 配置文件缺少必要字段: {missing_keys}")
            return False

        print("✅ SSO配置文件结构检查通过")
        return True

    except Exception as e:
        print(f"❌ SSO配置文件测试失败: {e}")
        return False


def test_border_secure_core():
    """测试border_secure_core库"""
    print("\n🧪 测试border_secure_core库...")

    try:
        from border_secure_core.utils import YamlParser

        config_path = project_root / "sso_conf.yaml"
        if not config_path.exists():
            print("⚠️  SSO配置文件不存在，跳过测试")
            return True

        # 测试YamlParser加载
        yaml_parser = YamlParser(str(config_path))
        config = yaml_parser.loaded

        if not config:
            print("❌ YamlParser加载配置失败")
            return False

        print("✅ YamlParser加载配置成功")

        # 检查必要的方法
        required_methods = ["request_validate_api", "decrypt"]
        for method in required_methods:
            if not hasattr(yaml_parser, method):
                print(f"❌ YamlParser缺少方法: {method}")
                return False

        print("✅ YamlParser方法检查通过")
        return True

    except ImportError:
        print("⚠️  border_secure_core库未安装，跳过测试")
        return True
    except Exception as e:
        print(f"❌ border_secure_core库测试失败: {e}")
        return False


def test_middleware_logic():
    """测试中间件逻辑"""
    print("\n🧪 测试中间件逻辑...")

    try:
        # 模拟中间件类
        class SimpleAuthMiddleware:
            def __init__(self, excluded_paths=None):
                self.excluded_paths = excluded_paths or [
                    "/auth/",
                    "/public/",
                    "/health",
                ]

            def is_excluded_path(self, path: str) -> bool:
                for excluded_path in self.excluded_paths:
                    if path.startswith(excluded_path):
                        return True
                return False

        middleware = SimpleAuthMiddleware()

        # 测试排除路径
        assert middleware.is_excluded_path("/auth/login") is True
        assert middleware.is_excluded_path("/public/info") is True
        assert middleware.is_excluded_path("/health") is True
        assert middleware.is_excluded_path("/private/data") is False

        print("✅ 中间件逻辑测试通过")
        return True

    except Exception as e:
        print(f"❌ 中间件逻辑测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始SSO功能测试...\n")

    tests = [
        ("SSO数据模型", test_sso_models),
        ("JWT令牌操作", test_jwt_operations),
        ("工具函数", test_utility_functions),
        ("SSO配置结构", test_sso_config_structure),
        ("border_secure_core库", test_border_secure_core),
        ("中间件逻辑", test_middleware_logic),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 测试结果摘要")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1

    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有测试通过！SSO模块功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查实现")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
