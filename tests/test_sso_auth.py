#!/usr/bin/env python3
"""
SSO认证模块测试用例

测试SSO认证、用户管理、中间件等功能
"""

import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

# 设置测试环境变量，避免加载完整配置
os.environ["OPSBRAIN_WD"] = str(Path(__file__).parent.parent)

# 导入待测试的模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.v2.auth.middleware import AuthMiddleware
from src.api.v2.auth.models import AuthUser, SSOUser, UserToken
from src.api.v2.auth.utils import (
    create_user_token,
    generate_state_token,
    get_sso_config_path,
    is_safe_redirect_url,
    mask_sensitive_data,
    verify_state_token,
    verify_user_token,
)


class TestSSOUtils:
    """测试SSO工具函数"""

    def test_create_user_token(self):
        """测试创建用户令牌"""
        sso_user = SSOUser(
            username="testuser",
            uid="123",
            sid="456",
            timestamp=1234567890,
            feature="ab",
        )

        token = create_user_token(sso_user)
        assert isinstance(token, str)
        assert len(token) > 0

    def test_verify_user_token_valid(self):
        """测试验证有效令牌"""
        sso_user = SSOUser(
            username="testuser",
            uid="123",
            sid="456",
            timestamp=1234567890,
            feature="ab",
        )

        token = create_user_token(sso_user)
        user_token = verify_user_token(token)

        assert user_token is not None
        assert user_token.username == "testuser"
        assert user_token.uid == "123"

    def test_verify_user_token_invalid(self):
        """测试验证无效令牌"""
        invalid_token = "invalid.token.here"
        user_token = verify_user_token(invalid_token)

        assert user_token is None

    def test_get_sso_config_path(self):
        """测试获取SSO配置文件路径"""
        config_path = get_sso_config_path()
        assert isinstance(config_path, str)
        assert config_path.endswith("sso_conf.yaml")

    def test_generate_state_token(self):
        """测试生成状态令牌"""
        user_id = "123"
        timestamp = 1234567890

        token = generate_state_token(user_id, timestamp)
        assert isinstance(token, str)
        assert len(token) == 16

    def test_verify_state_token_valid(self):
        """测试验证有效状态令牌"""
        user_id = "123"
        timestamp = 1234567890

        token = generate_state_token(user_id, timestamp)
        is_valid = verify_state_token(token, user_id, timestamp, max_age=3600)

        assert is_valid is True

    def test_verify_state_token_invalid(self):
        """测试验证无效状态令牌"""
        user_id = "123"
        timestamp = 1234567890
        invalid_token = "invalid_token"

        is_valid = verify_state_token(invalid_token, user_id, timestamp)
        assert is_valid is False

    def test_is_safe_redirect_url(self):
        """测试安全重定向URL检查"""
        # 相对路径应该是安全的
        assert is_safe_redirect_url("/dashboard") is True
        assert is_safe_redirect_url("/auth/login") is True

        # 协议相对路径不安全
        assert is_safe_redirect_url("//evil.com") is False

        # 绝对URL需要在允许列表中
        allowed_hosts = ["example.com", "app.example.com"]
        assert is_safe_redirect_url("https://example.com/page", allowed_hosts) is True
        assert is_safe_redirect_url("https://evil.com/page", allowed_hosts) is False

    def test_mask_sensitive_data(self):
        """测试敏感数据遮蔽"""
        # 测试正常情况
        result = mask_sensitive_data("1234567890", visible_chars=4)
        assert result == "1234******"

        # 测试短数据
        result = mask_sensitive_data("123", visible_chars=4)
        assert result == "***"

        # 测试空数据
        result = mask_sensitive_data("", visible_chars=4)
        assert result == ""


class TestSSOModels:
    """测试SSO数据模型"""

    def test_sso_user_model(self):
        """测试SSO用户模型"""
        user = SSOUser(
            username="testuser",
            uid="123",
            sid="456",
            timestamp=1234567890,
            feature="ab",
        )

        assert user.username == "testuser"
        assert user.uid == "123"
        assert user.sid == "456"
        assert user.timestamp == 1234567890
        assert user.feature == "ab"

    def test_user_token_model(self):
        """测试用户令牌模型"""
        token = UserToken(
            username="testuser",
            uid="123",
            issued_at=1234567890,
            expires_at=1234567890 + 3600,
        )

        assert token.username == "testuser"
        assert token.uid == "123"
        assert token.issued_at == 1234567890
        assert token.expires_at == 1234567890 + 3600

    def test_auth_user_model(self):
        """测试认证用户模型"""
        user = AuthUser(username="testuser", uid="123", is_authenticated=True)

        assert user.username == "testuser"
        assert user.uid == "123"
        assert user.is_authenticated is True


class TestAuthMiddleware:
    """测试认证中间件"""

    def test_auth_middleware_init(self):
        """测试认证中间件初始化"""
        middleware = AuthMiddleware(
            login_url="/custom/login", excluded_paths=["/public", "/health"]
        )

        assert middleware.login_url == "/custom/login"
        assert "/public" in middleware.excluded_paths
        assert "/health" in middleware.excluded_paths

    def test_is_excluded_path(self):
        """测试排除路径检查"""
        middleware = AuthMiddleware(excluded_paths=["/auth/", "/public/", "/health"])

        assert middleware._is_excluded_path("/auth/login") is True
        assert middleware._is_excluded_path("/public/info") is True
        assert middleware._is_excluded_path("/health") is True
        assert middleware._is_excluded_path("/private/data") is False


@pytest.fixture
def mock_request():
    """模拟FastAPI请求对象"""
    request = Mock()
    request.url.path = "/test"
    request.cookies = {}
    request.headers = {}
    request.client.host = "127.0.0.1"
    return request


@pytest.fixture
def mock_sso_config():
    """模拟SSO配置"""
    return {
        "data": {
            "sc": "test-app",
            "name": "测试应用",
            "environment": "testing",
            "sso_domain": "http://test-sso.com",
            "validate_api": "http://test-sso.com/api/sso/validate/",
            "logout_api": "http://test-sso.com/accounts/logout/",
            "private_key": "-----BEGIN RSA PRIVATE KEY-----\ntest_key\n-----END RSA PRIVATE KEY-----",
            "salt": "test_salt",
            "app_config": {
                "cookie": {
                    "name": "Authorization",
                    "domain": "localhost",
                    "path": "/",
                    "secure": False,
                    "httponly": True,
                    "samesite": "lax",
                    "max_age": 86400,
                }
            },
        }
    }


class TestSSOIntegration:
    """测试SSO集成功能"""

    @patch("src.api.v2.auth.utils.YamlParser")
    def test_sso_config_loading(self, mock_yaml_parser, mock_sso_config):
        """测试SSO配置加载"""
        # 模拟YamlParser
        mock_parser_instance = Mock()
        mock_parser_instance.loaded = mock_sso_config
        mock_yaml_parser.return_value = mock_parser_instance

        # 测试配置加载
        from src.api.v2.auth.utils import get_sso_config_path

        config_path = get_sso_config_path()

        parser = mock_yaml_parser(config_path)
        config = parser.loaded

        assert config["data"]["sc"] == "test-app"
        assert config["data"]["name"] == "测试应用"
        assert "validate_api" in config["data"]
        assert "logout_api" in config["data"]


def test_integration_workflow():
    """测试完整的认证工作流程"""
    # 1. 创建SSO用户
    sso_user = SSOUser(
        username="testuser", uid="123", sid="456", timestamp=1234567890, feature="ab"
    )

    # 2. 生成访问令牌
    token = create_user_token(sso_user)
    assert token is not None

    # 3. 验证令牌
    user_token = verify_user_token(token)
    assert user_token is not None
    assert user_token.username == "testuser"

    # 4. 创建认证用户
    auth_user = AuthUser(
        username=user_token.username, uid=user_token.uid, is_authenticated=True
    )

    assert auth_user.is_authenticated is True
    assert auth_user.username == "testuser"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
