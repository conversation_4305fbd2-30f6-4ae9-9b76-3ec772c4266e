import json
import time
from typing import Any, Optional

import lark_oapi as lark
from lark_oapi.api.cardkit.v1 import CreateCardRequest, CreateCardRequestBody, CreateCardResponse, \
    ContentCardElementRequest, ContentCardElementRequestBody, ContentCardElementResponse, SettingsCardRequest, \
    SettingsCardRequestBody, SettingsCardResponse, BatchUpdateCardRequest, BatchUpdateCardRequestBody, \
    BatchUpdateCardResponse, PatchCardElementRequest, PatchCardElementRequestBody, PatchCardElementResponse, \
    CreateCardElementRequest, CreateCardElementRequestBody, CreateCardElementResponse
from lark_oapi.api.contact.v3 import GetUserRequest, GetUserResponse
from lark_oapi.api.contact.v3.model import User
from lark_oapi.api.im.v1 import (
    CreateMessageRequest,
    CreateMessageRequestBody,
    CreateMessageResponse,
    PatchMessageRequest,
    PatchMessageRequestBody,
    PatchMessageResponse, CreateMessageReactionRequest, CreateMessageReactionRequestBody, Emoji,
    CreateMessageReactionResponse, CreateMessageReactionResponseBody, ReplyMessageRequest, ReplyMessageRequestBody,
    ReplyMessageResponse, ReplyMessageResponseBody,
)
from loguru import logger

from src.infra import clients

lark_client = clients.get_lark_client()


class LarkMessageOperator:

    def __init__(self) -> None:
        self.client = lark_client

    def send_text(self, text: str, user_id: str) -> None:
        content = {"text": text}
        self.send_msg(content, user_id)

    def send_card(
            self, user_id: str, template_id: str, template_variable: dict
    ) -> CreateMessageResponse:
        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable,
            },
        }
        return self.send_msg(content, user_id, msg_type="interactive")

    def send_msg(self, content: Any, user_id: str, msg_type: str = "text") -> CreateMessageResponse:
        if not hasattr(self.client, "im") or self.client.im is None:
            raise ValueError("Client does not have 'im' attribute or it is None")

        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("user_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .content(json.dumps(content, ensure_ascii=False, indent=4))
                .msg_type(msg_type=msg_type)
                .receive_id(user_id)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)
        #
        if response.success:
            return None
        logger.error(f"Failed to send message: {response.msg}")
        return response

    def update_card(self, message_id: str, content: Any) -> None:
        if not hasattr(self.client, "im") or self.client.im is None:
            raise ValueError("Client does not have 'im' attribute or it is None")

        request: PatchMessageRequest = (
            PatchMessageRequest.builder()
            .request_body(
                PatchMessageRequestBody.builder()
                .content(json.dumps(content, ensure_ascii=False, indent=4))
                .build()
            )
            .message_id(message_id)
            .build()
        )

        # 发起请求
        response: PatchMessageResponse = self.client.im.v1.message.patch(request)
        if response.success:
            return None
        logger.error(f"Failed to update message: {response.msg}")

    def get_user_info(self, user_id: str) -> User:
        request: GetUserRequest = (
            GetUserRequest.builder()
            .user_id_type("user_id")
            .user_id(user_id)  # type: ignore
            .build()
        )
        response: GetUserResponse = self.client.contact.v3.user.get(  # type: ignore
            request
        )

        if not response.success():
            logger.error(
                f"user.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            raise Exception

        assert response.data is not None and response.data.user is not None
        return response.data.user

    def reply_reaction(self, message_id: str, reaction_type: str) -> Optional[CreateMessageReactionResponseBody]:
        request: CreateMessageReactionRequest = CreateMessageReactionRequest.builder() \
            .message_id(message_id) \
            .request_body(CreateMessageReactionRequestBody.builder()
                          .reaction_type(Emoji.builder()
                                         .emoji_type(reaction_type)
                                         .build())
                          .build()) \
            .build()
        # 发起请求
        response: CreateMessageReactionResponse = self.client.im.v1.message_reaction.create(request)
        if not response.success():
            logger.error(
                f"client.im.v1.message_reaction.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return

        return response.data

    def reply_message(self, message_id: str, content: str) -> Optional[ReplyMessageResponseBody]:
        # 构造请求对象
        request: ReplyMessageRequest = ReplyMessageRequest.builder() \
            .message_id(message_id) \
            .request_body(ReplyMessageRequestBody.builder()
                          .content(f"{{\"text\":\"{content}\"}}")
                          .msg_type("text")
                          .build()) \
            .build()

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        if not response.success():
            logger.error(
                f"client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return

        return response.data


def create_card_instance():
    card_json = {
        "schema": "2.0",
        "config": {
            "update_multi": True,
            "streaming_mode": True,
            "streaming_config": {
                "print_step": {
                    "default": 5
                },
                "print_frequency_ms": {
                    "default": 70
                },
                "print_strategy": "fast"
            },
            "style": {
                "text_size": {
                    "normal_v2": {
                        "default": "normal",
                        "pc": "normal",
                        "mobile": "heading"
                    }
                }
            }
        },
        "body": {
            "direction": "vertical",
            "horizontal_spacing": "8px",
            "vertical_spacing": "8px",
            "horizontal_align": "left",
            "vertical_align": "top",
            "padding": "12px 12px 12px 12px",
            "elements": [
                {
                    "tag": "markdown",
                    "content": "thinking...",
                    "text_align": "left",
                    "text_size": "notation",
                    "margin": "0px 0px 0px 0px",
                    "icon": {
                        "tag": "standard_icon",
                        "token": "robot_outlined",
                        "color": "blue"
                    },
                    "element_id": "status"
                },
                {
                    "tag": "column_set",
                    "horizontal_spacing": "8px",
                    "horizontal_align": "left",
                    "columns": [
                        {
                            "tag": "column",
                            "width": "weighted",
                            "background_style": "grey-100",
                            "elements": [
                                {
                                    "tag": "markdown",
                                    "content": "等待计划",
                                    "text_align": "left",
                                    "text_size": "notation",
                                    "margin": "4px 4px 4px 8px",
                                    "icon": {
                                        "tag": "standard_icon",
                                        "token": "meeting-plan_outlined",
                                        "color": "orange"
                                    },
                                    "element_id": "plan"
                                }
                            ],
                            "padding": "0px 0px 0px 0px",
                            "direction": "vertical",
                            "horizontal_spacing": "8px",
                            "vertical_spacing": "8px",
                            "horizontal_align": "left",
                            "vertical_align": "top",
                            "margin": "0px 0px 0px 0px",
                            "weight": 1
                        }
                    ],
                    "margin": "0px 0px 0px 0px"
                },
                {
                    "tag": "markdown",
                    "content": "...",
                    "text_align": "left",
                    "text_size": "normal_v2",
                    "margin": "0px 0px 0px 4px",
                    "element_id": "content"
                },
                {
                    "tag": "hr",
                    "margin": "0px 0px 0px 0px"
                },
                {
                    "tag": "column_set",
                    "horizontal_spacing": "8px",
                    "horizontal_align": "left",
                    "columns": [
                        {
                            "tag": "column",
                            "width": "auto",
                            "elements": [
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "赞"
                                    },
                                    "type": "default",
                                    "width": "default",
                                    "size": "medium",
                                    "icon": {
                                        "tag": "standard_icon",
                                        "token": "thumbsup_outlined"
                                    },
                                    "behaviors": [
                                        {
                                            "type": "callback",
                                            "value": {
                                                "session_id": "${session_id}",
                                                "action": "like",
                                                "content": "${content}"
                                            }
                                        }
                                    ],
                                    "element_id": "like",
                                    "disabled": False,
                                },
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "终止"
                                    },
                                    "type": "default",
                                    "width": "default",
                                    "size": "medium",
                                    "disabled": False,
                                    "icon": {
                                        "tag": "standard_icon",
                                        "token": "stop_outlined"
                                    },
                                    "behaviors": [
                                        {
                                            "type": "callback",
                                            "value": {
                                                "session_id": "${session_id}",
                                                "action": "stop",
                                                "content": "${content}"
                                            }
                                        }
                                    ],
                                    "element_id": "stop"
                                }
                            ],
                            "direction": "horizontal",
                            "vertical_spacing": "8px",
                            "horizontal_align": "left",
                            "vertical_align": "top",
                            "element_id": "button_group"
                        },
                        {
                            "tag": "column",
                            "width": "weighted",
                            "elements": [],
                            "vertical_spacing": "8px",
                            "horizontal_align": "left",
                            "vertical_align": "top",
                            "weight": 1
                        },
                        {
                            "tag": "column",
                            "width": "weighted",
                            "elements": [
                                {
                                    "tag": "select_static",
                                    "placeholder": {
                                        "tag": "plain_text",
                                        "content": "错误原因"
                                    },
                                    "options": [
                                        {
                                            "text": {
                                                "tag": "plain_text",
                                                "content": "回答错误"
                                            },
                                            "value": "回答错误"
                                        },
                                        {
                                            "text": {
                                                "tag": "plain_text",
                                                "content": "内容过时"
                                            },
                                            "value": "内容过时"
                                        },
                                        {
                                            "text": {
                                                "tag": "plain_text",
                                                "content": "理解错误"
                                            },
                                            "value": "理解错误"
                                        },
                                        {
                                            "text": {
                                                "tag": "plain_text",
                                                "content": "参数错误"
                                            },
                                            "value": "参数错误"
                                        }
                                    ],
                                    "type": "default",
                                    "width": "default",
                                    "behaviors": [
                                        {
                                            "type": "callback",
                                            "value": {
                                                "session_id": "${session_id}",
                                                "action": "report",
                                                "content": "${content}"
                                            }
                                        }
                                    ],
                                    "margin": "0px 0px 0px 0px",
                                    "element_id": "wrong_reason"
                                }
                            ],
                            "vertical_spacing": "8px",
                            "horizontal_align": "left",
                            "vertical_align": "top",
                            "weight": 1
                        }
                    ],
                    "margin": "0px 0px 0px 0px"
                }
            ]
        }
    }
    request: CreateCardRequest = CreateCardRequest.builder() \
        .request_body(CreateCardRequestBody.builder()
                      .type("card_json")
                      .data(json.dumps(card_json)).build()) \
        .build()

    # 发起请求
    response: CreateCardResponse = lark_client.cardkit.v1.card.create(request)
    print(response.msg)
    print(response.data.card_id)
    return response.data.card_id


def stream_update_card(card_id: str, element_id: str):
    request: ContentCardElementRequest = ContentCardElementRequest.builder() \
        .card_id(card_id) \
        .element_id("status") \
        .request_body(ContentCardElementRequestBody.builder()
                      .content("thinking...")
                      .sequence(10000)
                      .build()) \
        .build()
    # # 发起请求
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(request)
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card_element.content failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return
    time.sleep(3)
    request: ContentCardElementRequest = ContentCardElementRequest.builder() \
        .card_id(card_id) \
        .element_id("status") \
        .request_body(ContentCardElementRequestBody.builder()
                      .content("completed")
                      .sequence(10001)
                      .build()) \
        .build()
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(request)
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card_element.content failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return

    # 构造请求对象
    content = ""
    for i in range(10002, 10010):
        time.sleep(0.3)
        content += f"{i}"
        request: ContentCardElementRequest = ContentCardElementRequest.builder() \
            .card_id(card_id) \
            .element_id(element_id) \
            .request_body(ContentCardElementRequestBody.builder()
                          .content(content)
                          .sequence(i)
                          .build()) \
            .build()

        # 发起请求
        response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(request)
        if not response.success():
            lark.logger.error(
                f"client.cardkit.v1.card_element.content failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return
        # lark.logger.info(lark.JSON.marshal(response.data, indent=4))


def add_element(card_id: str):
    # 构造请求对象
    # 构造请求对象
    request: CreateCardElementRequest = CreateCardElementRequest.builder().card_id(card_id) \
        .request_body(CreateCardElementRequestBody.builder()
                      .type("append")
                      .target_element_id("button_group")
                      .sequence(10011)
                      .elements(json.dumps(
        [
            {
                "tag": "button",
                "text": {
                    "tag": "plain_text",
                    "content": "执行"
                },
                "type": "danger",
                "width": "default",
                "size": "medium",
                "disabled": False,
                "behaviors": [
                    {
                        "type": "callback",
                        "value": {
                            "session_id": "${session_id}",
                            "action": "stop",
                            "content": "${content}"
                        }
                    }
                ],
                "element_id": "execute"
            }
        ]
    ))
                      .build()) \
        .build()

    # 发起请求
    response: CreateCardElementResponse = lark_client.cardkit.v1.card_element.create(request)

    # 构造请求对象
    request: BatchUpdateCardRequest = BatchUpdateCardRequest.builder() \
        .card_id(card_id) \
        .request_body(BatchUpdateCardRequestBody.builder()
                      .sequence(10011)
                      .actions(json.dumps(
        [
            {"action": "add_elements",
             "params": {
                 "type": "append",
                 "target_element_id": "button_group",
                 "elements": [
                     {
                         "tag": "button",
                         "text": {
                             "tag": "plain_text",
                             "content": "执行"
                         },
                         "type": "danger",
                         "width": "default",
                         "size": "medium",
                         "disabled": False,
                         "behaviors": [
                             {
                                 "type": "callback",
                                 "value": {
                                     "session_id": "${session_id}",
                                     "action": "stop",
                                     "content": "${content}"
                                 }
                             }
                         ],
                         "element_id": "execute"
                     }
                 ]
             }
             }
        ]
    ))
                      .build()) \
        .build()

    # 发起请求
    response: BatchUpdateCardResponse = lark_client.cardkit.v1.card.batch_update(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card_element.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


def delete_element(card_id: str):
    # 构造请求对象
    request: BatchUpdateCardRequest = BatchUpdateCardRequest.builder() \
        .card_id(card_id) \
        .request_body(BatchUpdateCardRequestBody.builder()
                      .sequence(10011)
                      .actions(json.dumps(
        [
            {"action": "delete_elements", "params": {"element_ids": ["button_group"]}}
        ]
    ))
                      .build()) \
        .build()

    # 发起请求
    response: BatchUpdateCardResponse = lark_client.cardkit.v1.card.batch_update(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


def update_element_behavior(card_id: str):
    # 构造请求对象
    request: BatchUpdateCardRequest = BatchUpdateCardRequest.builder() \
        .card_id(card_id) \
        .request_body(BatchUpdateCardRequestBody.builder()
                      .sequence(10011)
                      .actions(json.dumps(
        [
            {
                "action": "partial_update_element",
                "params": {
                    "element_id": "stop",
                    "partial_element": {
                        "behaviors": [
                            {
                                "type": "callback",
                                "value": {
                                    "session_id": "111111",
                                    "action": "stop",
                                    "content": "222222"
                                }
                            }
                        ],
                    }
                }
            }
        ]
    ))
                      .build()) \
        .build()

    # 发起请求
    response: BatchUpdateCardResponse = lark_client.cardkit.v1.card.batch_update(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


def batch_disable_card(card_id: str):
    # 构造请求对象
    request: BatchUpdateCardRequest = BatchUpdateCardRequest.builder() \
        .card_id(card_id) \
        .request_body(BatchUpdateCardRequestBody.builder()
                      .sequence(10011)
                      .actions(json.dumps(
        [
            {
                "action": "partial_update_element",
                "params": {
                    "element_id": "like",
                    "partial_element": {
                        "disabled": True
                    }
                }
            },
            {
                "action": "partial_update_element",
                "params": {
                    "element_id": "wrong_reason",
                    "partial_element": {
                        "disabled": True
                    }
                }
            }
        ]
    ))
                      .build()) \
        .build()

    # 发起请求
    response: BatchUpdateCardResponse = lark_client.cardkit.v1.card.batch_update(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


def disable_card(card_id: str):
    # 构造请求对象
    request: PatchCardElementRequest = PatchCardElementRequest.builder() \
        .card_id(card_id) \
        .element_id("like") \
        .request_body(PatchCardElementRequestBody.builder()
                      .partial_element(json.dumps({"disabled": True}))
                      .sequence(10011)
                      .build()) \
        .build()

    # 发起请求
    response: PatchCardElementResponse = lark_client.cardkit.v1.card_element.patch(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card_element.patch failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


def close_card(card_id: str):
    card_config = {
        "config": {
            "streaming_mode": False,
        }
    }
    # 构造请求对象
    request: SettingsCardRequest = SettingsCardRequest.builder() \
        .card_id(card_id) \
        .request_body(SettingsCardRequestBody.builder()
                      .settings(json.dumps(card_config))
                      .sequence(10012)
                      .build()) \
        .build()

    # 发起请求
    response: SettingsCardResponse = lark_client.cardkit.v1.card.settings(request)
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card.settings failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return


# {'sender': {'user_id': '166488913', 'chat_type': 'p2p', 'chat_id': 'oc_0b638cf5fc987280a90471dac40e20bf'}, 'payload': {'message_id': 'om_895d28209e06c66a4dcfef00d79938c2', 'content': [{'type': 'text', 'content': '你好'}]}, 'source': 'lark'}
user_id = "166488913"
template_id = "AAqRfBpoepIVc"

card_id = create_card_instance()
element_id = 'content'
content = {
    "type": "card",
    "data": {
        "card_id": card_id,
    }
}

resp = LarkMessageOperator().send_msg(content, user_id, "interactive")

stream_update_card(card_id=card_id, element_id=element_id)
# add_element(card_id)
# delete_element(card_id=card_id)
# batch_disable_card(card_id=card_id)
disable_card(card_id=card_id)
# update_element_behavior(card_id=card_id)
close_card(card_id=card_id)

# send hello world
# LarkMessageOperator().send_card(user_id, template_id, {"content": "你好"})
# print(resp.msg)
# print(resp.data.__dict__)
