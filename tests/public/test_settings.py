"""
Settings 配置加载单元测试

测试 settings.py 中的配置类是否能正常加载配置文件和环境变量
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from pydantic import ValidationError

from settings.common import CommonSettings, LLMModel
from settings.settings import (
    APPSettings,
    ChatBotSettings,
    ConsoleSettings,
    LarkClient,
    Mem0Settings,
    MilvusSettings,
    PostgresSettings,
    RedisSettings,
    Settings,
)


class TestSettingsConfiguration(unittest.TestCase):
    """测试配置加载功能"""

    def setUp(self):
        """测试前准备"""
        # 保存原始环境变量
        self.original_env = os.environ.copy()

        # 创建临时目录用于测试配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.toml_dir = Path(self.temp_dir) / "settings" / "toml"
        self.toml_dir.mkdir(parents=True, exist_ok=True)

        # 设置测试环境变量
        os.environ["OPSBRAIN_WD"] = self.temp_dir
        os.environ["OPSBRAIN_ENV"] = "test"

    def tearDown(self):
        """测试后清理"""
        # 恢复原始环境变量
        os.environ.clear()
        os.environ.update(self.original_env)

        # 清理临时文件
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_toml_config(self, filename: str, config_content: str):
        """创建测试用的 TOML 配置文件"""
        config_file = self.toml_dir / filename
        with open(config_file, "w", encoding="utf-8") as f:
            f.write(config_content)

    def test_app_settings_default_values(self):
        """测试 APPSettings 默认值"""
        app_settings = APPSettings()

        self.assertEqual(app_settings.host, "0.0.0.0")
        self.assertEqual(app_settings.log_level, "DEBUG")
        self.assertEqual(app_settings.thread_pool_size, 50)
        self.assertEqual(app_settings.http_port, 8000)
        self.assertEqual(app_settings.http_openapi_url, "/openapi.json")
        self.assertEqual(app_settings.http_docs_url, "/docs")
        self.assertEqual(app_settings.terminal_type, "lark")

    def test_app_settings_env_override(self):
        """测试 APPSettings 环境变量覆盖"""
        # 设置环境变量
        os.environ["OPSBRAIN_HOST"] = "127.0.0.1"
        os.environ["OPSBRAIN_LOG_LEVEL"] = "INFO"
        os.environ["OPSBRAIN_HTTP_PORT"] = "9000"
        os.environ["OPSBRAIN_TERMINAL_TYPE"] = "console"

        app_settings = APPSettings()

        self.assertEqual(app_settings.host, "127.0.0.1")
        self.assertEqual(app_settings.log_level, "INFO")
        self.assertEqual(app_settings.http_port, 9000)
        self.assertEqual(app_settings.terminal_type, "console")

    def test_chatbot_settings_default_values(self):
        """测试 ChatBotSettings 默认值"""
        chatbot_settings = ChatBotSettings()
        self.assertEqual(chatbot_settings.shortterm_memory_expired, 600)

    def test_console_settings_default_values(self):
        """测试 ConsoleSettings 默认值"""
        console_settings = ConsoleSettings()
        self.assertEqual(console_settings.chatbot_type, "chat")
        self.assertEqual(console_settings.login_name, "none")

    def test_lark_client_required_fields(self):
        """测试 LarkClient 必填字段验证"""
        with self.assertRaises(ValidationError):
            LarkClient()  # 缺少必填字段应该抛出异常

    def test_lark_client_with_valid_data(self):
        """测试 LarkClient 有效数据"""
        lark_client = LarkClient(
            app_id="test_app_id",
            app_secret="test_app_secret",
            encrypt_key="test_encrypt_key",
            verification_token="test_verification_token",
            name="test_app",
            type="chat",
        )

        self.assertEqual(lark_client.app_id, "test_app_id")
        self.assertEqual(lark_client.app_secret, "test_app_secret")
        self.assertEqual(lark_client.encrypt_key, "test_encrypt_key")
        self.assertEqual(lark_client.verification_token, "test_verification_token")
        self.assertEqual(lark_client.name, "test_app")
        self.assertEqual(lark_client.type, "chat")
        self.assertTrue(lark_client.is_on)  # 默认值

    def test_redis_settings_required_fields(self):
        """测试 RedisSettings 必填字段"""
        with self.assertRaises(ValidationError):
            RedisSettings()  # 缺少必填字段

    def test_redis_settings_with_valid_data(self):
        """测试 RedisSettings 有效数据"""
        redis_settings = RedisSettings(host="localhost", port=6379, db=0)

        self.assertEqual(redis_settings.host, "localhost")
        self.assertEqual(redis_settings.port, 6379)
        self.assertEqual(redis_settings.db, 0)
        self.assertIsNone(redis_settings.password)  # 默认值
        self.assertEqual(redis_settings.max_conn, 50)  # 默认值
        self.assertEqual(redis_settings.conn_timeout, 15)  # 默认值

    def test_postgres_settings_default_port(self):
        """测试 PostgresSettings 默认端口"""
        postgres_settings = PostgresSettings(
            user="test_user",
            password="test_password",
            host="localhost",
            database="test_db",
        )

        self.assertEqual(postgres_settings.port, 5432)  # 默认端口
        self.assertEqual(postgres_settings.min_size, 1)  # 默认值
        self.assertEqual(postgres_settings.max_size, 10)  # 默认值
        self.assertFalse(postgres_settings.debug)  # 默认值

    def test_milvus_settings_optional_fields(self):
        """测试 MilvusSettings 可选字段"""
        milvus_settings = MilvusSettings(uri="http://localhost:19530")

        self.assertEqual(milvus_settings.uri, "http://localhost:19530")
        self.assertIsNone(milvus_settings.user)
        self.assertIsNone(milvus_settings.password)
        self.assertIsNone(milvus_settings.db_name)
        self.assertEqual(milvus_settings.timeout, 20)  # 默认值

    def test_mem0_settings_default_values(self):
        """测试 Mem0Settings 默认值"""
        mem0_settings = Mem0Settings()

        self.assertTrue(mem0_settings.enable)
        self.assertEqual(mem0_settings.collection_name, "jarvis_mem0")
        self.assertEqual(mem0_settings.embedding_model_dims, 2560)
        self.assertEqual(mem0_settings.provider, "milvus")
        self.assertEqual(mem0_settings.metric_type, "COSINE")

    @patch("src.infra.utils.get_work_dir")
    @patch("src.infra.utils.get_toml_dir")
    def test_common_settings_toml_loading(self, mock_get_toml_dir, mock_get_work_dir):
        """测试 CommonSettings TOML 配置加载"""
        # Mock 返回值
        mock_get_work_dir.return_value = self.temp_dir
        mock_get_toml_dir.return_value = str(self.toml_dir)

        # 创建测试 TOML 配置
        common_toml = """
[llms.test_model]
provider = "openai"
model = "gpt-4"
temperature = 0.7
max_tokens = 2048
"""
        self.create_test_toml_config("common.toml", common_toml)

        # 测试加载
        try:
            common_settings = CommonSettings()
            self.assertIn("test_model", common_settings.llms)
            self.assertEqual(common_settings.llms["test_model"].provider, "openai")
            self.assertEqual(common_settings.llms["test_model"].model, "gpt-4")
            self.assertEqual(common_settings.llms["test_model"].temperature, 0.7)
            self.assertEqual(common_settings.llms["test_model"].max_tokens, 2048)
        except Exception as e:
            # 如果因为依赖问题无法加载，至少验证配置结构
            self.skipTest(f"Skipping TOML loading test due to dependency: {e}")


class TestSettingsIntegration(unittest.TestCase):
    """测试配置集成功能"""

    def setUp(self):
        """测试前准备"""
        self.original_env = os.environ.copy()

    def tearDown(self):
        """测试后清理"""
        os.environ.clear()
        os.environ.update(self.original_env)

    def test_environment_variable_precedence(self):
        """测试环境变量优先级"""
        # 设置环境变量
        os.environ["OPSBRAIN_HOST"] = "env-host"
        os.environ["OPSBRAIN_HTTP_PORT"] = "8080"

        app_settings = APPSettings()

        # 环境变量应该覆盖默认值
        self.assertEqual(app_settings.host, "env-host")
        self.assertEqual(app_settings.http_port, 8080)

    def test_invalid_enum_values(self):
        """测试无效枚举值"""
        os.environ["OPSBRAIN_LOG_LEVEL"] = "INVALID_LEVEL"

        with self.assertRaises(ValidationError):
            APPSettings()

    def test_invalid_type_conversion(self):
        """测试无效类型转换"""
        os.environ["OPSBRAIN_HTTP_PORT"] = "not_a_number"

        with self.assertRaises(ValidationError):
            APPSettings()


class TestLLMModel(unittest.TestCase):
    """测试 LLMModel 配置类"""

    def test_required_fields_missing(self):
        """测试必填字段缺失"""
        with self.assertRaises(ValidationError):
            LLMModel()

    def test_valid_data_with_defaults(self):
        """测试有效数据和默认值"""
        llm_model = LLMModel(provider="openai", model="gpt-4")

        self.assertEqual(llm_model.provider, "openai")
        self.assertEqual(llm_model.model, "gpt-4")
        self.assertEqual(llm_model.temperature, 0.0)
        self.assertEqual(llm_model.max_tokens, 4096)
        self.assertEqual(llm_model.external_args, {})
        self.assertEqual(llm_model.extra_body, {})

    def test_custom_values(self):
        """测试自定义值"""
        llm_model = LLMModel(
            provider="anthropic",
            model="claude-3",
            temperature=0.7,
            max_tokens=2048,
            external_args={"api_key": "test"},
            extra_body={"stream": True},
        )

        self.assertEqual(llm_model.provider, "anthropic")
        self.assertEqual(llm_model.model, "claude-3")
        self.assertEqual(llm_model.temperature, 0.7)
        self.assertEqual(llm_model.max_tokens, 2048)
        self.assertEqual(llm_model.external_args, {"api_key": "test"})
        self.assertEqual(llm_model.extra_body, {"stream": True})


class TestCompleteSettingsLoading(unittest.TestCase):
    """测试完整的 Settings 配置加载"""

    def setUp(self):
        """测试前准备"""
        self.original_env = os.environ.copy()
        # 创建临时目录用于测试配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.toml_dir = Path(self.temp_dir) / "settings" / "toml"
        self.toml_dir.mkdir(parents=True, exist_ok=True)

    def tearDown(self):
        """测试后清理"""
        os.environ.clear()
        os.environ.update(self.original_env)
        # 清理临时文件
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_minimal_test_config(self):
        """创建最小化的测试配置"""
        # 创建 common.toml
        common_toml = """
[llms.test_model]
provider = "openai"
model = "gpt-4"
"""

        # 创建 test.toml
        test_toml = """
[app]
host = "127.0.0.1"
http_port = 8888

[chatbot]
shortterm_memory_expired = 300

[console]
chatbot_type = "chat"
login_name = "test_user"

[lark.chat]
app_id = "test_chat_app_id"
app_secret = "test_chat_app_secret"
encrypt_key = "test_chat_encrypt_key"
verification_token = "test_chat_verification_token"
name = "Test Chat App"
type = "chat"

[lark.risk]
app_id = "test_risk_app_id"
app_secret = "test_risk_app_secret"
encrypt_key = "test_risk_encrypt_key"
verification_token = "test_risk_verification_token"
name = "Test Risk App"
type = "risk"

[redis]
host = "localhost"
port = 6379
db = 0

[qianxin]
base_url = "https://test.example.com"
token = "test_token"

[template]
auth_tmp_id = "test_auth_tmp_id"
interact_tmp_id = "test_interact_tmp_id"
final_tmp_id = "test_final_tmp_id"
system_tmp_id = "test_system_tmp_id"

[models]
default = "test_model"

[postgres]
user = "test_user"
password = "test_password"
host = "localhost"
database = "test_db"

[milvus]
uri = "http://localhost:19530"

[mcp]
test_key = "test_value"

[toolkits.chat]
test_toolkit = { name = "test", domain = "/test/" }

[toolkits.risk]
risk_toolkit = { name = "risk", domain = "/risk/" }

[embedding]
model = "http://localhost:8081"

[mem0]
enable = true
"""

        with open(self.toml_dir / "common.toml", "w", encoding="utf-8") as f:
            f.write(common_toml)

        with open(self.toml_dir / "test.toml", "w", encoding="utf-8") as f:
            f.write(test_toml)

    @patch("src.infra.utils.get_work_dir")
    @patch("src.infra.utils.get_toml_dir")
    def test_complete_settings_loading_with_toml(
        self, mock_get_toml_dir, mock_get_work_dir
    ):
        """测试完整的 Settings 配置加载（使用 TOML 文件）"""
        # Mock 返回值
        mock_get_work_dir.return_value = self.temp_dir
        mock_get_toml_dir.return_value = str(self.toml_dir)

        # 创建测试配置文件
        self.create_minimal_test_config()

        # 设置环境变量
        os.environ["OPSBRAIN_ENV"] = "test"

        try:
            # 尝试加载完整的 Settings
            settings = Settings()

            # 验证各个配置部分是否正确加载
            self.assertEqual(settings.app.host, "127.0.0.1")
            self.assertEqual(settings.app.http_port, 8888)
            self.assertEqual(settings.chatbot.shortterm_memory_expired, 300)
            self.assertEqual(settings.console.login_name, "test_user")
            self.assertEqual(settings.lark.chat.app_id, "test_chat_app_id")
            self.assertEqual(settings.lark.risk.app_id, "test_risk_app_id")
            self.assertEqual(settings.redis.host, "localhost")
            self.assertEqual(settings.qianxin.base_url, "https://test.example.com")
            self.assertEqual(settings.template.auth_tmp_id, "test_auth_tmp_id")
            self.assertEqual(settings.models.default, "test_model")
            self.assertEqual(settings.postgres.user, "test_user")
            self.assertEqual(settings.milvus.uri, "http://localhost:19530")
            self.assertEqual(settings.embedding.provider, "qwen3")
            self.assertTrue(settings.mem0.enable)

            # 验证 LLM 配置
            self.assertIn("test_model", settings.llms)
            self.assertEqual(settings.llms["test_model"].provider, "openai")

        except Exception as e:
            # 如果因为依赖问题无法加载完整配置，跳过测试
            self.skipTest(
                f"Skipping complete settings loading test due to dependency: {e}"
            )

    def test_settings_validation_errors(self):
        """测试 Settings 验证错误"""
        # 测试缺少必填字段时的验证错误
        with self.assertRaises(ValidationError):
            # 尝试创建没有必填字段的 Settings
            Settings(
                app=APPSettings(),
                chatbot=ChatBotSettings(),
                console=ConsoleSettings(),
                # 缺少其他必填字段...
            )


if __name__ == "__main__":
    unittest.main()
